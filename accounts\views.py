from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.contrib import messages
from django.contrib.auth import logout

# Create your views here.
def custom_login(request):
    if request.user.is_authenticated:
       # Redirige vers la page d'accueil ou la page demandée avec `next`
        next_url = request.GET.get('next', 'index')
        return redirect(next_url)  # Redirection vers la page demandée (ou index si non défini)

    if request.method == 'POST':
        username = request.POST['username']
        password = request.POST['password']
        remember_me = request.POST.get('remember_me', False)
        next_url = request.GET.get('next', '/')  # Récupérer l'URL de redirection

        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            request.session.set_expiry(604800 if remember_me else 0)  # 7 jours ou session fermée
            return redirect(next_url)  # Redirection après connexion
        else:
            messages.error(request, "Nom d'utilisateur ou mot de passe incorrect")
            return redirect('login')

        # Si la méthode n'est pas POST, on rend la page de login
    response = render(request, 'accounts/login.html')

    # Désactivation du cache pour la page de login
    response['Cache-Control'] = 'no-store, no-cache, must-revalidate, proxy-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'

    return response
 
 


 

def custom_logout(request):
    # Déconnecter l'utilisateur
    logout(request)
 # Rediriger vers la page de login après la déconnexion
    response = redirect('login')  # Redirection vers la page de login
    response.delete_cookie('sessionid')  # Effacer les cookies de session
    return response