{% extends 'base.html' %}

{% load static %} 

{% block content %}


  <div class="container mt-5">
    <form action="" method="POST" enctype="multipart/form-data">
      {% csrf_token %}
 {% if message %}
  <div class="alert alert-warning" role="alert">
    {{ message }}
  </div>
{% endif %}
      <div class="col-md-4 mb-4">
        <label for="type_maintenance" class="form-label">
          <strong>Type de la maintenance :</strong></label>
        {{ form.type_maintenance}}
      </div>
      <div class="border p-2 mb-3">
        <h5>Informations Générales</h5>
        <div class="row">
          <div class="col-md-3 mb-3">
            <label for="bion" class="form-label">Bataillon :</label>
            {{ form.bion }}
          </div>
          <div class="col-md-3 mb-3">
            <label for="cie" class="form-label">Compagnie :</label>
            {{ form.cie }}
          </div>
          <div class="col-md-3 mb-3">
            <label for="sion" class="form-label">Section :</label>
            {{ form.sion }}
          </div>
          <div class="col-md-3 mb-3">
            <label for="systeme" class="form-label">Système :</label>
            {{ form.systeme}}
          </div>
        </div>
      </div>

      <div class="border p-3 mb-3">
        <h5>Equipement :</h5>
        <div class="row">
          <div class="col-md-4 mb-3">
            <label for="categorie" class="form-label">Catégorie :</label>
            {{ form.sous_systeme }}
          </div>
          <div class="col-md-4 mb-3">
            <label for="equipement" class="form-label">Equipement :</label>
            {{ form.equipement }}
          </div>

          <div class="col-md-4 mb-3">
            <label for="equipement_en_panne" class="form-label"
              >Instance en panne :</label
            >
            <select name="equipementsystemeInstance" class="form-control">
              {% for equipement in form.equipementsystemeInstance.field.queryset %}
              <option value="{{ equipement.id }}">{{ equipement }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="col-md-4 mb-3">
            <label for="reference_msg_declaration_panne" class="form-label">Référence de panne :</label>

            {{ form.reference_declaration_panne}}
          </div>
        </div>
      </div>

      <div class="border p-3 mb-3">
        <h5>Statut :</h5>
        <div class="row">
          <div class="col-md-4 mb-4">
            <label for="statut_systeme" class="form-label"
              >Statut Système :</label
            >
            {{ form.statut_systeme}}
          </div>

          <div class="col-md-4 mb-4">
            <label for="statut_sous_systeme" class="form-label"
              >Statut Equipement :</label
            >
            {{form.statut_equipement}}
          </div>
        </div>
      </div>

      <div class="border p-3 mb-3">
        <h5>Détails :</h5>
        <div class="row">
          <div class="col-md-4 mb-4">
            <label for="technicien" class="form-label">Technicien :</label>
            {{ form.technicien}}
          </div>
          <div class="col-md-4 mb-4">
            <label for="date_maintenance" class="form-label"
              >Date de la panne :</label
            >
            {{ form.date_maintenance}}
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 mb-4">
            <label for="description" class="form-label">Description :</label>
            {{ form.description}}
          </div>
        </div>
      </div>
        <div class="border p-3 mb-3">
                <h5>Médias associés :</h5>
                <div class="mb-3">
                    <label for="media" class="form-label">Téléverser des images :</label>
                    <input type="file" name="media" multiple class="form-control">
                    <small class="form-text text-muted">Vous pouvez sélectionner plusieurs fichiers.</small>
                </div>
            </div>
      <div class="d-flex justify-content-center mb-3">
        <button type="submit" class="btn btn-primary">Valider</button>
      </div>
    </form>
  </div>
 

{% endblock %}
