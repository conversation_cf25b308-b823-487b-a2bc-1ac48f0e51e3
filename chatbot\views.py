from django.shortcuts import render
from langchain.agents import initialize_agent
 
from langchain.tools import Tool
from chatbot.tools import lister_personnel,get_missions_by_battalion, check_minimum_stock,get_installed_equipment_by_system,lister_unites_organisationnelles

from langchain_ollama import OllamaLLM
llm = OllamaLLM(model="llama3")

tools = [
    Tool.from_function(
        get_missions_by_battalion,
        name="missions_par_bataillon",
        description="Retourne les missions associées à un bataillon donné (ex: 'BATAILLON 1')."
    ),
    Tool.from_function(
        check_minimum_stock,
        name="verifier_stock_minimum",
        description="Vérifie si un équipement donné est en dessous de son stock minimum (ex: 'Routeur X')."
    ),
    Tool.from_function(
        get_installed_equipment_by_system,
        name="equipement_par_systeme",
        description="Retourne tous les équipements installés dans un système donné (ex: 'ESM-11')."
    ),
   Tool.from_function(
    lister_unites_organisationnelles,
    name="lister_unites_organisationnelles",
    description="Liste les noms des bataillons (Bion), compagnies (Cie) ou sections (Sion) selon la demande utilisateur."
),
Tool.from_function(
    lister_personnel,
    name="lister_personnel",
    description="Liste tous les techniciens ou servants enregistrés. Entrée possible : 'technicien' ou 'servant'."
)
]


agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent="zero-shot-react-description",
    verbose=True,
    handle_parsing_errors=True,
)
def chatbot(request):
    conversation_log = request.session.get("conversation_log", [])
    question = None
    reponse = None
    user_name = request.user.username if request.user.is_authenticated else "Invité"

    # Gérer le cas du replay
    replay_index = request.GET.get("replay")
    if replay_index is not None and replay_index.isdigit():
        idx = int(replay_index)
        if 0 <= idx < len(conversation_log):
            user_name, question, reponse = conversation_log[idx]

    elif request.method == "POST":
        question = request.POST.get("question", "").strip()

        try:
            resultat = agent.invoke(question)
            reponse = resultat["output"] if isinstance(resultat, dict) and "output" in resultat else str(resultat)
        except Exception as e:
            reponse = f"Erreur : {str(e)}"

        conversation_log.append((user_name, question, reponse))
        request.session["conversation_log"] = conversation_log

    return render(request, "chatbot/chatbot.html", {
        "question": question,
        "final_response": reponse,
        "user_name": user_name,
        "conversation_log": conversation_log,
    })
