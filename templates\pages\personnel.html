{% extends 'base.html' %}
{% load static %}
{% block content %}
<style>
    /* Style pour centrer les cellules du tableau */
    #datatablesSimple td,
    #datatablesSimple th {
        text-align: center !important; /* Centre horizontalement */
        vertical-align: middle !important; /* Centre verticalement */
    }
</style>
<div class="container-fluid px-4">
     <ol class="breadcrumb mb-4" style="margin-top: 10px;" >
        <li class="breadcrumb-item">Stock</a></li>
        <li class="breadcrumb-item active"><a href="{% url 'personnel' %}">Personnel</a></li>
    </ol>
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Liste du Personnel
            <!-- Bouton pour ajouter un nouveau personnel -->
            <a href="{% url 'addpersonnel' %}" class="btn btn-primary">
                <i class="fa-solid fa-plus"></i> Ajouter
            </a>
          
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Prénom</th>
                        <th>Matricule</th>
                        <th>Grade</th>
                        <th>Spécialité</th>
                        <th>Date d'Affectation</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for personnel in personnels %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ personnel.nom|default:"Non spécifié" }}</td>
                        <td>{{ personnel.prenom|default:"Non spécifié" }}</td>
                        <td>{{ personnel.matricule_formate|default:"Non spécifié" }}</td>
                        <td>{{ personnel.grade|default:"Non spécifié" }}</td>
                        <td>{{ personnel.specialite|default:"Non spécifié" }}</td>
                        <td>{{ personnel.date_affectation|default:"Non spécifié" }}</td>
                        <td class="d-flex justify-content-center gap-2">
                            <!-- Bouton pour afficher les détails -->
                            <a 
                            href="{% url 'fiche_personnel' personnel.matricule %}" class="btn btn-success btn-sm">
                                <i class="fa-solid fa-eye fa-sm"></i>
                            </a>
                            <!-- Bouton pour modifier -->
                            <a 
                                class="btn btn-secondary btn-sm" 
                                data-bs-toggle="modal" 
                                data-bs-target="#editData"
                                data-matricule="{{ personnel.matricule }}"
                                data-nom="{{ personnel.nom }}"
                                data-prenom="{{ personnel.prenom }}"
                                data-grade="{{ personnel.grade }}"
                                data-specialite="{{ personnel.specialite }}"
                                data-date-affectation="{{ personnel.date_affectation }}">
                                <i class="fa-solid fa-pencil"></i>
                            </a>
                            <!-- Bouton pour supprimer -->
                            <a class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteData"
                                data-matricule="{{ personnel.matricule }}">
                                <i class="fa-solid fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- Modal pour modifier les données -->
<div class="modal fade" id="editData" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">Modifier le Personnel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" method="POST" action="{% url 'edit_personnel' %}">
                    {% csrf_token %}
                    <input type="hidden" id="matricule" name="matricule">
                    <div class="mb-3">
                        <label for="nom" class="form-label">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" maxlength="45" required>
                    </div>
                    <div class="mb-3">
                        <label for="prenom" class="form-label">Prénom</label>
                        <input type="text" class="form-control" id="prenom" name="prenom" maxlength="45" required>
                    </div>
                    <div class="mb-3">
                        <label for="grade" class="form-label">Grade</label>
                        <input type="text" class="form-control" id="grade" name="grade" required>
                    </div>
                    <div class="mb-3">
                        <label for="date_affectation" class="form-label">Date d'Affectation</label>
                        <input type="date" class="form-control" id="date_affectation" name="date_affectation">
                    </div>
                    <div class="mb-3">
                        <label for="specialite" class="form-label">Spécialité</label>
                        <input type="text" class="form-control" id="specialite" name="specialite" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteData" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Voulez-vous vraiment supprimer cet élément ? Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <form id="deleteForm" method="POST" action="">
                    {% csrf_token %}
                    <input type="hidden" id="matricule" name="matricule">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
    // Pré-remplir le formulaire de modification lors de l'ouverture du modal
    const editModal = document.getElementById('editData');
    if (editModal) {
        editModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const matricule = button.getAttribute('data-matricule'); // Matricule
            const nom = button.getAttribute('data-nom'); // Nom
            const prenom = button.getAttribute('data-prenom'); // Prénom
            const grade = button.getAttribute('data-grade'); // Grade
            const dateAffectation = button.getAttribute('data-date-affectation'); // Date d'affectation
            const specialite = button.getAttribute('data-specialite'); // Spécialité

            // Pré-remplir les champs du formulaire
            document.getElementById('matricule').value = matricule;
            document.getElementById('nom').value = nom;
            document.getElementById('prenom').value = prenom;
            document.getElementById('grade').value = grade;
            document.getElementById('date_affectation').value = formatDate(dateAffectation);
            document.getElementById('specialite').value = specialite;
        });
    }

    // Modifier l'action du formulaire de suppression avec le matricule
    const deleteModal = document.getElementById('deleteData');
    if (deleteModal) {
        deleteModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const matricule = button.getAttribute('data-matricule');
            const deleteForm = document.getElementById('deleteForm');
            deleteForm.action = `/delete_personnel/${matricule}/`;
        });
    }

    // Fonction utilitaire pour formater une date au format yyyy-mm-dd
    function formatDate(date) {
        if (!date) return '';
        const dateObj = new Date(date);
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const day = String(dateObj.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
</script>
{% endblock %}