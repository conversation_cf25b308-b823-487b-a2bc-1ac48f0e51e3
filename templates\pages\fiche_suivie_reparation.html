{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
  /* Styles personnalisés */
  .image-equipement {
    width: 60%;
    height: auto;
    max-width: 600px;
    margin-top: 10px;
  }
  .card {
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #ffffff;
  }
  .card-header {
    background-color: #007bff;
    color: #ffffff;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    font-weight: bold;
    text-align: center;
  }
</style>

<div class="card mb-3" style="max-width: 900px; margin: 20px auto; padding: 20px;">
  <div class="card-header text-center">
    FICHE DE SUIVI DE RÉPARATION
  </div>

  <div class="fiche-container">
    <!-- Informations générales -->
    <div class="row mb-3">
      <div class="col-md-6">
        <h5 class="text-primary">Informations générales :</h5>
        <p><strong>Date de la panne :</strong> {{ equipement.date_panne|default:"Non définie" }}</p>
        <p><strong>Statut :</strong>
          <span class="badge {% if equipement.statut == 'pending' %}bg-warning{% elif equipement.statut == 'in_progress' %}bg-info{% elif equipement.statut == 'completed' %}bg-success{% elif equipement.statut == 'not_repairable' %}bg-danger{% elif equipement.statut == 'external_repair' %}bg-secondary{% endif %}">
            {% if equipement.statut == 'pending' %}
              En panne
            {% elif equipement.statut == 'in_progress' %}
              En cours de réparation
            {% elif equipement.statut == 'completed' %}
              Réparé
            {% elif equipement.statut == 'not_repairable' %}
              Non réparable
            {% elif equipement.statut == 'external_repair' %}
              Envoyé au spécialiste
            {% endif %}
          </span>
        </p>
        <p><strong>Lieu de réparation :</strong> {{ equipement.lieu_reparation|default:"Non défini" }}</p>
        <p><strong>Système concerné :</strong> {{ equipement.systeme.nom|default:"Non défini" }}</p>
        <p><strong>Équipement :</strong> {{ equipement.equipement.nom|default:"Non défini" }}</p>
      </div>
      <div class="col-md-6 text-center">
        {% if equipement_media %}
          <img src="{{ equipement_media }}" alt="Image de l'équipement" class="img-fluid rounded image-equipement img-thumbnail">
        {% else %}
          <p>Aucune image disponible</p>
        {% endif %}
      </div>
    </div>

    <hr />

    <!-- Dates importantes -->
    <div class="row mb-3">
      <!-- Styles personnalisés pour la timeline -->
<style>
  /* Le style que vous avez déjà défini reste inchangé */
  .main-timeline {
    position: relative;
  }
  .main-timeline::after {
    content: "";
    position: absolute;
    width: 6px;
    background-color: #ffffff;
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
  }
  .timeline {
    position: relative;
    background-color: inherit;
    width: 50%;
  }
  .timeline::after {
    content: "";
    position: absolute;
    width: 25px;
    height: 25px;
    right: -13px;
    background-color: #ffffff;
    border: 5px solid #f5df4d;
    top: 15px;
    border-radius: 50%;
    z-index: 1;
  }
  .left {
    padding: 0px 40px 20px 0px;
    left: 0;
  }
  .right {
    padding: 0px 0px 20px 40px;
    left: 50%;
  }
  .left::before {
    content: " ";
    position: absolute;
    top: 18px;
    z-index: 1;
    right: 30px;
    border: medium solid white;
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent white;
  }
  .right::before {
    content: " ";
    position: absolute;
    top: 18px;
    z-index: 1;
    left: 30px;
    border: medium solid white;
    border-width: 10px 10px 10px 0;
    border-color: transparent white transparent transparent;
  }
  .right::after {
    left: -12px;
  }
  @media screen and (max-width: 600px) {
    .main-timeline::after {
      left: 31px;
    }
    .timeline {
      width: 100%;
      padding-left: 70px;
      padding-right: 25px;
    }
    .timeline::before {
      left: 60px;
      border: medium solid white;
      border-width: 10px 10px 10px 0;
      border-color: transparent white transparent transparent;
    }
    .left::after,
    .right::after {
      left: 18px;
    }
    .left::before {
      right: auto;
    }
    .right {
      left: 0%;
    }
  }
  </style>
  
  <!-- Section principale -->
   <!-- Dates importantes -->
   <div class="row mb-3">
    <section style="background-color: #ffffff;">
  <div class="container py-5">
    <div class="main-timeline">
      <!-- Boucle pour générer dynamiquement les timelines -->
      {% for event in timeline %}
        <!-- Vérifier si la date est valide -->
        {% if event.date and event.date != "Non spécifiée" %}
          <div class="timeline {% cycle 'left' 'right' %}">
            <div class="card 
              {% if event.statut == 'pending' or event.statut == 'not_repairable' %}
                bg-danger
              {% elif event.type == 'panne' %}
                bg-warning
              {% else %}
                bg-success
              {% endif %}
              text-white"
            >
              <div class="accordion" id="accordion{{ forloop.counter }}">
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                    <button 
                      class="accordion-button 
                        {% if event.statut == 'pending' or event.statut == 'not_repairable' %}
                          bg-danger
                        {% elif event.type == 'panne' %}
                          bg-warning
                        {% else %}
                          bg-success
                        {% endif %}
                        text-white" 
                      type="button" 
                      data-bs-toggle="collapse" 
                      data-bs-target="#collapse{{ forloop.counter }}" 
                      aria-expanded="true" 
                      aria-controls="collapse{{ forloop.counter }}"
                    >
                      {{ event.type }} : {{ event.date }}
                    </button>
                  </h2>
                  <div 
                    id="collapse{{ forloop.counter }}" 
                    class="accordion-collapse collapse show" 
                    aria-labelledby="heading{{ forloop.counter }}" 
                    data-bs-parent="#accordion{{ forloop.counter }}"
                  >
                    <div class="accordion-body bg-light text-dark">
                      <p>{{ event.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</section>
  </div>
      
    </div>

 

 
  </div>
</div>

{% endblock %}