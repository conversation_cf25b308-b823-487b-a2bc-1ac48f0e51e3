 

{% extends 'base.html' %}
{% load static %}

{% block content %}
 

    <div class="container mt-5">
        <form action="" method="POST" enctype="multipart/form-data">
            {% csrf_token %}
     <!-- Affichage des erreurs s'il y en a -->
     {% if form.errors %}
     <div class="alert alert-danger">
       <ul>
         {% for field in form %}
           {% for error in field.errors %}
             <li>{{ field.label }}: {{ error }}</li>
           {% endfor %}
         {% endfor %}
       </ul>
     </div>
   {% endif %}
   <div class="col-md-4 mb-4">
    <label for="type_maintenance" class="form-label">Type de la maintenance : <span class="badge bg-primary">{{ equipement_en_panne.type_maintenance}} </span> </label>
    
</div>
             
              <div class="border p-3 mb-3">
 
                <h5>Informations Générales</h5> 
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="bion" class="form-label">Bataillon :</label>
                        {% if equipement_en_panne.bion %}
                       <p> {{ equipement_en_panne.bion.nom }}</p>
                    {% else %}
                        Aucun bataillon associé
                    {% endif %}
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="cie" class="form-label">Compagnie :</label>
                        <p> {{ equipement_en_panne.cie.nom }}    </p>
                           
                    </div>
                    <div class="col-md-3 mb-3">
                      <label for="sion" class="form-label">Section :</label>
                      <p> {{ equipement_en_panne.sion.nom }} </p>
                         
                  </div>
                  <div class="col-md-3 mb-3">
                    <label for="systeme" class="form-label">système :</label>
                  <p> {{  equipement_en_panne.systeme.nom  }} </p>
                       
                </div>
                </div>
            </div>
          
    
            <div class="border p-3 mb-3">
              <h5>Equipement :</h5> 
              <div class="row">
                  <div class="col-md-4 mb-3">
                      <label for="categorie" class="form-label">Catégorie :</label>
                      <p> {{ equipement_en_panne.sous_systeme.nom }} </p>
                  </div>
                  <div class="col-md-4 mb-3">
                      <label for="equipement" class="form-label">Equipement :</label>
                     <p><span class="badge bg-danger">{{  equipement_en_panne.equipement }} </span></p> 
                         
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label for="equipement_en_panne" class="form-label">Instance en panne :</label>
                    
                    <p><span class="badge bg-danger">{{  equipement_en_panne.equipementsystemeInstance.numero_serie  }}</span> </p> 
                </div>
              <div class="col-md-4 mb-3">
                <label for="reference_msg" class="form-label">Message de panne :</label>
                
                <span class="badge bg-info">{{ equipement_en_panne.reference_declaration_panne}} </span>
            </div>




            <div class="border p-3 mb-3">
              <h5>Equipement de rechange :</h5> 
              <div class="row">
                  
                   
                  
                  <div class="col-md-4 mb-3">
                    <label for="equipement_de_rechange" class="form-label">Instance en stock :</label>
                    
                     {{  form.equipement_de_rechange  }}    
                </div>


              <div class="col-md-4 mb-3">
                <label for="reference_msg" class="form-label">Ordre de sortie du stock :</label>
                
                {{ form.reference_sortie}} 
            </div>
















            <div class="col-md-4 mb-3">
              <label for="date_sortie_stock" class="form-label">Date de sortie du stock :</label>
              {{ form.date_sortie_stock }} 
                 
          </div>
        
             
              </div>
          </div>
        
          <div class="border p-3 mb-3">
            <h5>Statut :</h5> 
            <div class="row">
                <div class="col-md-4 mb-4">
                    <label for="statut_systeme" class="form-label">Statut Système :</label>
                    {{ form.statut_systeme}} 
                </div>
             
              <div class="col-md-4 mb-4">
                <label for="statut_sous_systeme" class="form-label">Statut Equipement :</label>
                {{ form.statut_equipement}}     
            </div>

            <div class="col-md-3 mb-3">
              <label for="date_réparation" class="form-label">Date de réparation:</label>
              {{ form.date_maintenance }} 
                 
          </div>
            </div>
        </div>
      
        <div class="border p-3 mb-3">
          <h5>Détails :</h5> 
          <div class="row">
              <div class="col-md-4 mb-4">
                  <label for="duree" class="form-label">Durée :</label>
                  {{ form.duree}} 
              </div>
              <div class="col-md-4 mb-4">
                <label for="technicien" class="form-label">Technicien :</label>
                {{ form.technicien}} 
            </div>
            </div>
            <div class="row">
            <div class="col-md-12 mb-4">
              <label for="description" class="form-label">Description :</label>
              {{ form.description}}     
          </div>
          </div>
      </div>
        <div class="border p-3 mb-3">
                <h5>Médias associés :</h5>
                <div class="mb-3">
                    <label for="media" class="form-label">Téléverser des images :</label>
                    <input type="file" name="media" multiple class="form-control">
                    <small class="form-text text-muted">Vous pouvez sélectionner plusieurs fichiers.</small>
                </div>
            </div>
      <div class="d-flex justify-content-center mb-3">
        <button type="submit" class="btn btn-primary">Valider</button>
    </div>
        </form>
    </div>
   




 
{% endblock %}


 
 