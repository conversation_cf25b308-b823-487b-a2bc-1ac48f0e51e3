{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>Connexion - EWMS</title>
    <link rel="icon" type="image/png" href="{% static 'img/far.png' %}">
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <style>
        body {
            background: url("{% static 'img/cuas.jpg' %}") no-repeat center center fixed;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
        }
        .login-container h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    {% if user.is_authenticated %}
        <script>window.location.href = "/index/";</script>
    {% endif %}

    <!-- Wrapper pour le centrage -->
    <div class="container-fluid d-flex justify-content-center align-items-center vh-100">
        <div class="login-container">
            <h3>Connexion</h3>
            <form method="POST" action="{% url 'login' %}">
                {% csrf_token %}
                <input type="hidden" name="next" value="{{ request.GET.next }}">
                
                <div class="mb-3">
                    <label for="inputUsername" class="form-label">Nom d'utilisateur</label>
                    <input type="text" class="form-control" id="inputUsername" name="username" placeholder="Votre identifiant" required>
                </div>
                
                <div class="mb-3">
                    <label for="inputPassword" class="form-label">Mot de passe</label>
                    <input type="password" class="form-control" id="inputPassword" name="password" placeholder="Votre mot de passe" required>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="inputRememberPassword">
                    <label class="form-check-label" for="inputRememberPassword">Se souvenir de moi</label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">Se connecter</button>
            </form>
            
            {% if messages %}
                <div class="alert alert-danger mt-3">
                    {% for message in messages %}
                        <p>{{ message }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>