from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User
from .models import Profile


# Inline pour afficher le profil directement dans l'utilisateur
class ProfileInline(admin.StackedInline):
    model = Profile
    can_delete = False
    verbose_name_plural = 'Profil'
    fk_name = 'user'

    # Affiche un message quand le profil n'existe pas encore (optionnel)
    def has_add_permission(self, request, obj):
        return False if obj is None else True

    def has_change_permission(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return False


# Admin personnalisé pour User
class CustomUserAdmin(UserAdmin):
    inlines = (ProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'get_role', 'date_joined', 'is_active')
    list_select_related = ('profile',)
    search_fields = ('username', 'email', 'first_name', 'last_name')
    list_filter = ('is_active', 'is_staff', 'profile__role')
    ordering = ['-date_joined']

    def get_role(self, instance):
        try:
            return instance.profile.role
        except Profile.DoesNotExist:
            return "Aucun rôle"
    get_role.short_description = 'Rôle'

    def get_inline_instances(self, request, obj=None):
        return super().get_inline_instances(request, obj)

    # Facultatif : désactiver modification si besoin
    def has_add_permission(self, request):
        return True

    def has_delete_permission(self, request, obj=None):
        return True


# Remplacer l'admin par défaut de User
admin.site.unregister(User)
admin.site.register(User, CustomUserAdmin)


# Admin pour le modèle Profile seul (facultatif, mais pas recommandé pour l'édition libre)
@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):

    list_display = ('user', 'role', 'get_email', 'get_date_joined', 'get_is_active')
    search_fields = ('user__username', 'user__email', 'role')
    list_filter = ('role',)
    readonly_fields = ('user',)
    exclude = ('created_at', 'updated_at')  # ou champs supplémentaires si tu en as

    def get_email(self, obj):
        return obj.user.email
    get_email.short_description = 'Email'

    def get_date_joined(self, obj):
        return obj.user.date_joined
    get_date_joined.short_description = 'Date d\'inscription'

    def get_is_active(self, obj):
        return obj.user.is_active
    get_is_active.boolean = True
    get_is_active.short_description = 'Actif'

    def has_add_permission(self, request):
        # Empêche l'ajout manuel de Profile sans User lié
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

    def save_model(self, request, obj, form, change):
        if not obj.user:
            raise ValueError("Un utilisateur doit être sélectionné.")
        super().save_model(request, obj, form, change)