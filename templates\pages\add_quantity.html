{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="card mb-3" style="max-width: 800px; margin-left: 10px; margin-top: 10px;"> <!-- Réduire la taille de la carte -->
    <div class="card-header">
        <h5 class="card-title" style="text-align: center;">Ajouter une quantité</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            {% csrf_token %}
        <div class="row mb-3">
            <div class="col-md-5"> <!-- Réduire la largeur de la catégorie -->
                <label for="categorie_select" class="form-label">Choisir une catégorie</label>
                <select class="form-select" id="categorie_select" name="categorie_select">
                    <option value="">Sélectionner une catégorie</option>
                    {% for categorie in categories %}
                        <option value="{{ categorie.id }}">{{ categorie.nom }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-5"> <!-- Réduire la largeur de l'équipement -->
                <label for="equipement_select" class="form-label">Choisir un équipement</label>
                <select class="form-select" id="equipement_select" name="equipement_select">
                    <option value="">Sélectionner un équipement</option>
                    {% for equipement in equipements %}
                        <option value="{{ equipement.id }}">{{ equipement.nom }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-5"> <!-- Réduire la largeur de l'équipement -->
                <label for="stock_select" class="form-label">Choisir un stock</label>
                <select class="form-select" id="stock_select" name="stock_select">
                    <option value="">Sélectionner un stock</option>
                    {% for stock in stocks %}
                        <option value="{{ stock.id }}">{{ stock.unite}}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-5"> <!-- Réduire la largeur de la quantité -->
                <label for="quantité" class="form-label">Quantité</label>
                <input type="text" class="form-control" id="quantité" name="quantité" required>
            </div>

            <div class="col-md-5"> <!-- Réduire la largeur de la référence -->
                <label for="reference" class="form-label">Référence</label>
                <input type="text" class="form-control" id="reference" name="reference" required>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-5"> <!-- Réduire la largeur de la date d'entrée -->
                <label for="date_entree" class="form-label">Date d'entrée</label>
                <input type="date" class="form-control" id="date_entree" name="date_entree" required>
            </div>

            <div class="col-md-5"> <!-- Réduire la largeur de la date de fin de garantie -->
                <label for="date_fin_garantie" class="form-label">Date de fin de garantie</label>
                <input type="date" class="form-control" id="date_fin_garantie" name="date_fin_garantie" required>
            </div>
        </div>

        <div class="mb-2">
            <div class="col-md-5"> 
                <label class="form-label">Numéro(s) de série</label>
                <div id="serial_numbers_container">
                    <div class="input-group mb-2">
                        <input type="text" class="form-control serial-number" name="numero_serie[]" placeholder="Numéro de série">
                        <button type="button" class="btn btn-outline-primary add-serial"><i class="fa fa-plus" aria-hidden="true"></i></button>
                    </div>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-primary">Ajouter</button>
    </form>
    </div>
</div>

<!-- JavaScript minimal pour gérer l'ajout de champs -->
<script>
    document.addEventListener("click", function (event) {
        if (event.target.classList.contains("add-serial")) {
            let container = document.getElementById("serial_numbers_container");
            let newField = document.createElement("div");
            newField.classList.add("input-group", "mb-2");
            newField.innerHTML = `
                <input type="text" class="form-control serial-number" name="numero_serie[]" placeholder="Numéro de série">
                <button type="button" class="btn btn-outline-danger remove-serial"><i class="fa fa-minus" aria-hidden="true"></i></button>
            `;
            container.appendChild(newField);
        }
        if (event.target.classList.contains("remove-serial")) {
            event.target.parentElement.remove();
        }
    });
</script>

{% endblock %}
