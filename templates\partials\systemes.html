{% load static %}
<div class="row" style="display: flex; margin-left: 10px; margin-top: 10px;">
    <div class="col-xl-3" style="margin-top: 20px; margin-left: 20px;">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-shield-alt me-1"></i>
                ESM
            </div>
            <div class="card-body">
                <img class="rounded mx-auto d-block  " 
     id="vegetables-img" 
     alt="Vegetable Tray" 
     src="{% static 'img/cuas.png' %}" 
     usemap="#vegetables-map" />
                <map name="vegetables-map">
                    <area target="" alt="SINGLE PHASE STABILIZER" title="camera" href="" coords="404,379,280,268" shape="rect" data-name="eos" />
                    <area target="" alt="antenne" title="antenne" href="" coords="135,286,162,286,168,443,135,443,134,414,134,355" shape="poly" data-name="antenne" />
                    <area target="" alt="roue" title="roue" href="" coords="556,760,98" shape="circle" data-name="celery" />




                    
                </map>   
            </div>
        </div>
    </div>

    <div class="col-xl-8" style="margin-top: 20px;">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-file-alt me-1"></i>
                Détails
            </div>
            <div class="card-body">
                <div class="card mb-3 w-100">
                    <div class="row g-0 h-100">
                        <div class="col-md-4">
                            <div id="selections-demo">
                                <div id="selections-vegetable-details"></div>
                                <span id="selections-instructions">
                                    Click a vegetable to see it's description!
                                </span>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="card-body">
                                <h5 class="card-title">Card title</h5>
                              
                                <p class="card-text"><small class="text-body-secondary">Last updated 3 mins ago</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion" id="accordionExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                Accordion Item #1
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                              <div class="card-body">
                                 <table id="datatablesSimple">
                                     <thead>
                                         <tr>
                                             <th>Nom</th>
                                             <th>Constructeur</th>
                                             <th>product_numbre</th>
                                             <th>Date d'Installation</th>
                                            
                                             <th>Criticité</th>
                                         </tr>
                                     </thead>
                                     <tfoot>
                                         <tr>
                                          <tr>
                                             <th>Nom</th>
                                             <th>Constructeur</th>
                                             <th>product_numbre</th>
                                             <th>Date d'Installation</th>
                                              
                                             <th>Criticité</th>
                                         </tr>
                                         </tr>
                                     </tfoot>
                                     <tbody>
                                       {% for equipement in equipements %}
                                       <tr>
                                           <td>{{ equipement.nom }}</td>
                                           <td>{{ equipement.constructeur }}</td>
                                           <td>{{ equipement.product_numbre }}</td>
                                           <td>{{ equipement.date_installation }}</td>
                                           
                                         
                                           <td>{{ equipement.criticite }}</td>
                                       </tr>
                                       {% empty %}
                                       <tr>
                                           <td colspan="7">Aucun équipement disponible</td>
                                       </tr>
                                       {% endfor %}
                                          
                                     </tbody>
                                 </table>
                             </div> 
                              






                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                Accordion Item #2
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                                <strong>This is the second item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall
                                appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within
                                the <code>.accordion-body</code>, though the transition does limit overflow.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                Accordion Item #3
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                                <strong>This is the third item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall
                                appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within
                                the <code>.accordion-body</code>, though the transition does limit overflow.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    
        $(function () {
          // a cross reference of area names to text for each area's tooltip
          var xref = {
            carrots: '<b>Carrots</b> are delicious and may turn your skin orange!',
            asparagus:
              "<b>Asparagus</b> is one of the first vegetables of the spring. Being a dark green, it's great for you, and has interesting side effects.",
            squash:
              '<b>Squash</b> is a winter vegetable, and not eaten raw too much. Is that really squash?',
            eos: '<img src="{% static "img/eos.jpeg" %}" data-zoom-image="{% static "img/antenne.jpeg" %}" alt="eos" width="100" id="zoom_02">', // Changed the quotes
      
            yellowpepper:
              'Similar to red peppers, <b>yellow peppers</b> are sometimes sweeter.',
            antenne: '<img src="{% static "img/antenne.jpeg" %}" alt="Antenne" width="100">', // Changed the quotes
      
            cucumbers: '<b>Cucumbers</b> are cool.',
            broccoli:
              '<b>Broccoli</b> is like a forest of goodness in your mouth. And very good for you. Eat lots of broccoli!',
            dip: "Everything here is good for you but this one. <b>Don't be a dip!</b>"
          },
          defaultDipTooltip =
            "I know you want the dip. But it's loaded with saturated fat, just skip it and enjoy as many delicious, crisp vegetables as you can eat.",
          $image = $('#vegetables-img'),
          $selectionsDetails = $('#selections-vegetable-details'),
          $selectionsInstructions = $('#selections-instructions'),
          opts = {
            enableAutoResizeSupport: true,
            autoResize: true,
            fillOpacity: 0.4,
            fillColor: 'd42e16',
            strokeColor: '3320FF',
            strokeOpacity: 0.8,
            strokeWidth: 4,
            stroke: true,
            isSelectable: true,
            singleSelect: true,
            mapKey: 'data-name',
            listKey: 'data-name',
            onClick: function (data) {
              var newToolTip = defaultDipTooltip;
              if (data.selected) {
                $selectionsDetails.html(xref[data.key]);
                $selectionsInstructions.hide();
                $selectionsDetails.show();
              } else {
                $selectionsDetails.hide();
                $selectionsInstructions.show();
              }
              if (data.key === 'asparagus') {
                newToolTip =
                  "OK. I know I have come down on the dip before, but let's be real. Raw asparagus without any of that " +
                  'delicious ranch and onion dressing slathered all over it is not so good.';
              }
              $image.mapster('set_options', {
                areas: [
                  {
                    key: 'dip',
                    toolTip: newToolTip
                  }
                ]
              });
            },
            showToolTip: true,
            toolTipClose: ['tooltip-click', 'area-click', 'image-mouseout'],
            areas: [
              {
                key: 'redpepper',
                fillColor: 'ffffff'
              },
              {
                key: 'yellowpepper',
                fillColor: '000000'
              },
              {
                key: 'carrots',
                fillColor: '000000'
              },
              {
                key: 'dip',
                toolTip: defaultDipTooltip
              },
              {
                key: 'asparagus',
                strokeColor: 'FFFFFF'
              }
            ]
          };
      
          $image.mapster(opts);
        });
      </script>
      
      <script>
        document.addEventListener("DOMContentLoaded", function () {
            document.querySelectorAll("area").forEach(area => {
                area.addEventListener("click", function (event) {
                    event.preventDefault();  // Empêcher le redirection par défaut
                    
                    let zone = this.dataset.name;  // Récupère le nom de la zone
        
                    fetch(`/get-equipements/?zone=${zone}`)
                        .then(response => response.json())
                        .then(data => {
                            let tableBody = document.querySelector("#datatablesSimple tbody");
                            tableBody.innerHTML = "";  // Vider le tableau avant d'insérer de nouvelles données
        
                            if (data.equipements.length > 0) {
                                data.equipements.forEach(equipement => {
                                    let row = `<tr>
                                        <td>${equipement.nom}</td>
                                        <td>${equipement.constructeur}</td>
                                        <td>${equipement.product_numbre}</td>
                                        <td>${equipement.date_installation || 'N/A'}</td>
                                        <td>${equipement.criticite}</td>
                                    </tr>`;
                                    tableBody.innerHTML += row;
                                });
                            } else {
                                tableBody.innerHTML = '<tr><td colspan="5">Aucun équipement trouvé</td></tr>';
                            }
                        })
                        .catch(error => console.error("Erreur lors de la récupération des équipements:", error));
                });
            });
        });
        </script>
 
