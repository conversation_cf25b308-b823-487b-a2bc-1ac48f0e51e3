{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-3">
    <div class="container mt-5">
        <form action="" method="POST" enctype="multipart/form-data">
            {% csrf_token %}

            <!-- Fonction -->
            <div class="border p-3 mb-2" style="margin-top: 5px;">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <h5><label for="fonction" class="form-label">Fonction:</label></h5>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="fonction" id="fonction_servant" value="servant" checked>
                            <label class="form-check-label" for="fonction_servant">
                                Servant
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="fonction" id="fonction_technicien" value="technicien">
                            <label class="form-check-label" for="fonction_technicien">
                                Technicien
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations générales -->
            <div class="border p-3 mb-3">
                <h5>Informations générales :</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="nom" class="form-label">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" maxlength="45" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="prenom" class="form-label">Prénom</label>
                        <input type="text" class="form-control" id="prenom" name="prenom" maxlength="45" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="grade" class="form-label">Grade</label>
                        <select class="form-select" id="grade" name="grade" required>
                            <option value="" disabled selected>Choisissez un grade</option>
                            <option value="COL">Colonel (COL)</option>
                            <option value="LCL">Lieutenant-Colonel (LCL)</option>
                            <option value="CDT">Commandant (CDT)</option>
                            <option value="CNE">Capitaine (CNE)</option>
                            <option value="LT">Lieutenant (LT)</option>
                            <option value="SLT">Sous-Lieutenant (SLT)</option>
                            <option value="ADJ/C">Adjudant-Chef (ADJ/C)</option>
                            <option value="ADJ">Adjudant (ADJ)</option>
                            <option value="S/C">Sergent-Chef (S/C)</option>
                            <option value="SGT">Sergent (SGT)</option>
                            <option value="CAL">Caporal-Chef (CAL)</option>
                            <option value="C/C">Caporal (C/C)</option>
                            <option value="1°C">1ère Classe (1°C)</option>
                            <option value="2°C">2ème Classe (2°C)</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="matricule" class="form-label">Matricule</label>
                        <input type="text" class="form-control" id="matricule" name="matricule" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date_affectation" class="form-label">Date d'Affectation</label>
                        <input type="date" class="form-control" id="date_affectation" name="date_affectation">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="specialite" class="form-label">Spécialité</label>
                        <input type="text" class="form-control" id="specialite" name="specialite" required>
                    </div>
                </div>
            </div>

            <!-- Formations -->
            <div class="border p-3 mb-3">
                <h5>Formations :</h5>
                <div id="formations-container">
                    <div class="col-md-4 mb-3">
                        <label for="selected_formations" class="form-label">Sélectionnez les formations</label>
                        <select class="form-select" id="selected_formations" multiple>
                            {% for formation in formations %}
                                <option value="{{ formation.id }}">{{ formation.intitule }}</option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">Maintenez Ctrl pour sélectionner plusieurs formations.</small>
                    </div>
                </div>
                <button type="button" class="btn btn-success" id="add-formation-btn">
                    <i class="fa-solid fa-plus"></i> Ajouter une formation
                </button>
            </div>

            <!-- Bouton de validation -->
            <div class="d-flex justify-content-center mb-3">
                <button type="submit" class="btn btn-primary">Valider</button>
            </div>
        </form>
    </div>
</div>

<script>
   
 // Script pour ajouter dynamiquement des champs de formation
document.addEventListener('DOMContentLoaded', function () {
    let formationCount = 0;

    const addFormationBtn = document.getElementById('add-formation-btn');
    const formationsContainer = document.getElementById('formations-container');
    const selectedFormations = document.getElementById('selected_formations');

    addFormationBtn.addEventListener('click', function () {
        const selectedOptions = Array.from(selectedFormations.selectedOptions);
        if (selectedOptions.length === 0) {
            alert("Veuillez sélectionner au moins une formation.");
            return;
        }

        selectedOptions.forEach(option => {
            const newFormationGroup = document.createElement('div');
            newFormationGroup.classList.add('formation-group', 'row', 'mb-3');
            newFormationGroup.setAttribute('data-formation-id', option.value);

            // Générer le HTML pour la nouvelle formation
            newFormationGroup.innerHTML = `
                <div class="col-md-6 mb-3">
                    <label for="intitule_${formationCount}" class="form-label">Intitulé de la formation:</label>
                    <input type="text" class="form-control" id="intitule_${formationCount}" name="Intituleformation[]" value="${option.text}" readonly>
                    <input type="hidden" name="formation_id[]" value="${option.value}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="reference_${formationCount}" class="form-label">Référence:</label>
                    <input type="text" class="form-control" id="reference_${formationCount}" name="reference[]" maxlength="105" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="date_debut_${formationCount}" class="form-label">Date de début:</label>
                    <input type="date" class="form-control date-debut" id="date_debut_${formationCount}" name="date_debut[]" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="date_fin_${formationCount}" class="form-label">Date de fin:</label>
                    <input type="date" class="form-control date-fin" id="date_fin_${formationCount}" name="date_fin[]" required>
                </div>
                <div class="col-md-12 mb-3 text-end">
                    <button type="button" class="btn btn-danger remove-formation-btn">
                        <i class="fa-solid fa-trash"></i> Supprimer
                    </button>
                </div>
            `;

            // Validation des dates lors de l'ajout
            const dateDebutInput = newFormationGroup.querySelector('.date-debut');
            const dateFinInput = newFormationGroup.querySelector('.date-fin');

            dateFinInput.addEventListener('change', function () {
                const dateDebut = new Date(dateDebutInput.value);
                const dateFin = new Date(dateFinInput.value);

                if (dateDebut > dateFin) {
                    alert("La date de début doit être antérieure ou égale à la date de fin.");
                    dateFinInput.value = ''; // Effacer la valeur incorrecte
                }
            });

            formationsContainer.appendChild(newFormationGroup);
            formationCount++;
        });

        selectedFormations.selectedIndex = -1;

        // Ajouter un gestionnaire d'événements pour les boutons "Supprimer"
        const removeButtons = document.querySelectorAll('.remove-formation-btn');
        removeButtons.forEach(button => {
            button.addEventListener('click', function () {
                const parentGroup = this.closest('.formation-group');
                parentGroup.remove();
            });
        });
    });
});
</script>
{% endblock %}