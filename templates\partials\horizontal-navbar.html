<nav class="sb-topnav navbar navbar-expand navbar-dark bg-dark">
  <a class="navbar-brand ps-3" href="{% url 'index' %}">GE-R</a>

  <button class="btn btn-link btn-sm order-1 order-lg-0 me-4 me-lg-0" id="sidebarToggle">
    <i class="fas fa-bars"></i>
  </button>

  <div class="d-flex ms-auto">
    <div class="me-3">
      <a href="{% url 'chatbot' %}" aria-label="Chatbot">
        <i class="fa-solid fa-robot fa-2x"></i>
      </a>
    </div>

    <!-- Icône Notification -->
    <div class="user-notification">
      <ul class="navbar-nav">
        <li class="nav-item dropdown me-3">
          <a class="nav-link position-relative" id="notificationsDropdown" href="#" role="button"
             data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-bell fa-fw fa-lg"></i>
            <span id="notificationBadge" class="top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge small">0</span>
          </a>

          <ul class="dropdown-menu dropdown-menu-end p-2 show-on-click" aria-labelledby="notificationsDropdown" id="notifications-container" style="max-height: 300px; overflow-y: auto;">
            <li><a class="dropdown-item text-center">Chargement...</a></li>
          </ul>
        </li>
      </ul>
    </div>
  </div>

  <ul class="navbar-nav ms-auto ms-md-0 me-3 me-lg-4">
    <li class="nav-item dropdown">
      <a class="nav-link dropdown-toggle" id="navbarDropdown" href="#" role="button" data-bs-toggle="dropdown">
        <i class="fas fa-user fa-fw"></i>
      </a>
      <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
        <li>
          <form method="POST" action="{% url 'logout' %}">
            {% csrf_token %}
            <button type="submit" class="dropdown-item">Logout</button>
          </form>
        </li>
      </ul>
    </li>
  </ul>
</nav>

<script>
// CSRF Token extraction
function getCSRFToken() {
  let cookieValue = null;
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const trimmed = cookie.trim();
    if (trimmed.startsWith('csrftoken=')) {
      cookieValue = trimmed.substring('csrftoken='.length);
      break;
    }
  }
  return cookieValue;
}

// Supprimer une notification avec confirmation si nécessaire
function removeNotification(notificationId) {
  const notification = document.getElementById(`notification-${notificationId}`);
  if (!notification) return;

  const message = notification.querySelector('.notification-message')?.innerText.toLowerCase() || "";

  if (message.includes("panne")) {
    const confirmed = confirm("Cette notification concerne une panne. Veuillez traiter la panne avant de la supprimer. Confirmez-vous ?");
    if (!confirmed) return;
  }

  notification.remove();

  const badge = document.getElementById("notificationBadge");
  let count = parseInt(badge.textContent) || 0;
  badge.textContent = Math.max(count - 1, 0);
  if (count <= 1) badge.style.display = "none";

  fetch(`/mark_notification_as_read/${notificationId}/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': getCSRFToken()
    }
  }).then(r => r.json()).catch(console.error);
}

// Charger les notifications depuis l’API
function fetchNotifications() {
  fetch('/fetch_notifications/')
    .then(response => response.json())
    .then(data => {
      const container = document.getElementById("notifications-container");
      container.innerHTML = '';

      const badge = document.getElementById("notificationBadge");
      badge.textContent = data.notifications.length;
      badge.style.display = data.notifications.length ? 'inline-block' : 'none';

      if (!data.notifications.length) {
        container.innerHTML = `<li class="dropdown-item text-center">Aucune notification</li>`;
        return;
      }

      data.notifications.forEach(notif => {
        const li = document.createElement('li');
        li.classList.add('dropdown-item');
        li.style.cursor = "pointer";
        li.id = `notification-${notif.id}`;
        li.innerHTML = `
          <div class="alert alert-${notif.type_notification} notification-alert shadow-sm rounded mb-2" role="alert">
            <span class="notification-message">${notif.message}</span>
          </div>
        `;

        li.addEventListener('click', e => {
          e.stopPropagation(); // Empêche la fermeture du dropdown
          removeNotification(notif.id);
        });

        container.appendChild(li);
      });
    })
    .catch(error => console.error("Erreur récupération notifications:", error));
}

document.addEventListener("DOMContentLoaded", fetchNotifications);
setInterval(fetchNotifications, 30000); // Toutes les 30s
</script>
