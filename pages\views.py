from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Count, Q
from django.db.models.functions import ExtractMonth, ExtractYear
from django.template.loader import render_to_string
from django.dispatch import receiver
from django.db.models.signals import post_save
from django.db import models

from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from reportlab.lib.colors import black, grey, lightgrey
from reportlab.lib.units import inch

from itertools import chain
from collections import OrderedDict, defaultdict
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import json, calendar, math, textwrap

from .models import (
    Equipement, Maintenance, TechnicienHasMaintenance, EquipementSystemeInstance,
    Notification, Systeme, Valeur, Technicien, Servant, ServantHasFormation,
    Stock, StockHasEquipement, EquipementStockInstance, SousSysteme,
    MaintenanceHistory, SousSystemeHasEquipement, TechnicienHasFormation,
    MissionHasServant, MissionHasTechnicienRole, MissionHasServantRole,
    Formation, Bion, Cie, Sion, Mission, MissionHasTechnicien,
    SystemeHasSousSysteme, Media
)
from .forms import (
    MaintenanceForm, MaintenanceHistoryForm, AtelierForm,
    ReparationExterneForm, DetailsReparationForm
)
from accounts.decorators import admin_required, admin_or_user_required





 

@login_required
def index(request):
    # Récupérer les notifications non lues
    unread_notifications = Notification.objects.filter(is_read=False).order_by("-id")
    counts = unread_notifications.count()
# Requêtes pour chaque catégorie avec les statuts 'OPERATIONNEL', 'operationnel' et 'OPS'
    resultats_esm = Systeme.objects.filter(
    Q(nom__startswith='ESM') & (Q(status='OPERATIONNEL') | Q(status='operationnel') | Q(status='OPS'))
).order_by('nom')

    resultats_ecm = Systeme.objects.filter(
    Q(nom__startswith='ECM') & (Q(status='OPERATIONNEL') | Q(status='operationnel') | Q(status='OPS'))
).order_by('nom')

    resultats_cc = Systeme.objects.filter(
    Q(nom__startswith='CC') & (Q(status='OPERATIONNEL') | Q(status='operationnel') | Q(status='OPS'))
).order_by('nom')

    resultats_bcc = Systeme.objects.filter(
    Q(nom__startswith='BCC') & (Q(status='OPERATIONNEL') | Q(status='operationnel') | Q(status='OPS'))
).order_by('nom')

    resultats_maintenance = Systeme.objects.filter(
    Q(nom__startswith='MAINTENANCE') & (Q(status='OPERATIONNEL') | Q(status='operationnel') | Q(status='OPS'))
).order_by('nom')

    resultats_simulateur = Systeme.objects.filter(
    Q(nom__startswith='SIMULATEUR') & (Q(status='OPERATIONNEL') | Q(status='operationnel') | Q(status='OPS'))
).order_by('nom')

    for r in resultats_esm:
        print("result :")
        print(  r.id)
    # Fonction pour calculer le statut et le pourcentage d'équipements en panne
    def calculer_statut_et_pourcentage(resultats):
        nombre_en_panne = 0
        total = len(resultats)
        for res in resultats:
            if res.status.lower() in ['en panne', 'panne']:
                nombre_en_panne += 1
        pourcentage_en_panne = (nombre_en_panne / total) * 100 if total > 0 else 0
        pourcentage_operationnel = 100 - pourcentage_en_panne
        statut = 'OPERATIONNEL' if nombre_en_panne == 0 else 'EN PANNE'
        return statut, math.ceil(pourcentage_operationnel)  # Arrondir au-dessus
    
    
   
    stock = StockHasEquipement.objects.filter(
    quantite__lte=models.F('quantite_critique')
    ) 
   
    
           
    
   
 
      
    # Calcul des statuts et pourcentages pour chaque système
    esm_statut, esm_pourcentage = calculer_statut_et_pourcentage(resultats_esm)
    ecm_statut, ecm_pourcentage = calculer_statut_et_pourcentage(resultats_ecm)
    cc_statut, cc_pourcentage = calculer_statut_et_pourcentage(resultats_cc)
    bcc_statut, bcc_pourcentage = calculer_statut_et_pourcentage(resultats_bcc)
    maintenance_statut, maintenance_pourcentage = calculer_statut_et_pourcentage(resultats_maintenance)
    simulateur_statut, simulateur_pourcentage = calculer_statut_et_pourcentage(resultats_simulateur)

    # Ajout des résultats au dictionnaire
    results = {
        'resultats_esm': resultats_esm,
        'resultats_ecm': resultats_ecm,
        'resultats_cc': resultats_cc,
        'resultats_bcc': resultats_bcc,
        'resultats_maintenance': resultats_maintenance,
        'resultats_simulateur': resultats_simulateur,
        'esm_statut': esm_statut,
        'ecm_statut': ecm_statut,
        'cc_statut': cc_statut,
        'bcc_statut': bcc_statut,
        'maintenance_statut': maintenance_statut,
        'simulateur_statut': simulateur_statut,
        'esm_pourcentage': esm_pourcentage,
        'ecm_pourcentage': ecm_pourcentage,
        'cc_pourcentage': cc_pourcentage,
        'bcc_pourcentage': bcc_pourcentage,
        'maintenance_pourcentage': maintenance_pourcentage,
        'simulateur_pourcentage': simulateur_pourcentage,
    }

    # Récupérer tous les équipements
    equipement_instance = EquipementSystemeInstance.objects.filter(date_remplacement__isnull=True)

    # Préparer les événements pour FullCalendar
    events = []

    for eq in equipement_instance:
        # Vérifier que les champs requis sont renseignés
        if (
            eq.date_derniere_maintenance and
            eq.sous_systeme_has_instance.equipement.periodicite_maintenance
        ):
            # Calculer les 5 prochaines maintenances
            for i in range(2):  # Boucle pour générer 5 dates
                prochaine_maintenance = (
                    eq.date_derniere_maintenance +
                    relativedelta(months=(eq.sous_systeme_has_instance.equipement.periodicite_maintenance * (i + 1)))
                )
               
                events.append({
                    'title': f"{eq.sous_systeme_has_instance.systeme.nom} {eq.sous_systeme_has_instance.equipement.nom} {eq.numero_serie}",
                    'start': prochaine_maintenance.isoformat(),
                   
                    'description': f"{eq.sous_systeme_has_instance.equipement.type}",
                    'extendedProps': {
                        'id_instance': int(eq.sous_systeme_has_instance.id),  # Nested under extendedProps
                        'serial_id': eq.numero_serie,
                    },
                })
                print(prochaine_maintenance.strftime("%Y-%m-%d"))
    # Convertir les événements en JSON
    events_json = json.dumps(events)

    return render(
        request,
        'pages/index.html',
        {
            'counts': counts,
            'results': results,
            'events': events_json,
             'esm_pourcentage': esm_pourcentage,
        'ecm_pourcentage': ecm_pourcentage,
        'cc_pourcentage': cc_pourcentage,
        'bcc_pourcentage': bcc_pourcentage,
        'maintenance_pourcentage': maintenance_pourcentage,
        'simulateur_pourcentage': simulateur_pourcentage,
         'stock':stock,
        }
    )
 



# Vue "about" pour les autres utilisateurs
@login_required
def about(request):
    return render(request, 'pages/about.html')


 




def get_equipements(request):
    piece_nom = request.GET.get('partie')  # Récupère le nom de l'équipement (par exemple "must" pour le mât)
    id=request.GET.get('id') 
     # Vérifiez que les paramètres sont présents
    if not piece_nom or not id:
        return JsonResponse({'error': 'Paramètres manquants.'}, status=400)
    try:
        # Récupère l'équipement en utilisant son nom
        equipement = Equipement.objects.get(nom=piece_nom)
        nom = equipement.nom  # Détails de l'équipement
        image_url = equipement.media.url
        equipement_id=equipement.id
        try:
        # Filtrez les sous-systèmes associés
         sous_systeme_equipements = SousSystemeHasEquipement.objects.filter(
            equipement_id=equipement.id,
            systeme_id=id
        )
        except Exception as e:
         return JsonResponse({'error': f'Erreur lors de la récupération des sous-systèmes : {str(e)}'}, status=500)
        equipement_systeme_instances = EquipementSystemeInstance.objects.filter(sous_systeme_has_instance__in=sous_systeme_equipements)
        systemes = [
             {'id': instance.id, 'numero_serie': instance.numero_serie,'systeme_nom': instance.sous_systeme_has_instance.systeme.nom,'statut':instance.statut } 
            for instance in equipement_systeme_instances
             if not instance.date_remplacement  # Vérifie si date_remplacement est null ou vide
        ]  
        if not systemes:
            response_data = {'error': 'Aucun système trouvé pour cet équipement'}
        else:
            response_data = {
                'nom': nom,
                'image': image_url,
                'systemes': systemes,
                'equipement_id':equipement_id,
            }
    except Exception as e:
        response_data = {'error': str(e)}
    return JsonResponse(response_data)  


@login_required
def ect(request):
    return render(request, 'pages/ect.html')

@login_required
def bst(request):
    return render(request, 'pages/bst.html')


def formatmatricule(matricule, prefix):
    try:
        # Vérifier si le matricule commence par le préfixe
        if matricule.startswith(prefix):
            # Retourner uniquement le préfixe
            return prefix
        else:
            # Si le matricule ne contient pas le préfixe, retourner le matricule tel quel
            return matricule
    except Exception:
        # En cas d'erreur inattendue, retourner le matricule tel quel
        return matricule
    
 
def get_personnel():
     # Récupérer tous les techniciens et servants
    techniciens = Technicien.objects.all()
    servants = Servant.objects.all()


    # Fusionner les deux querysets en une seule liste
    personnels = []
    for technicien in techniciens:
        personnels.append({
            'id':technicien.id,
            'specialite': technicien.specialite,
            'nom': technicien.nom,
            'prenom': technicien.prenom,
            'matricule': technicien.matricule,
            'matricule_formate': formatmatricule(technicien.matricule,"PO"),
            'date_affectation': technicien.date_affectation,
            'grade':technicien.grade,
        })
    for servant in servants:
        personnels.append({
            'id':servant.id,
            'specialite':servant.specialite,
            'nom': servant.nom,
            'prenom': servant.prenom,
            'matricule': servant.matricule,
            'matricule_formate': formatmatricule(servant.matricule,"PO"),
            'date_affectation': servant.date_affectation,
            'grade':servant.grade,
        })
    return personnels

@login_required

def personnel(request):
   personnels=get_personnel()
   context = {
        'personnels': personnels,
    }
   return render(request, 'pages/personnel.html', context)





@admin_required
@login_required
def panne(request):

   histories = MaintenanceHistory.objects.all().order_by('-date_panne')
   
    
   context = {
        'histories': histories,
    }
   return render(request, 'pages/panne.html',context)

 









@login_required
@admin_or_user_required
def fiche_personnel(request, id):
    info_servant = None
    info_technicien = None
    info_formation_technicien = []
    info_formation_servant = []
    mois_labels = []
    intervention_data = []
    mission_data = []

    try:
        # Cas servant
        info_servant = Servant.objects.get(matricule=id)
        info_formation_servant = ServantHasFormation.objects.filter(servant=info_servant)

        missions_servant = (
    MissionHasServantRole.objects
    .filter(servant=info_servant)
    .annotate(
        year=ExtractYear('missionhasservant__mission__date_debut_mission'),
        month=ExtractMonth('missionhasservant__mission__date_debut_mission')
    )
    .values('year', 'month')
    .annotate(count=Count('id'))
    .order_by('year', 'month')
)

        grouped_missions = defaultdict(lambda: [0]*12)
        for m in missions_servant:
            grouped_missions[m['year']][m['month'] - 1] += m['count']

        for year in sorted(grouped_missions.keys()):
            for month in range(1, 13):
                mois_labels.append(f"{calendar.month_name[month]} {year}")
                mission_data.append(grouped_missions[year][month-1])
                intervention_data.append(0)  # Pas d'intervention technique pour servant

    except Servant.DoesNotExist:
        try:
            # Cas technicien
            info_technicien = Technicien.objects.get(matricule=id)
            info_formation_technicien = TechnicienHasFormation.objects.filter(technicien=info_technicien)

            interventions = (
                Maintenance.objects
                .filter(technicien=info_technicien)
                .exclude(date_maintenance__isnull=True)
                .annotate(year=ExtractYear('date_maintenance'),
                          month=ExtractMonth('date_maintenance'))
                .values('year', 'month')
                .annotate(count=Count('id'))
                .order_by('year', 'month')
            )
            


            missions= (
    MissionHasTechnicienRole.objects
    .filter(technicien=info_technicien)
    .annotate(
        year=ExtractYear('missionhastechnicien__mission__date_debut_mission'),
        month=ExtractMonth('missionhastechnicien__mission__date_debut_mission')
    )
    .values('year', 'month')
    .annotate(count=Count('id'))
    .order_by('year', 'month')
)
             

            grouped_data = defaultdict(lambda: {"intervention": [0]*12, "mission": [0]*12})
            for entry in interventions:
                grouped_data[entry['year']]["intervention"][entry['month'] - 1] += entry['count']
            for entry in missions:
                grouped_data[entry['year']]["mission"][entry['month'] - 1] += entry['count']

            for year in sorted(grouped_data.keys()):
                for month in range(1, 13):
                    mois_labels.append(f"{calendar.month_name[month]} {year}")
                    intervention_data.append(grouped_data[year]["intervention"][month-1])
                    mission_data.append(grouped_data[year]["mission"][month-1])

        except Technicien.DoesNotExist:
            raise Http404("Personnel non trouvé")

    context = {
        'info_servant': info_servant,
        'info_technicien': info_technicien,
        'info_formation_technicien': info_formation_technicien,
        'info_formation_servant': info_formation_servant,
        'mois_labels': json.dumps(mois_labels),
        'intervention_data': json.dumps(intervention_data),
        'mission_data': json.dumps(mission_data),
    }

    return render(request, 'pages/fiche_personnel.html', context)



@login_required
def simulateur(request):
    return render(request, 'pages/intervention.html')

@login_required
def bge(request):
    equipements_info = []

    # Récupérer toutes les données nécessaires en une seule requête
    stock_has_equipements = StockHasEquipement.objects.all()
    equipement_instances = EquipementStockInstance.objects.filter(date_sortie__isnull=True)

    # Créer un dictionnaire pour mapper les instances d'équipements à leurs stock_has_instance
    equipement_instance_map = {}
    for equip_instance in equipement_instances:
        key = equip_instance.stock_has_instance.id
        if key not in equipement_instance_map:
            equipement_instance_map[key] = []
        equipement_instance_map[key].append(equip_instance)

    # Liste pour les objets à mettre à jour (utilisée avec bulk_update)
    to_update = []

    # Itérer sur les stock_has_equipements et calculer la quantité
    for item in stock_has_equipements:
        qte = len(equipement_instance_map.get(item.id, []))

        # Vérifier si la quantité a changé avant de sauvegarder
        if item.quantite != qte:
            item.quantite = qte
            to_update.append(item)  # Ajouter à la liste des objets à mettre à jour

        # Ajouter les informations d'équipement au résultat
        equipement_data = {
            'equipement': item.equipement,
            'sous_systeme': item.sous_systeme,
            'stock': item.stock,
            'quantite': qte,
            'instances': equipement_instance_map.get(item.id, []),
        }
        equipements_info.append(equipement_data)

    # Utiliser bulk_update pour mettre à jour tous les objets en une seule requête
    if to_update:
        StockHasEquipement.objects.bulk_update(to_update, ['quantite'])

    # Récupérer les données supplémentaires pour le rendu du template
    equipements = Equipement.objects.all()
    categories = StockHasEquipement.objects.select_related("sous_systeme").all()

    return render(request, 'pages/bge.html', {
        'equipements_info': equipements_info,
        'equipements': equipements,
        'categories': categories
    })
from django.shortcuts import get_object_or_404, render
from .models import Maintenance, TechnicienHasMaintenance

def fiche_maintenance(request, id):
    maintenance = get_object_or_404(Maintenance, id=id)
    equipement_media = maintenance.equipement
    equipement_instance = maintenance.equipementsystemeInstance
    numero_serie = equipement_instance.numero_serie if equipement_instance else "Non spécifié"
    
    technicien_maintenance = TechnicienHasMaintenance.objects.filter(maintenance=maintenance).first()
    if technicien_maintenance:
        maintenance.technicien_nom = technicien_maintenance.technicien.nom
        maintenance.technicien_prenom = technicien_maintenance.technicien.prenom
    else:
        maintenance.technicien_nom = "Non spécifié"
        maintenance.technicien_prenom = ""

    context = {
        'maintenance': maintenance,
        'equipement_instance': equipement_instance,
        'equipement_media': equipement_media,
        'numero_serie': numero_serie,
        'images': maintenance.media.all(),  # <- Ajout ici
    }
    return render(request, 'pages/fiche_maintenance.html', context)











import json
from django.shortcuts import render, get_object_or_404
from django.core.exceptions import ObjectDoesNotExist
from .models import Equipement, EquipementSystemeInstance, Valeur, StockHasEquipement, Maintenance

def fiche_equipement(request, id1, id2):
    try:
        # Récupération des objets
        instance = get_object_or_404(EquipementSystemeInstance, id=id2)
        equipement = get_object_or_404(Equipement, id=id1)

        # Image
        try:
            equi_image = equipement.media.url
        except (ValueError, AttributeError):
            equi_image = None

        # Caractéristiques
        caracteristiques = Valeur.objects.filter(equipement=equipement)
        caracteristiques_data = [
            {"caracteristique": str(c.caracteristique), "valeur": str(c.valeur)}
            for c in caracteristiques
        ]

        # Stock
        try:
            equipement_stock = StockHasEquipement.objects.get(equipement=equipement.id)
            quantite_stock = equipement_stock.quantite
        except ObjectDoesNotExist:
            equipement_stock = None
            quantite_stock = 0

        # Maintenance
        maintenances = Maintenance.objects.filter(equipementsystemeInstance=instance).order_by('-date_maintenance')

        # Instance de l’équipement parent (si défini)
        parent_instance = None
        if equipement.parent:
            parent_instance = EquipementSystemeInstance.objects.filter(
                sous_systeme_has_instance__equipement=equipement.parent
            ).first()

        return render(request, 'pages/fiche_equipement.html', {
            'instances': instance,
            'equi_image': equi_image,
            'equipement': equipement,
            'equipement_parent_instance': parent_instance,
            'equipement_stock': equipement_stock,
            'quantite_stock': quantite_stock,
            'maintenances': maintenances,
            'caracteristiques': json.dumps(caracteristiques_data),
        })

    except Exception as e:
        return render(request, 'pages/error.html', {'error_message': str(e)})








from .models import Media

@login_required
def preventive(request):
    if request.method == 'POST':
        form = MaintenanceForm(request.POST, request.FILES)
        if form.is_valid():
            maintenance = form.save()
            technicien = form.cleaned_data['technicien']
            if technicien:
                TechnicienHasMaintenance.objects.create(
                    technicien=technicien,
                    maintenance=maintenance
                )

            # Gérer les images uploadées
            for image in request.FILES.getlist('media'):
                media_obj = Media.objects.create(image=image)
                maintenance.media.add(media_obj)

            return redirect('intervention')
        else:
            return render(request, 'pages/preventive.html', {'form': form})
    else:
        # Initialiser le champ type_maintenance à "PREVENTIVE"
        form = MaintenanceForm(initial={'type_maintenance': 'PREVENTIVE'})
    return render(request, 'pages/preventive.html', {'form': form})





from .models import Media

@login_required
def corrective(request):
    if request.method == 'POST':
        form = MaintenanceForm(request.POST, request.FILES)
        if form.is_valid():
            maintenance = form.save()

            # Technicien, Système, Equipement, etc.
            technicien = form.cleaned_data['technicien']
            date_sortie_stock = form.cleaned_data['date_sortie_stock']
            date_remplacement = form.cleaned_data['date_maintenance']

            # Mise à jour de l'équipement de rechange
            equipement_de_rechange = form.cleaned_data.get('equipement_de_rechange')
            if equipement_de_rechange:
                equipement_de_rechange.date_sortie = date_sortie_stock
                equipement_de_rechange.save()

            # Liaison technicien ↔ maintenance
            if technicien:
                TechnicienHasMaintenance.objects.create(
                    technicien=technicien,
                    maintenance=maintenance
                )

            # Mise à jour de l'équipement en panne
            instance_id = form.cleaned_data['equipementsystemeInstance'].id
            instance = EquipementSystemeInstance.objects.filter(id=instance_id).first()
            if instance:
                instance.date_remplacement = date_remplacement
                instance.destination = 'réparation'
                instance.save()

            # 🎯 Gestion des fichiers image (Media)
            for uploaded_file in request.FILES.getlist('media'):
                media_obj = Media.objects.create(image=uploaded_file)
                maintenance.media.add(media_obj)

            return redirect('intervention')
    else:
        form = MaintenanceForm()
        form.fields['equipementsystemeInstance'].queryset = EquipementSystemeInstance.objects.filter(date_remplacement__isnull=True)
        form.fields['equipement_de_rechange'].queryset = EquipementStockInstance.objects.filter(date_sortie__isnull=True)

    return render(request, 'pages/corrective.html', {'form': form})




@login_required
def declarer_panne(request):
    message = None  # message d’alerte si instance manquante

    if request.method == 'POST':
        form = MaintenanceForm(request.POST, request.FILES)
        if form.is_valid():
            maintenance = form.save()

            equipementsystemeInstance = form.cleaned_data.get('equipementsystemeInstance')
            systeme = form.cleaned_data.get('systeme')

            if equipementsystemeInstance:
                statut_equipement = form.cleaned_data.get('statut_equipement')
                equipementsystemeInstance.statut = statut_equipement
                equipementsystemeInstance.save()
            else:
                message = "⚠️ Aucun équipement sélectionné. Merci d’ajouter une instance d’équipement liée."

            if systeme:
                statut_systeme = form.cleaned_data.get('statut_systeme')
                systeme.status = statut_systeme
                systeme.save()

            if maintenance.technicien:
                TechnicienHasMaintenance.objects.create(
                    maintenance=maintenance,
                    technicien=maintenance.technicien
                )

            for image in request.FILES.getlist('media'):
                media_obj = Media.objects.create(image=image)
                maintenance.media.add(media_obj)

            return redirect('intervention')
    else:
        form = MaintenanceForm(initial={'type_maintenance': 'PANNE'})

    return render(request, 'pages/declarer_panne.html', {'form': form, 'message': message})



 

@login_required
def systeme(request, id):
    # Initialisation des variables
    cat = []  # Liste vide par défaut
    equipements_par_sous_systeme_dict = defaultdict(list)  # Dictionnaire vide par défaut
    
    # Récupérer l'objet Systeme correspondant à 'id'
    systeme = get_object_or_404(Systeme, nom=id)
    
    
    # Filtrer les sous-systèmes liés à ce système
    cat = SystemeHasSousSysteme.objects.filter(systeme=systeme)
    print(cat)
    sous_systemes = [relation.sous_systeme for relation in cat]
     
    
    # Récupérer les équipements liés aux sous-systèmes DU SYSTÈME SÉLECTIONNÉ
    equipements_par_sous_systeme = (
        SousSystemeHasEquipement.objects
        .filter(
            sous_systeme__in=sous_systemes,
            systeme=systeme  # Filtrer par système
        )
        .select_related('equipement', 'sous_systeme', 'systeme')  # Optimisation des requêtes
    )
 
    
    # Regrouper les équipements par sous-système
    for relation in equipements_par_sous_systeme:
        sous_systeme = relation.sous_systeme
        equipement = relation.equipement
        quantite = relation.quantite or 0
        
        # Récupérer les instances et leurs numéros de série
        instances = EquipementSystemeInstance.objects.filter(sous_systeme_has_instance=relation)
        
        # Créer une liste de tuples (numero_serie, instance_id)
        serial_numbers_with_instances = [
            (instance.numero_serie, instance.id) if instance.numero_serie else ("Non spécifié", None)
            for instance in instances
        ]
        
        # Remplir les numéros de série manquants avec "Non spécifié"
        serial_numbers_with_instances += [("Non spécifié", None)] * (quantite - len(serial_numbers_with_instances))
        
        # Ajouter les données dans le dictionnaire regroupé par sous-système
        equipements_par_sous_systeme_dict[sous_systeme].append({
            "designation": equipement.nom,
            "equi_id": equipement.id,
            "marque": equipement.constructeur,
            "quantite": quantite,
            "product_number": getattr(equipement, 'product_numbre', "Non spécifié"),  # Gestion des attributs manquants
            "serial_numbers_with_instances": serial_numbers_with_instances[:quantite],  # Limiter à la quantité
            "details": {
                "mttr": equipement.mttr,
                "mtbf": equipement.mtbf,
                "criticite": equipement.criticite,
            }
        })
    
    # Définir le contexte après la boucle
    context = {
        'cat': cat,
        'equipements_par_sous_systeme': dict(equipements_par_sous_systeme_dict),
        'systeme_id': id  # Passer l'ID au template
    }
    
 
    return render(request, 'pages/systeme.html', context)
 



@login_required
def fiche_maint(request):
    return render(request, 'pages/fiche_maintenance.html')

@login_required
def ecm(request):
    return render(request, 'pages/ecm.html')

@login_required
def cc(request):
    return render(request, 'pages/cc.html')

@login_required
def allstock(request):
    return render(request, 'pages/allstock.html')

@login_required
def maintenance(request):
    return render(request, 'pages/maintenance.html')

@login_required
def intervention(request):
    maintenances = Maintenance.objects.all()
    for maintenance in maintenances:
        # Récupérer les technicien associé à la maintenance
        technicien_maintenance = TechnicienHasMaintenance.objects.filter(maintenance=maintenance).first()
        if technicien_maintenance:
            maintenance.technicien_nom = technicien_maintenance.technicien.nom+' '+technicien_maintenance.technicien.prenom
        else:
            maintenance.technicien_nom = "Non spécifié"
    
    context = {
        'maintenances': maintenances,
    }
    return render(request, 'pages/intervention.html', context)
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from .models import Mission, MissionHasServantRole

@login_required
def mission(request):
    missions = Mission.objects.select_related('bion_mission') \
                               .prefetch_related('cie_missions', 'sion_missions') \
                               .order_by('-id')

    # Préparer le mapping mission.id → responsables (servants avec rôle "responsable")
    responsables_by_mission = {}

    for mission_instance in missions:
        responsables = MissionHasServantRole.objects.filter(
            missionhasservant__mission=mission_instance,
            role__icontains='responsable'
        ).select_related('servant')
        responsables_by_mission[mission_instance.id] = responsables

    return render(request, 'pages/mission.html', {
        'missions': missions,
        'responsables_by_mission': responsables_by_mission,
    })


from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from .models import (
    Mission, MissionHasTechnicien, MissionHasServant,
    MissionHasServantRole, MissionHasTechnicienRole,
    Technicien, Servant, Bion, Cie, Sion,MissionHasConducteurRole,MissionHasConducteur,Conducteur,
    AutrePersonnel, MissionHasAutrePersonnel, MissionHasAutrePersonnelRole
)

@login_required
def addmission(request):
    if request.method == 'POST':
        objet = request.POST.get('objet')
        reference_mission = request.POST.get('reference_mission')
        date_debut_mission = request.POST.get('date_debut_mission')
        date_fin_mission = request.POST.get('date_fin_mission')
        description = request.POST.get('description')
        lieu = request.POST.get('lieu')
        latitude = request.POST.get('latitude')
        longitude = request.POST.get('longitude')   

        # Récupérer l’unique bataillon
        bion_id = request.POST.get('bataillon')
        bion = Bion.objects.get(id=bion_id) if bion_id else None

        # Créer la mission principale
        mission = Mission.objects.create(
            bion_mission=bion,
            objet=objet,
            reference_mission=reference_mission,
            date_debut_mission=date_debut_mission,
            date_fin_mission=date_fin_mission,
            description=description,
            lieu=lieu,
            latitude=latitude,
            longitude=longitude,  
        )

        # Ajouter compagnies
        for cie_id in request.POST.getlist('compagnies[]'):
            cie = Cie.objects.get(id=cie_id)
            mission.cie_missions.add(cie)

        # Ajouter sections
        for sion_id in request.POST.getlist('sections[]'):
            sion = Sion.objects.get(id=sion_id)
            mission.sion_missions.add(sion)

        # Ajout des techniciens avec rôles
        technicien_ids = request.POST.getlist('technicien_id[]')
        roles_techniciens = request.POST.getlist('role_technicien[]')

        mission_has_technicien = MissionHasTechnicien.objects.create(mission=mission)
        for tech_id, role in zip(technicien_ids, roles_techniciens):
            tech = Technicien.objects.get(id=tech_id)
            mission_has_technicien.technicien.add(tech)
            MissionHasTechnicienRole.objects.create(
                missionhastechnicien=mission_has_technicien,
                technicien=tech,
                role=role
            )

        # Ajout des servants avec rôles
        servant_ids = request.POST.getlist('servant_id[]')
        roles_servants = request.POST.getlist('role_servant[]')

        mission_has_servant = MissionHasServant.objects.create(mission=mission)
        for servant_id, role in zip(servant_ids, roles_servants):
            servant = Servant.objects.get(id=servant_id)
            MissionHasServantRole.objects.create(
                missionhasservant=mission_has_servant,
                servant=servant,
                role=role
            )
 # Ajout des conducteur avec rôles
        conducteur_ids = request.POST.getlist('conducteur_id[]')
        roles_conducteurs = request.POST.getlist('role_conducteur[]')

        mission_has_conducteur = MissionHasConducteur.objects.create(mission=mission)
        for conducteur_id, role in zip(conducteur_ids, roles_conducteurs):
            conducteur = Conducteur.objects.get(id=conducteur_id)
            MissionHasConducteurRole.objects.create(
                missionhasconducteur=mission_has_conducteur,
                conducteur=conducteur,
                role=role
            )

        # Ajout des autres personnels avec rôles
        autre_noms = request.POST.getlist('autre_participant_nom[]')
        autre_prenoms = request.POST.getlist('autre_participant_prenom[]')
        autre_matricules = request.POST.getlist('autre_participant_matricule[]')
        autre_unites = request.POST.getlist('autre_participant_unite[]')
        autre_dates_arrivee = request.POST.getlist('autre_participant_date_arrivee[]')
        autre_dates_depart = request.POST.getlist('autre_participant_date_depart[]')
        autre_specialites = request.POST.getlist('autre_participant_specialite[]')
        autre_roles = request.POST.getlist('autre_participant_role[]')

        if autre_noms:  # S'il y a des autres personnels à ajouter
            mission_has_autre_personnel = MissionHasAutrePersonnel.objects.create(mission=mission)

            for i in range(len(autre_noms)):
                # Créer l'autre personnel
                autre_personnel = AutrePersonnel.objects.create(
                    nom=autre_noms[i] if i < len(autre_noms) else '',
                    prenom=autre_prenoms[i] if i < len(autre_prenoms) else '',
                    matricule=autre_matricules[i] if i < len(autre_matricules) and autre_matricules[i] else None,
                    unite=autre_unites[i] if i < len(autre_unites) and autre_unites[i] else None,
                    date_arrivee=autre_dates_arrivee[i] if i < len(autre_dates_arrivee) and autre_dates_arrivee[i] else None,
                    date_depart=autre_dates_depart[i] if i < len(autre_dates_depart) and autre_dates_depart[i] else None,
                    specialite=autre_specialites[i] if i < len(autre_specialites) and autre_specialites[i] else None,
                )

                # Ajouter à la relation many-to-many
                mission_has_autre_personnel.autre_personnel.add(autre_personnel)

                # Créer le rôle
                MissionHasAutrePersonnelRole.objects.create(
                    missionhasautrepersonnel=mission_has_autre_personnel,
                    autre_personnel=autre_personnel,
                    role=autre_roles[i] if i < len(autre_roles) else ''
                )

        return redirect('mission')  # page de liste des missions

    # Si GET, affichage du formulaire
    context = {
        'bataillons': Bion.objects.all(),
        'compagnies': Cie.objects.all(),
        'systemes' : Systeme.objects.all(),
        'sections': Sion.objects.all(),
        'techniciens': Technicien.objects.all(),
        'servants': Servant.objects.all(),
        'conducteurs': Conducteur.objects.all(),
        'autres_personnels': AutrePersonnel.objects.all(),
    }
    return render(request, 'pages/addmission.html', context)

from django.http import JsonResponse
from .models import Systeme, Sion, SionHasSysteme, Cie

@login_required
def get_systemes_by_cies(request):
    cie_ids = request.GET.getlist('cies[]')
    systemes_data = []

    if cie_ids:
        sions = Sion.objects.filter(cie__id__in=cie_ids).prefetch_related('systemes__systeme')
        for sion in sions:
            for relation in sion.systemes.all():
                systeme = relation.systeme
                systemes_data.append({
                    'id': systeme.id,
                    'nom': systeme.nom
                })
    else:
        # Si aucun cie sélectionné, retourner tous les systèmes
        all_systemes = Systeme.objects.all()
        for systeme in all_systemes:
            systemes_data.append({
                'id': systeme.id,
                'nom': systeme.nom
            })

    return JsonResponse({'systemes': systemes_data})






















@login_required
def kpi(request):
    return render(request, 'pages/kpi.html')


########################## Stock
@login_required
def edit_equipment(request):
    if request.method == 'POST':
       

        instance_id = request.POST.get('instance_id')
        numero_serie = request.POST.get('numero_serie')
        date_entree = request.POST.get('date_entree', '').strip()
        date_sortie = request.POST.get('date_sortie', '').strip()

        # Vérifier si les champs date sont valides avant d'enregistrer
        if date_entree == "":
            date_entree = None
        if date_sortie == "":
            date_sortie = None

        # Récupérer l'instance à modifier
        try:
            instance = EquipementStockInstance.objects.get(id=instance_id)
            # Mettre à jour les champs
            instance.numero_serie = numero_serie
            instance.date_entree = date_entree
            instance.date_sortie = date_sortie
            instance.save()

            messages.success(request, "Équipement mis à jour avec succès !")
            return redirect('bge')
        except EquipementStockInstance.DoesNotExist:
            messages.error(request, "L'équipement spécifié n'existe pas.")
            return redirect('bge')

    return HttpResponse("Méthode non autorisée", status=405)



 

def delete_equipment(request, instance_id):
    # Récupérer l'instance d'équipement
    equipement_instance = get_object_or_404(EquipementStockInstance, id=instance_id)

    # Obtenir l'objet StockHasEquipement associé
    stock_has_equipement = equipement_instance.stock_has_instance

    # Vérifier si la quantité est supérieure à 0 avant de la réduire
    if stock_has_equipement.quantite > 0:
        stock_has_equipement.quantite -= 1
        stock_has_equipement.save()  # Sauvegarder la nouvelle quantité

    # Supprimer l'instance d'équipement
    equipement_instance.delete()

    # Afficher un message de succès
    messages.success(request, "L'équipement a été supprimé avec succès !")

    # Rediriger vers la page d'affichage du stock ou une autre page appropriée
    return redirect("bge")  # Remplacer par le nom de ta page d'affichage du stock










from django.http import HttpResponse
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.colors import black, grey, lightgrey
from datetime import datetime
import textwrap  # Pour gérer les retours à la ligne

def export_pdf(request):
    # Création de la réponse HTTP avec le bon type de contenu
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'inline; filename="stock_bge.pdf"'
    
    # Création du document PDF
    p = canvas.Canvas(response, pagesize=A4)
    width, height = A4  # Largeur et hauteur de la page A4
    p.setFont("Helvetica", 10)

    # Titre
    p.drawString(200, height - 40, "Stock BGE - Export PDF")

    # Récupération des données
    stock_has_equipements = StockHasEquipement.objects.all()
    equipements_info = []
    for item in stock_has_equipements:
        equipement_instances = EquipementStockInstance.objects.filter(stock_has_instance=item)
        equipement_data = {
            'equipement': item.equipement,
            'sous_systeme': item.sous_systeme,  # Ajout de sous_systeme
            'stock': item.stock,                # Ajout de stock
            'quantite': item.quantite,
            'instances': equipement_instances,
        }
        equipements_info.append(equipement_data)

    # Position initiale sur la page
    y = height - 60  # Départ juste en dessous du titre

    # Calcul des largeurs des colonnes en fonction des données les plus longues
    col_widths = [
        max([len(str(item['equipement'].nom)) for item in equipements_info] + [len("Équipement")]) * 5,  # Équipement
        max([len(str(item['sous_systeme'].nom)) for item in equipements_info] + [len("Sous-système")]) * 5,  # Sous-système
        max([len(str(instance.numero_serie)) for item in equipements_info for instance in item['instances']] + [len("N° Série")]) * 5,  # N° Série
        max([len("Date Entrée"), len("Date Sortie")]) * 5,  # Date Entrée
        max([len("Date Entrée"), len("Date Sortie")]) * 5,  # Date Sortie
    ]
    # Normaliser les largeurs pour qu'elles soient compatibles avec A4
    total_width = sum(col_widths)
    if total_width > (width - 100):  # Laisser un espace de sécurité
        scaling_factor = (width - 100) / total_width
        col_widths = [int(width * scaling_factor) for width in col_widths]

    # Position initiale de la première colonne
    x_positions = [50]
    for i in range(1, len(col_widths)):
        x_positions.append(x_positions[i - 1] + col_widths[i - 1])

    # Ajouter les en-têtes
    headers = ["Équipement", "Sous-système", "N° Série", "Date Entrée", "Date Sortie"]
    p.setFillColor(lightgrey)  # Fond clair pour les en-têtes
    for i, header in enumerate(headers):
        p.drawString(x_positions[i], y, header)
    p.setFillColor(black)  # Rétablir la couleur noire pour le texte
    y -= 20  # Déplacement vers le bas

    # Dessiner une ligne horizontale sous les en-têtes
    p.setStrokeColor(black)
    p.line(50, y, width - 50, y)  # Ligne horizontale sous les en-têtes

    # Dessiner les lignes verticales sous les en-têtes
    for x_pos in x_positions[1:]:  # Commencer à partir de la deuxième colonne
        p.line(x_pos, y, x_pos, height - 60)  # Tracer une ligne verticale depuis l'en-tête jusqu'au haut de la page

    y -= 10  # Espace supplémentaire après la ligne

    # Boucle pour afficher les équipements et leurs instances
    for equipement_data in equipements_info:
        for instance in equipement_data['instances']:
            # Données de l'équipement
            data = [
                str(equipement_data['equipement'].nom),
                str(equipement_data['sous_systeme'].nom),
                str(instance.numero_serie),
                instance.date_entree.strftime('%d/%m/%Y') if instance.date_entree else "-",
                instance.date_sortie.strftime('%d/%m/%Y') if instance.date_sortie else "-"
            ]

            # Calculer la hauteur maximale de cette ligne (en fonction du nombre de lignes dans chaque cellule)
            max_lines_per_row = 1
            wrapped_data = []
            for i, value in enumerate(data):
                lines = textwrap.wrap(value, width=int(col_widths[i] / 5))
                wrapped_data.append(lines)
                max_lines_per_row = max(max_lines_per_row, len(lines))

            # Affichage des données avec gestion des retours à la ligne
            for line_index in range(max_lines_per_row):
                for i, lines in enumerate(wrapped_data):
                    if line_index < len(lines):
                        p.drawString(x_positions[i], y - (line_index * 10), lines[line_index])
                    else:
                        p.drawString(x_positions[i], y - (line_index * 10), "")  # Ligne vide si pas de texte

            # Dessiner une ligne horizontale après chaque entrée
            p.setStrokeColor(grey)
            p.line(50, y - (max_lines_per_row * 10) - 5, width - 50, y - (max_lines_per_row * 10) - 5)

            # Dessiner les lignes verticales après chaque ligne de données
            for x_pos in x_positions[1:]:
                p.line(
                    x_pos,
                    y - (max_lines_per_row * 10) - 5,
                    x_pos,
                    y + 15
                )  # Tracer une ligne verticale

            # Mettre à jour la position verticale
            y -= (max_lines_per_row * 10) + 15  # Déplacement vers le bas

            # Vérifier si on doit créer une nouvelle page
            if y < 50:
                p.showPage()
                p.setFont("Helvetica", 10)
                y = height - 60  # Réinitialiser la position

                # Redessiner les en-têtes sur la nouvelle page
                p.setFillColor(lightgrey)  # Fond clair pour les en-têtes
                for i, header in enumerate(headers):
                    p.drawString(x_positions[i], y, header)
                p.setFillColor(black)  # Rétablir la couleur noire pour le texte
                y -= 20  # Déplacement vers le bas

                # Dessiner une ligne horizontale sous les en-têtes
                p.setStrokeColor(black)
                p.line(50, y, width - 50, y)

                # Dessiner les lignes verticales sous les en-têtes
                for x_pos in x_positions[1:]:
                    p.line(x_pos, y, x_pos, height - 60)

                y -= 10  # Espace supplémentaire après la ligne

    # Sauvegarder et terminer le document
    p.save()
    return response

  

def ajouter_equipement(request):
    categories = SousSysteme.objects.all()
    equipements = Equipement.objects.all()
    stocks = Stock.objects.all()
  


    if request.method == "POST":
        # Récupérer les données soumises par l'utilisateur
        categorie_select = request.POST.get("categorie_select")
        nom= request.POST.get("nom_equipement")
        quantite = int(request.POST.get("quantité"))  # Assurez-vous que c'est un entier
        reference_entree = request.POST.get("reference_entree")
        date_entree = request.POST.get("date_entree")
        date_fin_garantie = request.POST.get("date_fin_garantie")
        numeros_serie = request.POST.getlist("numero_serie[]")
        stock_select = request.POST.get("stock_select")  # Une seule valeur de stock sélectionnée
        
        constructeur = request.POST.get('constructeur')
        product_number = request.POST.get('product_number')
        date_fin_garantie = request.POST.get('date_fin_garantie')
        mttr = request.POST.get('mttr')
        mtbf = request.POST.get('mtbf')
        criticite = request.POST.get('criticite')
        media = request.FILES.get('media')
 
  # Récupérer ou créer les objets liés à l'équipement et au stock
        try:
            # Récupérer l'objet sous-système, l'équipement et le stock
            sous_systeme = SousSysteme.objects.get(id=categorie_select)
            
            stock = Stock.objects.get(id=stock_select)
        except SousSysteme.DoesNotExist:
            return HttpResponse("La catégorie sélectionnée n'existe pas.", status=404)
    
        except Stock.DoesNotExist:
            return HttpResponse("Le stock sélectionné n'existe pas.", status=404)


   # Vérifier si l'équipement existe déjà dans la base de données
        equipement = Equipement.objects.filter(
            nom=nom, 
            constructeur=constructeur, 
            product_numbre=product_number
        ).first()

        if not equipement:
            # Si l'équipement n'existe pas, on le crée
            equipement = Equipement.objects.create(
                nom=nom,
                constructeur=constructeur,
                product_numbre=product_number,
                date_fin_garantie=date_fin_garantie,
                mttr=mttr,
                mtbf=mtbf,
                criticite=criticite,
                media=media,
            )



   # Vérifier si une entrée StockHasEquipement existe déjà
        stock_has_equipement, created = StockHasEquipement.objects.get_or_create(
            sous_systeme=sous_systeme,
            equipement=equipement,
            stock=stock,
            defaults={'quantite': 0}  # Valeur par défaut si l'entrée est créée
        )
 # Vérifier que la quantité d'instances à créer correspond au nombre de numéros de série
        if len(numeros_serie) != quantite:
            return HttpResponse("Le nombre de numéros de série ne correspond pas à la quantité entrée.", status=400)

        # Mettre à jour la quantité si l'équipement existe déjà
        stock_has_equipement.quantite += quantite
        stock_has_equipement.save()

        # Ajouter les nouvelles instances avec numéro de série
        for num_serie in numeros_serie:
            EquipementStockInstance.objects.create(
                stock_has_instance=stock_has_equipement,
                numero_serie=num_serie,
                date_entree=date_entree,
                date_fin_garantie=date_fin_garantie,
                reference_entree=reference_entree
            )

        return redirect('bge')  # Redirection après l'ajout réussi

    return render(request, 'pages/add_new_equipement.html', {
        'categories': categories,
        'stocks': stocks,
    })
     



def ajouter_quantite_stock(request):
    categories = SousSysteme.objects.all()
    equipements = Equipement.objects.all()
    stocks = Stock.objects.all()

    if request.method == "POST":
        # Récupérer les valeurs soumises par l'utilisateur
        categorie_select = request.POST.get("categorie_select")
        equipement_select = request.POST.get("equipement_select")
        quantite = int(request.POST.get("quantité"))  # Assurez-vous que c'est un entier
        reference_entree = request.POST.get("reference")
        date_entree = request.POST.get("date_entree")
        date_fin_garantie = request.POST.get("date_fin_garantie")
        numeros_serie = request.POST.getlist("numero_serie[]")
        stock_select = request.POST.get("stock_select")  # Une seule valeur de stock sélectionnée

        try:
            # Récupérer l'objet sous-système, l'équipement et le stock
            sous_systeme = SousSysteme.objects.get(id=categorie_select)
            equipement = Equipement.objects.get(id=equipement_select)
            stock = Stock.objects.get(id=stock_select)
        except SousSysteme.DoesNotExist:
            return HttpResponse("La catégorie sélectionnée n'existe pas.", status=404)
        except Equipement.DoesNotExist:
            return HttpResponse("L'équipement sélectionné n'existe pas.", status=404)
        except Stock.DoesNotExist:
            return HttpResponse("Le stock sélectionné n'existe pas.", status=404)

        # Récupérer ou créer l'instance de StockHasEquipement
        stock_has_equipement, created = StockHasEquipement.objects.get_or_create(
            sous_systeme=sous_systeme,
            equipement=equipement,
            stock=stock,
             
        )
 # Vérifier si la quantité d'instances à créer est correcte par rapport au nombre de numéros de série
        if len(numeros_serie) != quantite:
            return HttpResponse("Le nombre de numéros de série ne correspond pas à la quantité entrée.", status=400)

        # Si l'instance existe déjà, mettez à jour la quantité
        stock_has_equipement.quantite = quantite
        stock_has_equipement.save()

        # Créer les instances d'équipement avec les numéros de série
        for num_serie in numeros_serie:
            EquipementStockInstance.objects.create(
                stock_has_instance=stock_has_equipement,
                numero_serie=num_serie,
                date_entree=date_entree,
                
                reference_entree=reference_entree
            )

        return redirect('bge')  # Redirige vers une page de succès ou autre

    else:
        return render(request, 'pages/add_quantity.html', {
            'categories': categories,
            'equipements': equipements,
            'stocks': stocks
        })
    

@login_required
def correctivebyId(request, id):
    equipement_en_panne = get_object_or_404(Maintenance, id=id)
    equipement = equipement_en_panne.equipement
    instance_equipement = equipement_en_panne.equipementsystemeInstance
    sous_systeme = equipement_en_panne.sous_systeme
    systeme = equipement_en_panne.systeme
    bion = equipement_en_panne.bion
    cie = equipement_en_panne.cie
    sion = equipement_en_panne.sion
   
    if request.method == 'POST':
        form = MaintenanceForm(request.POST, instance=equipement_en_panne)
        if form.is_valid():
            # Sauvegarder les modifications sans créer une nouvelle ligne
            maintenance = form.save(commit=False)
            maintenance.type_maintenance = 'CORRECTIVE'
            maintenance.equipement = equipement  # On garde le même équipement
            maintenance.equipementsystemeInstance = instance_equipement
            maintenance.systeme = systeme
            maintenance.sous_systeme = sous_systeme
            maintenance.bion = bion
            maintenance.cie = cie
            maintenance.sion = sion
            maintenance.save()  # Sauvegarde effective
            
            # Traitement des fichiers image uploadés
            if request.FILES.getlist('media'):
               for f in request.FILES.getlist('media'):
                    media_obj = Media.objects.create(image=f)
                    maintenance.media.add(media_obj)
            # Mettre à jour l'équipement de stock s'il y a un remplacement
            equipement_de_rechange = form.cleaned_data.get('equipement_de_rechange')
            date_sortie_stock = form.cleaned_data.get('date_sortie_stock')
            description_panne = form.cleaned_data.get('description')

            if equipement_de_rechange:
                    equipement_de_rechange.destination = equipement_en_panne.systeme.nom
                    equipement_de_rechange.date_sortie = date_sortie_stock
                    equipement_de_rechange.save()

                    # Ajout d'un nouvel enregistrement à EquipementSystemeInstance
                    nouvel_equipement_instance = EquipementSystemeInstance.objects.create(
                        numero_serie=equipement_de_rechange.numero_serie,
                        date_installation=date_sortie_stock,
                        destination=equipement_en_panne.systeme.nom,
                        sous_systeme_has_instance=instance_equipement.sous_systeme_has_instance,
                        statut='OPERATIONNEL'
                    )

                     # Associer la nouvelle instance à la maintenance
                    maintenance.equipement_de_rechange = equipement_de_rechange
                    maintenance.save()

 


            # Mise à jour de l'instance de l'équipement en panne
            date_maintenance = form.cleaned_data.get('date_maintenance')
            equipement_systeme_instance = EquipementSystemeInstance.objects.filter(id=instance_equipement.id).first()
            if equipement_systeme_instance:
                equipement_systeme_instance.destination = 'vers réparation'
                equipement_systeme_instance.date_remplacement = date_maintenance
                equipement_systeme_instance.save()


               
            nouvel_equipement = MaintenanceHistory.objects.create(
                equipement=equipement_systeme_instance.sous_systeme_has_instance.equipement,
                equipementsystemeInstance=instance_equipement,
                systeme=systeme,
                date_panne=date_maintenance,
                maintenance=maintenance,
                statut="EN PANNE",
                 
                description_panne=description_panne if description_panne else None
            )
            nouvel_equipement.save()

            # Mettre à jour les quantités dans StockHasEquipement
            stock_has_equipements = StockHasEquipement.objects.all()
            for item in stock_has_equipements:
                qte = 0  # Réinitialiser qte pour chaque item
                equipement_instances = EquipementStockInstance.objects.filter(stock_has_instance=item)
                for equip_instance in equipement_instances:
                    if equip_instance.date_sortie is None and item.equipement == equip_instance.stock_has_instance.equipement:
                        qte += 1
                item.quantite = qte
                item.save()

            # Mettre à jour les quantités dans SousSystemeHasEquipement
            sous_systeme_has_equipements = SousSystemeHasEquipement.objects.all()
            for item in sous_systeme_has_equipements:
                qte = 0  # Réinitialiser qte pour chaque item
                equipement_instances = EquipementSystemeInstance.objects.filter(sous_systeme_has_instance=item)
                for equip_instance in equipement_instances:
                    if equip_instance.date_remplacement is None and item.equipement == equip_instance.sous_systeme_has_instance.equipement:
                        qte += 1
                item.quantite = qte
                item.save()

            return redirect('intervention')  # Redirection après modification
    else:
        equipement_en_panne.type_maintenance = 'CORRECTIVE'
        form = MaintenanceForm(instance=equipement_en_panne)


    return render(request, 'pages/correctivebyId.html', {'form': form, 'equipement_en_panne': equipement_en_panne})
 


@csrf_exempt  # Utilisez ceci avec prudence en production
def mark_notification_as_read(request, notification_id):
    if request.method == "POST":
        try:
            # Récupérer la notification
            notif = Notification.objects.get(id=notification_id)
         
            # Marquer la notification comme lue
            notif.is_read = True
            notif.save()
            return JsonResponse({"success": True})
        except Notification.DoesNotExist:
            return JsonResponse({"success": False, "error": "Notification non trouvée"})
    return JsonResponse({"success": False, "error": "Méthode non autorisée"})



def fetch_notifications(request):
    Notification.objects.filter(is_read=True).delete()
    notifications = Notification.objects.filter(is_read=False).order_by("-id")
    notifications_list = [{"id": notif.id, "message": notif.message, "is_read": notif.is_read} for notif in notifications]
    unread_notifications = Notification.objects.filter(is_read=False).order_by("-id")
    
    return JsonResponse({"notifications": notifications_list,
                          "count": unread_notifications.count() })



 
def interventions_par_mois(request, technicien_id):
    # Récupérer le technicien
    technicien = get_object_or_404(Technicien, id=technicien_id)

    # Récupérer les interventions groupées par année et mois
    interventions = (
        Maintenance.objects.filter(technicien=technicien)
        .exclude(date_maintenance__isnull=True)  # Exclure les dates NULL
        .annotate(
            year=ExtractYear('date_maintenance'),
            month=ExtractMonth('date_maintenance')
        )
        .values('year', 'month')
        .annotate(total=Count('id'))
        .order_by('year', 'month')
    )

    # Vérifier si des interventions ont été trouvées
    if not interventions:
        context = {
            'technicien': technicien,
            'mois_labels': json.dumps([]),  # Encodage JSON pour JavaScript
            'interventions_data': json.dumps([]),  # Encodage JSON pour JavaScript
        }
        return render(request, 'pages/fiche_personnel.html', context)

    # Initialiser un dictionnaire pour stocker les données par année et mois
    data_by_year = {}

    # Remplir le dictionnaire avec des zéros pour chaque mois de chaque année
    for entry in interventions:
        year = entry['year']
        if year not in data_by_year:
            data_by_year[year] = {month: 0 for month in range(1, 13)}  # Initialiser tous les mois à 0

    # Remplir les données existantes
    for entry in interventions:
        year = entry['year']
        month = entry['month']
        total = entry['total']
        data_by_year[year][month] = total

    # Convertir le dictionnaire en listes pour le graphique
    mois_labels = []
    interventions_data = []

    # Parcourir les années et les mois dans l'ordre
    for year in sorted(data_by_year.keys()):
        for month in range(1, 13):  # De janvier (1) à décembre (12)
            mois_labels.append(f"{calendar.month_name[month]} {year}")
            interventions_data.append(data_by_year[year][month])

    # Contexte pour le template
    context = {
        'technicien': technicien,
        'mois_labels': json.dumps(mois_labels),  # Encodage JSON pour JavaScript
        'interventions_data': json.dumps(interventions_data),  # Encodage JSON pour JavaScript
    }

    return render(request, 'pages/fiche_personnel.html', context)



def fiche_systeme(request):
    return render(request, 'pages/fiche_systeme.html')



def historique_stock(request):
    equipements_instance_stock = EquipementStockInstance.objects.filter(date_sortie__isnull=False)
    context = {
       'equipements_instance_stock'  :equipements_instance_stock,
    }
    return render(request, 'pages/historique_stock.html',context)


 
from django.shortcuts import get_object_or_404, redirect, render
from django.contrib.auth.decorators import login_required
from .models import EquipementSystemeInstance, Maintenance
from .forms import MaintenanceForm

@login_required
def preventivebyId(request, id1, id2):
    # Récupérer l'instance d'équipement (attention au champ _numero_serie)
    instance_equipement = get_object_or_404(
        EquipementSystemeInstance,
        sous_systeme_has_instance_id=id1,
        date_remplacement__isnull=True,
        _numero_serie=id2,  # ⚠️ champ réel de la base
    )

    type_maintenance = 'Preventive'

    if request.method == 'POST':
        form = MaintenanceForm(request.POST, request.FILES)
        if form.is_valid():
            maintenance = form.save(commit=False)
            maintenance.type_maintenance = type_maintenance
            maintenance.equipementsystemeInstance = instance_equipement
            maintenance.sous_systeme = instance_equipement.sous_systeme_has_instance.sous_systeme
            maintenance.systeme = instance_equipement.sous_systeme_has_instance.systeme
            maintenance.equipement = instance_equipement.sous_systeme_has_instance.equipement
            maintenance.date_maintenance = form.cleaned_data['date_maintenance']
            maintenance.duree = form.cleaned_data['duree']
            maintenance.description = form.cleaned_data['description']
            maintenance.technicien = form.cleaned_data['technicien']
            maintenance.bion = form.cleaned_data['bion']
            maintenance.sion = form.cleaned_data['sion']
            maintenance.cie = form.cleaned_data['cie']
            maintenance.statut = form.cleaned_data['statut_equipement']
            maintenance.save()

            # Associer les images
            for f in request.FILES.getlist('media'):
                media_obj = Media.objects.create(image=f)
                maintenance.media.add(media_obj)

            # Lien technicien-maintenance
            TechnicienHasMaintenance.objects.create(
                maintenance=maintenance,
                technicien=form.cleaned_data['technicien']
            )

            # Mise à jour de l’instance de l’équipement
            instance_equipement.date_derniere_maintenance = form.cleaned_data['date_maintenance']
            instance_equipement.statut = form.cleaned_data['statut_equipement']
            instance_equipement.save()

            return redirect('index')
    else:
        initial_data = {
            'type_maintenance': type_maintenance,
            'statut_sous_systeme': 'OPS',
            'statut_equipement': 'OPS',
            'sous_systeme': instance_equipement.sous_systeme_has_instance.sous_systeme,
            'systeme': instance_equipement.sous_systeme_has_instance.systeme,
            'equipement': instance_equipement.sous_systeme_has_instance.equipement,
        }
        form = MaintenanceForm(initial=initial_data)

    return render(request, 'pages/preventivebyId.html', {
        'instance_equipement': instance_equipement,
        'form': form,
        'type_maintenance': type_maintenance,
    })

def fiche_suivie_reparation(request, id):
   # Récupérer l'objet Maintenance en fonction de l'ID
    equipement = get_object_or_404(MaintenanceHistory, id=id)
     
    try:
        equipement_media = equipement.equipement.media.url if equipement.equipement.media else None
    except ValueError:
        equipement_media = None
    

    timeline = [
        {
            "date": equipement.date_panne or "Non spécifiée",
            "description": equipement.description_panne or "Aucune description disponible",
            "type": "Date de panne"
        },
        {
            "date": equipement.date_entree_atelier or "Non spécifiée",
            "description": equipement.description_entree_atelier or "Aucune description disponible",
            "type": "Date d'entrée en atelier"
        },
       
        {
            "date": equipement.date_envoi_reparation_externe or "Non spécifiée",
            "description": equipement.description_envoi_reparation_externe or "Aucune description disponible",
            "type": "Date de réparation externe"
        },
         {
            "date": equipement.date_reparation or "Non spécifiée",
            "description": equipement.description_reparation or "Aucune description disponible",
            "type": "Date de réparation"
        },
    ]

   
    context = {
        'timeline':timeline,
        
         'equipement': equipement, 
         'equipement_media': equipement_media, 
          
    }
    return render(request, 'pages/fiche_suivie_reparation.html', context)
 

from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from .forms import AtelierForm, ReparationExterneForm, DetailsReparationForm
from .models import MaintenanceHistory

 
from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from .forms import AtelierForm, ReparationExterneForm, DetailsReparationForm
from .models import MaintenanceHistory

def create_maintenance_history(request, id):
    maintenance_instance = get_object_or_404(MaintenanceHistory, id=id)

    # Comparer les dates pour obtenir l'étape la plus récente
    etapes = [
        {'date': maintenance_instance.date_entree_atelier, 'statut': maintenance_instance.statut_atelier},
        {'date': maintenance_instance.date_envoi_reparation_externe, 'statut': maintenance_instance.statut_externe},
        {'date': maintenance_instance.date_reparation, 'statut': maintenance_instance.statut_reparation},
    ]
    etapes = [etape for etape in etapes if etape['date'] is not None]  # Filtrer les étapes avec une date renseignée
    if etapes:
        etape_plus_recente = max(etapes, key=lambda x: x['date'])  # Trouver l'étape avec la date la plus récente
        statut_plus_recent = etape_plus_recente['statut']
    else:
        statut_plus_recent = None

    # Mettre à jour le champ `statut` de l'instance avec le statut de l'étape la plus récente
    if statut_plus_recent and maintenance_instance.statut != statut_plus_recent:
        maintenance_instance.statut = statut_plus_recent
        maintenance_instance.save()
    # Déterminer si une étape est en mode édition (via un paramètre GET)
    edit_step = request.GET.get('edit_step', None)

    if request.method == 'POST':
        current_step = request.POST.get('current_step')

        if current_step == 'atelier':
            form = AtelierForm(request.POST, instance=maintenance_instance)
            if form.is_valid():
                form.save()
                messages.success(request, "Les informations pour l'étape 'Entrée en Atelier' ont été mises à jour.")
                return redirect('create_maintenance_history', id=id)

        elif current_step == 'externe':
            form = ReparationExterneForm(request.POST, instance=maintenance_instance)
            if form.is_valid():
                form.save()
                messages.success(request, "Les informations pour l'étape 'Réparation Externe' ont été mises à jour.")
                return redirect('create_maintenance_history', id=id)

        elif current_step == 'reparation':
            form = DetailsReparationForm(request.POST, instance=maintenance_instance)
            if form.is_valid():
                form.save()
                messages.success(request, "Les informations pour l'étape 'Détails de la Réparation' ont été mises à jour.")
                return redirect('create_maintenance_history', id=id)

        elif current_step == 'final':
            messages.success(request, "Toutes les étapes ont été validées avec succès.")
            return redirect('panne')  # Redirection vers une page de succès

    else:
        # Pré-remplir les formulaires avec les données existantes
        atelier_form = AtelierForm(instance=maintenance_instance)
        repar_externe_form = ReparationExterneForm(instance=maintenance_instance)
        details_reparation_form = DetailsReparationForm(instance=maintenance_instance)

    # Indicateurs pour savoir si chaque étape est déjà remplie
    is_atelier_filled = all([
        maintenance_instance.date_entree_atelier,
        maintenance_instance.description_entree_atelier,
        maintenance_instance.lieu_reparation,
        maintenance_instance.statut_atelier != 'pending'
    ])
    is_externe_filled = all([
        maintenance_instance.date_envoi_reparation_externe,
        maintenance_instance.description_envoi_reparation_externe,
        maintenance_instance.lieu_reparation_externe,
        maintenance_instance.statut_externe != 'pending'
    ])
    is_reparation_filled = all([
        maintenance_instance.date_reparation,
        maintenance_instance.description_reparation,
        maintenance_instance.statut_reparation != 'pending'
    ])

    context = {
        'maintenance_instance': maintenance_instance,
        'atelier_form': atelier_form,
        'repar_externe_form': repar_externe_form,
        'details_reparation_form': details_reparation_form,
        'is_atelier_filled': is_atelier_filled,
        'is_externe_filled': is_externe_filled,
        'is_reparation_filled': is_reparation_filled,
        'edit_step': edit_step,  # Passer l'étape en mode édition
    }
    return render(request, 'pages/suivie_reparation.html', context)




 
from django.shortcuts import get_object_or_404, render
from .models import Mission, MissionHasTechnicienRole, MissionHasServantRole
def fiche_mission(request, id):
    mission = get_object_or_404(Mission, id=id)

    technicien_roles = MissionHasTechnicienRole.objects.filter(
        missionhastechnicien__mission=mission
    ).select_related('technicien')

    servant_roles = MissionHasServantRole.objects.filter(
        missionhasservant__mission=mission
    ).select_related('servant')

    # Préparer les unités à afficher en respectant ta logique
    cie_list = list(mission.cie_missions.all())
    sion_list = list(mission.sion_missions.all())
    bion = mission.bion_mission

    afficher_bion = False
    afficher_cies = []
    afficher_sions = []

    if bion and not cie_list and not sion_list:
        afficher_bion = True  # cas 1 : seulement le bion est spécifié
    elif bion and cie_list:
        # Vérifier si toutes les cie du bion sont sélectionnées
        if list(bion.cies.all()) == cie_list and not sion_list:
            afficher_bion = True  # cas 2 : toutes les cie de ce bion sont sélectionnées
        else:
            afficher_cies = cie_list
    elif cie_list:
        afficher_cies = cie_list

    if sion_list:
        afficher_sions = sion_list

    context = {
        'mission': mission,
        'technicien_roles': technicien_roles,
        'servant_roles': servant_roles,
        'afficher_bion': bion if afficher_bion else None,
        'afficher_cies': afficher_cies,
        'afficher_sions': afficher_sions,
    }

    return render(request, 'pages/fiche_mission.html', context)


@admin_or_user_required 
def edit_personnel(request):
    # Vérifier si la méthode HTTP est POST
    if request.method == 'POST':
        # Récupérer les données du formulaire
        instance_id = request.POST.get('instance_id')
        nom = request.POST.get('nom')
        prenom = request.POST.get('prenom')
        matricule = request.POST.get('matricule')
        date_affectation = request.POST.get('date_affectation', '').strip()
        specialite = request.POST.get('specialite')
        grade = request.POST.get('grade')

      

        # Vérifier si le matricule existe dans la table Servant ou Technicien
        try:
            # Essayer de récupérer l'instance dans la table Servant
            instance = Servant.objects.get(matricule=matricule)
        except Servant.DoesNotExist:
            try:
                # Si non trouvé dans Servant, essayer dans Technicien
                instance = Technicien.objects.get(matricule=matricule)
            except Technicien.DoesNotExist:
                # Si le matricule n'existe ni dans Servant ni dans Technicien
                messages.error(request, f"Le matricule {id} n'existe pas.")
                return redirect('personnel')

        # Mettre à jour uniquement les champs modifiés
        if nom and nom != instance.nom:
            instance.nom = nom
        if prenom and prenom != instance.prenom:
            instance.prenom = prenom
        if matricule and matricule != instance.matricule:
            instance.matricule = matricule
        if date_affectation != instance.date_affectation:
            instance.date_affectation = date_affectation
        if specialite and specialite != instance.specialite:
            instance.specialite = specialite
        if grade and grade != instance.grade:
            instance.grade = grade

        # Sauvegarder les modifications
        instance.save()

        # Ajouter un message de succès
        messages.success(request, f"{instance.nom} a été mis à jour avec succès !")
        return redirect('personnel')

    # Si la méthode n'est pas POST, retourner une erreur
    return HttpResponse("Méthode non autorisée", status=405)





def esmbyid(request,id):
       
         
        return render(request, 'pages/esmbyid.html',{'id':id})



 
 

from django.db.models import Q

def matricule_officier(prefix):
    try:
        # Récupérer tous les matricules existants contenant le préfixe
        technicien_matricules = Technicien.objects.filter(matricule__startswith=prefix).values_list('matricule', flat=True)
        servant_matricules = Servant.objects.filter(matricule__startswith=prefix).values_list('matricule', flat=True)
        all_matricules = list(technicien_matricules) + list(servant_matricules)

        existing_numbers = []
        for matricule in all_matricules:
            try:
                number = int(matricule[len(prefix):])  # Extraire le numéro après le préfixe
                existing_numbers.append(number)
            except ValueError:
                continue  # Ignorer les matricules mal formatés

        next_number = max(existing_numbers) + 1 if existing_numbers else 1

        return f"{prefix}{next_number}"

    except Exception as e:
        raise ValueError(f"Erreur lors de la génération du matricule : {str(e)}")

 
def addpersonnel(request):
    if request.method == 'POST':
        # Récupérer les données du formulaire
        fonction = request.POST.get('fonction')  # "servant" ou "technicien"
        nom = request.POST.get('nom')
        prenom = request.POST.get('prenom')
        grade = request.POST.get('grade', '').strip()
        date_affectation = request.POST.get('date_affectation', '').strip() or None
        specialite = request.POST.get('specialite')

        # Récupérer les tableaux pour les formations
        intitules_formation = request.POST.getlist('Intituleformation[]')
        references_formation = request.POST.getlist('reference[]')
        dates_debut = request.POST.getlist('date_debut[]')
        dates_fin = request.POST.getlist('date_fin[]')
        formation_ids = request.POST.getlist('formation_id[]')

        # Récupérer le matricule saisi par l'utilisateur
        user_matricule = request.POST.get('matricule', '').strip()

        try:
            # Vérifier si l'utilisateur a entré "PO"
            if user_matricule == "PO":
                matricule = matricule_officier("PO")  # Utiliser votre fonction matricule_officier
            else:
                # Vérifier si le matricule saisi existe déjà
                if Technicien.objects.filter(matricule=user_matricule).exists() or Servant.objects.filter(matricule=user_matricule).exists():
                    messages.error(request, f"Le matricule '{user_matricule}' existe déjà.")
                    return render(request, 'pages/addpersonnel.html')
                matricule = user_matricule  # Utiliser le matricule saisi tel quel

            # Créer un Technicien ou un Servant en fonction de la fonction sélectionnée
            if fonction == 'technicien':
                personnel = Technicien.objects.create(
                    nom=nom,
                    prenom=prenom,
                    grade=grade,
                    matricule=matricule,
                    date_affectation=date_affectation,
                    specialite=specialite
                )
                FormationModel = TechnicienHasFormation  # Modèle de formation pour technicien
            elif fonction == 'servant':
                personnel = Servant.objects.create(
                    nom=nom,
                    prenom=prenom,
                    grade=grade,
                    matricule=matricule,
                    date_affectation=date_affectation,
                    specialite=specialite
                )
                FormationModel = ServantHasFormation  # Modèle de formation pour servant
            else:
                messages.error(request, "Fonction non valide.")
                return render(request, 'pages/addpersonnel.html')

            # Créer les formations associées
            for i in range(len(intitules_formation)):
                intitule = intitules_formation[i]
                reference = references_formation[i]
                date_debut = dates_debut[i]
                date_fin = dates_fin[i]
                formation_id = formation_ids[i]

                # Vérifier que les champs obligatoires sont remplis
                if not all([intitule, reference, date_debut, date_fin, formation_id]):
                    messages.warning(request, f"Données manquantes pour la formation {i + 1}.")
                    continue  # Passer à la prochaine formation

                # Vérifier que la formation existe dans la base de données
                try:
                    formation = Formation.objects.get(id=formation_id)
                except Formation.DoesNotExist:
                    messages.error(request, f"La formation avec l'ID {formation_id} n'existe pas.")
                    continue

                # Créer l'association entre le personnel et la formation
                FormationModel.objects.create(
                    **{f"{fonction}_id": personnel.id},  # Utiliser dynamiquement le bon champ (technicien_id ou servant_id)
                    formation=formation,
                    reference=reference,
                    date_debut=date_debut,
                    date_fin=date_fin
                )

            # Message de succès
            messages.success(request, f"{fonction.capitalize()} ajouté avec succès.")
            return redirect('personnel')

        except Exception as e:
            # Gérer les erreurs lors de l'enregistrement
            messages.error(request, f"Une erreur est survenue : {str(e)}")
            return render(request, 'pages/addpersonnel.html')

    # Si la méthode n'est pas POST, afficher simplement le formulaire
    formations = Formation.objects.all()
    return render(request, 'pages/addpersonnel.html', {'formations': formations})

 
from django.shortcuts import get_object_or_404, redirect
from django.contrib import messages
from .models import Servant, Technicien, MissionHasServant, ServantHasFormation, TechnicienHasFormation, TechnicienHasMaintenance

def delete_personnel(request, matricule):
  
    servant = None
    technicien = None

    try:
        # Vérifie si le matricule appartient à un Servant
        servant = Servant.objects.get(matricule=matricule)
    except Servant.DoesNotExist:
        try:
            # Sinon, vérifie si le matricule appartient à un Technicien
            technicien = Technicien.objects.get(matricule=matricule)
        except Technicien.DoesNotExist:
            messages.error(request, "Aucun personnel trouvé avec ce matricule.")
            return redirect('personnel')

    try:
        if servant:
            # Suppression des relations dépendantes pour Servant
            MissionHasServant.objects.filter(servant=servant).delete()
            ServantHasFormation.objects.filter(servant=servant).delete()

            # Suppression du Servant lui-même
            servant.delete()
            messages.success(request, f"Le servant {servant} a été supprimé avec succès.")

        elif technicien:
            # Suppression des relations dépendantes pour Technicien
            TechnicienHasFormation.objects.filter(technicien=technicien).delete()
            TechnicienHasMaintenance.objects.filter(technicien=technicien).delete()

            # Suppression du Technicien lui-même
            technicien.delete()
            messages.success(request, f"Le technicien {technicien} a été supprimé avec succès.")

    except Exception as e:
        messages.error(request, f"Une erreur est survenue lors de la suppression : {str(e)}")

    return redirect('personnel')  # Redirection vers une vue de liste des personnels