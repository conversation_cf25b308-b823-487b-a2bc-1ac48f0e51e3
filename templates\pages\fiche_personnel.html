{% extends 'base.html' %}
{% load static %}

{% block content %}
<section style="background-color: #eee;">
    <div class="container py-5">
        <div class="row">
            <!-- Section de gauche : Photo et informations générales -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-body text-center">
                    {% if info_technicien and info_technicien.photo %}
    <img src="{{ info_technicien.photo.url }}" alt="avatar"
         class="rounded-circle img-fluid object-cover"
         style="width: 200px; height: 200px; object-fit: cover;">
{% elif info_servant and info_servant.photo %}
    <img src="{{ info_servant.photo.url }}" alt="avatar"
         class="rounded-circle img-fluid object-cover"
         style="width: 200px; height: 200px; object-fit: cover;">
{% else %}
    <img src="{% static 'img/soldier.png' %}" alt="avatar"
         class="rounded-circle img-fluid object-cover"
         style="width: 200px; height: 200px; object-fit: cover;">
{% endif %}

                        {% if info_servant %}
                            <h5 class="my-3">{{ info_servant.nom }} {{ info_servant.prenom }}</h5>
                            <p class="text-muted mb-1">Opérateur</p>
                        {% elif info_technicien %}
                            <h5 class="my-3">{{ info_technicien.nom }} {{ info_technicien.prenom }}</h5>
                            <p class="text-muted mb-1">Technicien</p>
                        {% else %}
                            <h5 class="my-3">Personnel inconnu</h5>
                            <p class="text-muted mb-1">Rôle non spécifié</p>
                        {% endif %}

                        <div class="d-flex justify-content-center flex-wrap gap-2 mt-3">
                            {% for info in info_formation_servant %}
                                <div class="badge-container">
                                    <img class="rounded img-fluid badge-img" alt="badge" src="{{ info.formation.badge.url }}" style="width: 50px; height: 50px;" />
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section de droite : Détails du personnel -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-3"><p class="mb-0">Nom</p></div>
                            <div class="col-sm-9">
                                <p class="text-muted mb-0">
                                    {% if info_servant %}{{ info_servant.nom }} {{ info_servant.prenom }}
                                    {% elif info_technicien %}{{ info_technicien.nom }} {{ info_technicien.prenom }}
                                    {% else %}Non spécifié{% endif %}
                                </p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-3"><p class="mb-0">Grade</p></div>
                            <div class="col-sm-9">
                                <p class="text-muted mb-0">
                                    {% if info_servant %}{{ info_servant.grade|default:"Non spécifié" }}
                                    {% elif info_technicien %}{{ info_technicien.grade|default:"Non spécifié" }}
                                    {% else %}Non spécifié{% endif %}
                                </p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-3"><p class="mb-0">Matricule</p></div>
                            <div class="col-sm-9">
                                <p class="text-muted mb-0">
                                    {% if info_servant %}{{ info_servant.matricule|default:"Non spécifié" }}
                                    {% elif info_technicien %}{{ info_technicien.matricule|default:"Non spécifié" }}
                                    {% else %}Non spécifié{% endif %}
                                </p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-3"><p class="mb-0">Spécialité</p></div>
                            <div class="col-sm-9">
                                <p class="text-muted mb-0">
                                    {% if info_servant %}{{ info_servant.specialite|default:"Non spécifié" }}
                                    {% elif info_technicien %}{{ info_technicien.specialite|default:"Non spécifié" }}
                                    {% else %}Non spécifié{% endif %}
                                </p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-sm-3"><p class="mb-0">Date d'affectation</p></div>
                            <div class="col-sm-9">
                                <p class="text-muted mb-0">
                                    {% if info_servant %}{{ info_servant.date_affectation|default:"Non spécifié" }}
                                    {% elif info_technicien %}{{ info_technicien.date_affectation|default:"Non spécifié" }}
                                    {% else %}Non spécifié{% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphique -->
                <div class="card mb-4">
                    <div class="card-body">
                        <canvas id="myBarChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    var mois_labels = JSON.parse("{{ mois_labels|escapejs }}".replace(/'/g, '"'));
    var interventions_data = JSON.parse('{{ intervention_data|escapejs }}');
    var mission_data = JSON.parse('{{ mission_data|escapejs }}');

    Chart.defaults.global.defaultFontFamily = '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
    Chart.defaults.global.defaultFontColor = '#292b2c';

    var ctx = document.getElementById("myBarChart");
    var myBarChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: mois_labels,
            datasets: [
                {
                    label: "Interventions Techniques",
                    backgroundColor: "rgba(2,117,216,0.8)",
                    borderColor: "rgba(2,117,216,1)",
                    data: interventions_data,
                },
                {
                    label: "Missions Opérationnelles",
                    backgroundColor: "rgba(255,165,0,0.7)",
                    borderColor: "rgba(255,165,0,1)",
                    data: mission_data,
                }
            ]
        },
        options: {
            scales: {
                xAxes: [{
                    gridLines: { display: true },
                    ticks: { maxTicksLimit: 12 }
                }],
                yAxes: [{
                    ticks: { beginAtZero: true },
                    gridLines: { display: true }
                }]
            },
            legend: { display: true }
        }
    });
</script>

{% endblock %}
