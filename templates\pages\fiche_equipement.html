{% extends 'base.html' %}

{% block content %}
<style>
    #datatablesSimple td,
    #datatablesSimple th,
    #systemes-table td,
    #systemes-table th {
        text-align: center !important;
        vertical-align: middle !important;
    }

 #equipement-img {
    max-width: 100%;           /* ne dépasse pas la largeur du conteneur */
    max-height: 200px;         /* fixe la hauteur maximale dans la carte */
    object-fit: contain;       /* garde les proportions sans déformation */
    display: block;
    margin-left: auto;
    margin-right: auto;
    cursor: pointer;
}
</style>

<div class="row justify-content-center">
    <div class="col-xl-3" style="margin-top: 20px; margin-left: 20px;">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-shield-alt me-1"></i>
            </div>
            <div class="card-body">
                <img class="rounded mx-auto d-block img-fluid" 
                     id="equipement-img" 
                     alt="equipement" 
                     src="{{ equi_image }}" />
            </div>
        </div>
    </div>

    <div class="col-xl-6" style="margin-top: 20px; margin-left: 20px;">
        <div class="card mb-4">
            <div class="card-header text-primary">
                <i class="fas fa-info me-1"></i>
                Informations générales :
            </div>
            <div class="card-body">
                <p><strong>Identification de l'équipement :</strong> <span style="color: #000000;">{{ equipement.nom }}</span></p>
                <p><strong>Numéro de série :</strong> {{ instances.numero_serie }}</p>
                <p><strong>Date d'installation :</strong> {{ instances.date_installation }}</p>
                <p><strong>Système :</strong> {{ instances.sous_systeme_has_instance.systeme.nom }}</p>

                {% if equipement.parent %}
                <p><strong>Équipement parent :</strong>
                    <span style="color: #0c3f61;">
                        {{ equipement.parent.nom }}
                        {% if equipement_parent_instance %}
                            (N° série : {{ equipement_parent_instance.numero_serie }})
                        {% endif %}
                    </span>
                </p>
                {% endif %}

              <p><strong>Statut :</strong> 
    <span class="badge {% if instances.statut and instances.statut|upper == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
        {{ instances.statut }}
    </span>
</p>
                <p><strong>Quantité en stock :</strong> {{ quantite_stock }}</p>
            </div>
        </div>
    </div>
</div>

<div class="col-xl-11" style="margin-top: 5px; margin-left: 40px;">
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-file-alt me-1"></i>
            Détails
        </div>
        <div class="card-body">
            <div class="accordion" id="accordionExample">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                            <i class="fas fa-shield-alt me-1"></i> Caractéristiques de l'équipement :
                        </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                        <div class="accordion-body">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <table id="dynamicTable" class="table table-striped">
                                        <thead>
                                            <tr id="tableHeader"></tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                            <i class="fa-solid fa-circle-chevron-down"></i> Historique de la maintenance :
                        </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                        <div class="accordion-body">
                            <table id="datatablesSimple" class="table table-bordered">
                                <thead class="text-center">
                                    <tr class="text-center">
                                        <th>Date</th>
                                        <th>Type de maintenance</th>
                                        <th>Statut</th>
                                        <th>Technicien</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for maintenance in maintenances %}
                                    <tr>
                                        <td>{{ maintenance.date_maintenance }}</td>
                                        <td>{{ maintenance.type_maintenance }}</td>
                                        <td>{{ maintenance.statut_equipement }}</td>
                                        <td>{{ maintenance.technicien.nom }}</td>
                                        <td>{{ maintenance.description }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Zoom Image Modal -->
<div class="modal fade" id="imageZoomModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content bg-transparent border-0">
      <div class="modal-body p-0">
        <img src="" class="img-fluid w-100" id="zoomed-image" alt="Zoom équipement">
      </div>
    </div>
  </div>
</div>

<!-- JSON pour caractéristiques -->
<script id="caracteristiquesData" type="application/json">
    {{ caracteristiques|safe }}
</script>

<!-- Script dynamique -->
<script>
    document.addEventListener("DOMContentLoaded", function () {
        let equipementCaracteristiques = JSON.parse(document.getElementById('caracteristiquesData').textContent);

        let tableHeader = document.getElementById("tableHeader");
        let tbody = document.querySelector("#dynamicTable tbody");
        let tr = document.createElement("tr");

        equipementCaracteristiques.forEach(item => {
            let th = document.createElement("th");
            th.textContent = item.caracteristique;
            tableHeader.appendChild(th);

            let td = document.createElement("td");
            td.textContent = item.valeur;
            tr.appendChild(td);
        });

        tbody.appendChild(tr);

        // Zoom image
        const image = document.getElementById("equipement-img");
        const zoomedImage = document.getElementById("zoomed-image");

        image.addEventListener("click", function () {
            zoomedImage.src = image.src;
            const modal = new bootstrap.Modal(document.getElementById('imageZoomModal'));
            modal.show();
        });
    });
</script>
{% endblock %}
