{% extends 'base.html' %}
{% load static %}

{% block content %}
 
<div class="card mb-3" style="max-width: 800px; margin-left: 10px; margin-top: 10px;"> 
<div class="container mt-5">
    <form action="" method="POST" enctype="multipart/form-data">
        {% csrf_token %}

        <!-- Groupe Stock & Catégorie -->
        <div class="border p-3 mb-3">
            <h5>Informations sur le Stock</h5>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="stock_select" class="form-label">Stock</label>
                    <select class="form-control" id="stock_select" name="stock_select" required>
                        <option value="">Sélectionner un stock</option>
                        {% for stock in stocks %}
                            <option value="{{ stock.id }}">{{ stock.unite }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="categorie_select" class="form-label">Catégorie</label>
                    <select class="form-control" id="categorie_select" name="categorie_select" required>
                        <option value="">Sélectionner une catégorie</option>
                        {% for categorie in categories %}
                            <option value="{{ categorie.id }}">{{ categorie.nom }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <!-- Groupe Informations Générales -->
        <div class="border p-3 mb-3">
            <h5>Informations Générales</h5>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="quantité" class="form-label">Quantité</label>
                    <input type="number" class="form-control" id="quantité" name="quantité" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="reference_entree" class="form-label">Référence</label>
                    <input type="text" class="form-control" id="reference_entree" name="reference_entree" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_entree" class="form-label">Date d'entrée</label>
                    <input type="date" class="form-control" id="date_entree" name="date_entree" required>
                </div>
            </div>
        </div>

        <!-- Groupe Détails de l'Équipement -->
        <div class="border p-3 mb-3">
            <h5>Détails de l'Équipement</h5>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="nom_equipement" class="form-label">Nom de l'Équipement</label>
                    <input type="text" class="form-control" id="nom_equipement" name="nom_equipement" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="constructeur" class="form-label">Constructeur</label>
                    <input type="text" class="form-control" id="constructeur" name="constructeur" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="product_number" class="form-label">Numéro de Produit</label>
                    <input type="text" class="form-control" id="product_number" name="product_number" required>
                </div>
            </div>
        </div>

        <!-- Groupe Informations Techniques -->
        <div class="border p-3 mb-3">
            <h5>Informations Techniques</h5>
            <div class="row">
                

                <div class="col-md-4 mb-3">
                    <label for="mtbf" class="form-label">MTBF</label>
                    <input type="number" class="form-control" id="mtbf" name="mtbf" step="0.01">
                </div>

                <div class="col-md-4 mb-3">
                    <label for="date_fin_garantie" class="form-label">Date de Fin de Garantie</label>
                    <input type="date" class="form-control" id="date_fin_garantie" name="date_fin_garantie">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="mttr" class="form-label">MTTR (minutes)</label>
                    <input type="number" class="form-control" id="mttr" name="mttr" step="0.01">
                </div>
            </div>
        </div>

        <!-- Groupe Autres Informations -->
        <div class="border p-3 mb-3">
            <h5>Autres Informations</h5>
            <div class="row">
               
                <div class="col-md-4 mb-3">
                    <label for="media" class="form-label">Média</label>
                    <input type="file" class="form-control" id="media" name="media">
                </div>
            </div>
        </div>
        <div class="mb-2">
            <div class="col-md-5"> 
                <label class="form-label">Numéro(s) de série</label>
                <div id="serial_numbers_container">
                    <div class="input-group mb-2">
                        <input type="text" class="form-control serial-number" name="numero_serie[]" placeholder="Numéro de série">
                        <button type="button" class="btn btn-outline-primary add-serial"><i class="fa fa-plus" aria-hidden="true"></i></button>
                    </div>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-primary">Ajouter l'Équipement</button>
    </form>
</div>
</div>
<script>
    document.addEventListener("click", function (event) {
        if (event.target.classList.contains("add-serial")) {
            let container = document.getElementById("serial_numbers_container");
            let newField = document.createElement("div");
            newField.classList.add("input-group", "mb-2");
            newField.innerHTML = `
                <input type="text" class="form-control serial-number" name="numero_serie[]" placeholder="Numéro de série">
                <button type="button" class="btn btn-outline-danger remove-serial"><i class="fa fa-minus" aria-hidden="true"></i></button>
            `;
            container.appendChild(newField);
        }
        if (event.target.classList.contains("remove-serial")) {
            event.target.parentElement.remove();
        }
    });
</script>
{% endblock %}
