 
from django.contrib import admin
from django.urls import path,include
from django.conf.urls.static import static
from django.conf import settings
from django.contrib.auth import views as auth_views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('pages.urls')),
    path('pages/', include('pages.urls')),
    path('accounts/', include('accounts.urls')),
    path('chatbot/', include('chatbot.urls')),

path('login/', auth_views.LoginView.as_view(template_name='accounts/login.html'), name='login'),
]+ static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
