 
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>Connexion - EWMS</title>
   
    <style>
        .formContainer {
            max-width:320px;
            box-shadow : 4px 8px 16px #d7d7d7;
            margin:0 auto;
            padding:20px;
            border-radius:0px 0px 4px 4px;
          }
          input {
            box-shadow:none !important;
            outline:none;
          }
          #btnHolder {
            margin:0px -20px -20px -20px;
          }
          #btnHolder .btn {
            border-radius:0px 0px 4px 4px !important;
            height:60px;
          }
          @media screen and (max-width:766px){
            .centerOnMobile {
              margin : 20vh auto
            }
          }
          @media screen and (min-width:767px){
            #mainBgn {
              background : url('replace this with your image path') no-repeat;
              background-size:cover;
              background-position:center;
              height:90vh;
              width:100%;
            }
            #mainContainer {
              box-shadow : 2px 4px 12px rgba(0,0,0,0.4);
              height:90vh;
              margin: 5vh 20px;
            }
          }
      </style>
</head>
<body>
    <div id="mainContainer">
        <div class="">
        <div class="row align-items-center">
          <div class="col-lg-6 col-md-6 col-xs-12 d-none d-lg-block d-md-block">
            <div id="mainBgn"></div>
          </div>
          <div class="col-lg-6 col-md-6 col-xs-12">
            <div class="p-4 centerOnMobile" >
              <div class="formContainer">
                <h2 class="p-2 h4 text-center"><i class="fas fa-lock me-2"></i> Login</h2>
                <form action="#">
                  <div class="form-floating my-3">
                    <input type="email" class="form-control" id="floatingInput" placeholder="<EMAIL>">
                    <label for="floatingInput">Email address</label>
                  </div>
                  <div class="form-floating">
                    <input type="password" class="form-control" id="floatingPassword" placeholder="Password">
                    <label for="floatingPassword">Password</label>
                  </div>
                  <div class="mt-3">
                    <input type="checkbox" /> Remember me
                  </div>
                  <div id="btnHolder">
                    <button class="btn btn-lg btn-primary mt-3 w-100">Login</button>
                  </div>
                </form>
              </div>
              <div class="mt-2 text-center">
                <a href="#">New? signup</a><br />
                <a href="#">Forgot Password?</a>
              </div>
            </div>
          </div>
        </div>
        </div>
        </div>
</body>
</html>