{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>


    #datatablesSimple td,
#datatablesSimple th,
#systemes-table td,
#systemes-table th {
    text-align: center !important; /* Centre horizontalement */
    vertical-align: middle !important; /* Centre verticalement */
}
</style>
<div class="container-fluid px-4">
   
    <ol class="breadcrumb mb-4" style="margin-top: 10px;" >
        <li class="breadcrumb-item">Stock</a></li>
        <li class="breadcrumb-item active"><a href="{% url 'bge' %}">Historique du stock BGE</a></li>
    </ol>
    <div class="card mb-4" style="margin-top: 10px;">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Historique du stock BGE
           
           
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>Équipement</th>
                        <th>Déstination</th>
                        <th>N° Serie</th>
                        <th>Date Entrée</th>
                        <th>Date Sortie</th>
                        <th>Réfrence d'entrée</th>
                        <th>Réfrence de sortie</th>
                        
                    </tr>
                </thead>
            
                <tbody>
                    {% for equipement_info in equipements_instance_stock %}
          
                        <tr>
                            <td>{{ equipement_info.stock_has_instance.equipement.nom }}</td>
                            <td>{{ equipement_info.destination }}</td>
                            <td>{{ equipement_info.numero_serie }}</td>
                            <td>{{ equipement_info.date_entree }}</td>
                            <td>{{ equipement_info.date_sortie }}</td>
                            <td>{{ equipement_info.reference_entree }}</td>
                            <td>{{ equipement_info.reference_sortie}}</td>
                            
                          
                        </tr> 
                
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

 
      
    
    

{% endblock %}
