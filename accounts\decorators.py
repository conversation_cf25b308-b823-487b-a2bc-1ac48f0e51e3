# accounts/decorators.py

from functools import wraps
from django.http import HttpResponseForbidden
from django.shortcuts import redirect
from django.contrib import messages

# Importe le modèle Profile pour accéder au rôle de l'utilisateur
from accounts.models import Profile


def role_required(allowed_roles=None):
    """
    Décorateur qui vérifie que l'utilisateur connecté a un rôle autorisé.
    Si allowed_roles est vide ou None, refuse l'accès.
    """
    if allowed_roles is None:
        allowed_roles = []

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Vérifie si l'utilisateur est authentifié
            if not request.user.is_authenticated:
                return redirect('login')

            try:
                # Récupère le rôle depuis le profil
                user_role = request.user.profile.role

                if user_role in allowed_roles:
                    return view_func(request, *args, **kwargs)
                else:
                    # Accès refusé
                    messages.error(request, "Accès interdit : vous n'avez pas les droits nécessaires.")
                    return redirect('index')  # Redirige vers la page d'accueil ou une autre page
            except Profile.DoesNotExist:
                # Le profil n'existe pas → erreur
                messages.error(request, "Profil introuvable. Veuillez contacter l'administrateur.")
                return redirect('login')

        return _wrapped_view
    return decorator


# Décorateurs prédéfinis selon les rôles
def admin_required(view_func):
    """Accès réservé aux admins"""
    return role_required(['admin'])(view_func)


def user_required(view_func):
    """Accès réservé aux utilisateurs standard (user)"""
    return role_required(['user'])(view_func)


def viewer_required(view_func):
    """Accès réservé aux viewers"""
    return role_required(['viewer'])(view_func)


def admin_or_user_required(view_func):
    """Accès réservé aux admins ou utilisateurs standard"""
    return role_required(['admin', 'user'])(view_func)


def authenticated_user(view_func):
    """
    Empêche l'accès à des pages spécifiques si l'utilisateur est déjà connecté.
    Utile pour /login ou /register par exemple.
    """
    def _wrapped_view(request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('index')
        else:
            return view_func(request, *args, **kwargs)
    return _wrapped_view