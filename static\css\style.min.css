/**
 * Minified by js<PERSON><PERSON><PERSON><PERSON> using clean-css v5.3.1.
 * Original file: /npm/simple-datatables@7.1.2/dist/style.css
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
 .datatable-wrapper.no-header .datatable-container{border-top:1px solid #d9d9d9}
 .datatable-wrapper.no-footer .datatable-container{border-bottom:1px solid #d9d9d9}
 .datatable-bottom,.datatable-top{padding:8px 10px}
 .datatable-bottom>div:first-child,.datatable-bottom>nav:first-child,.datatable-top>div:first-child,.datatable-top>nav:first-child{float:left}.datatable-bottom>div:last-child,.datatable-bottom>nav:last-child,.datatable-top>div:last-child,.datatable-top>nav:last-child{float:right}
 .datatable-selector{padding:6px}.datatable-input{padding:6px 12px}.datatable-info{margin:7px 0}
 .datatable-pagination ul{margin:0;padding-left:0}.datatable-pagination li{list-style:none;float:left}
 .datatable-pagination li.datatable-hidden{visibility:hidden}
 .datatable-pagination a{border:1px solid transparent;float:left;margin-left:2px;padding:6px 12px;position:relative;text-decoration:none;color:#333;cursor:pointer}.datatable-pagination a:hover{background-color:#d9d9d9}
 .datatable-pagination .datatable-active a,.datatable-pagination .datatable-active a:focus,.datatable-pagination .datatable-active a:hover{background-color:#d9d9d9;cursor:default}
 .datatable-pagination .datatable-disabled a,.datatable-pagination .datatable-disabled a:focus,.datatable-pagination .datatable-disabled a:hover,.datatable-pagination .datatable-ellipsis a{pointer-events:none;cursor:default}
 .datatable-pagination .datatable-disabled a,.datatable-pagination .datatable-disabled a:focus,.datatable-pagination .datatable-disabled a:hover{cursor:not-allowed;opacity:.4}
 .datatable-pagination .datatable-pagination a{font-weight:700}
 .datatable-table{max-width:100%;width:100%;border-spacing:0;border-collapse:separate}
 .datatable-table>tbody>tr>td,.datatable-table>tbody>tr>th,.datatable-table>tfoot>tr>td,.datatable-table>tfoot>tr>th,.datatable-table>thead>tr>td,.datatable-table>thead>tr>th{vertical-align:top;padding:8px 10px}
 .datatable-table>thead>tr>th{vertical-align:bottom;text-align:left;border-bottom:1px solid #d9d9d9}
 .datatable-table>tfoot>tr>th{vertical-align:bottom;text-align:left;border-top:1px solid #d9d9d9}.datatable-table th{vertical-align:bottom;text-align:left}
 .datatable-table th a{text-decoration:none;color:inherit}.datatable-filter,.datatable-sorter{display:inline-block;height:100%;position:relative;width:100%}
 .datatable-sorter::after,.datatable-sorter::before{content:"";height:0;width:0;position:absolute;right:4px;border-left:4px solid transparent;border-right:4px solid transparent;opacity:.2}.datatable-sorter::before{border-top:4px solid #000;bottom:0}.datatable-sorter::after{border-bottom:4px solid #000;border-top:4px solid transparent;top:0}
 .datatable-ascending .datatable-filter::after,.datatable-ascending .datatable-sorter::after,.datatable-descending .datatable-filter::before,.datatable-descending .datatable-sorter::before{opacity:.6}
 .datatable-filter::before{content:"";position:absolute;right:4px;opacity:.2;width:0;height:0;border-left:7px solid transparent;border-right:7px solid transparent;border-radius:50%;border-top:10px solid #000;top:25%}
 .datatable-filter-active .datatable-filter::before{opacity:.6}
 .datatable-empty{text-align:center}
 .datatable-bottom::after,.datatable-top::after{clear:both;content:" ";display:table}
 table.datatable-table:focus tr.datatable-cursor>td:first-child{border-left:3px #00f solid}
 table.datatable-table:focus{outline:solid 1px black;outline-offset:-1px}
 /*# sourceMappingURL=/sm/7faebb93ab083e20bf71c693c970b2206a78620f4a20eb890eeaee129d14cd66.map */