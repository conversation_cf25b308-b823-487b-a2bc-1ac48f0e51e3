function CopyToClipboard(t,e,o){var a=$("<input>");if(""!=t){a=$("<input>");$("body").append(a),a.val(t).select(),document.execCommand("copy"),a.remove()}void 0===e&&(e=!0),void 0===o&&(o="Copied to clipboard");var n=$("div.copy-notification");e&&0==n.length&&(n=$("<div/>",{class:"copy-notification",text:o}),$("body").append(n),n.fadeIn("slow",function(){setTimeout(function(){n.fadeOut("slow",function(){n.remove()})},1e3)}))}jQuery(window).on("load",function(){"use strict";$(".textarea_editor").wysihtml5({html:!0})}),jQuery(window).on("load resize",function(){$(".customscroll").mCustomScrollbar({theme:"dark-2",scrollInertia:300,autoExpandScrollbar:!0,advanced:{autoExpandHorizontalScroll:!0}})}),jQuery(document).ready(function(){"use strict";jQuery(".bg_img").each(function(t,e){var o=jQuery(e);jQuery(this).hide(),jQuery(this).parent().css({background:"url("+o.attr("src")+") no-repeat center center"})}),jQuery("img.svg").each(function(){var o=jQuery(this),a=o.attr("id"),n=o.attr("class"),t=o.attr("src");jQuery.get(t,function(t){var e=jQuery(t).find("svg");void 0!==a&&(e=e.attr("id",a)),void 0!==n&&(e=e.attr("class",n+" replaced-svg")),!(e=e.removeAttr("xmlns:a")).attr("viewBox")&&e.attr("height")&&e.attr("width")&&e.attr("viewBox","0 0 "+e.attr("height")+" "+e.attr("width")),o.replaceWith(e)},"xml")});var i={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};window.onload=function(){var t=document.querySelectorAll("pre code");if(t.length){for(var e=0,o=t.length;e<o;e++){var a=t[e],n=a.innerHTML;n=String(n).replace(/[&<>"'\/]/g,function(t){return i[t]}),a.innerHTML=n}$("pre code").each(function(t,e){hljs.highlightBlock(e)})}},$("#filter_input").on("keyup",function(){var t=$(this).val().toLowerCase();$("#filter_list .fa-hover").filter(function(){$(this).toggle(-1<$(this).text().toLowerCase().indexOf(t))})}),$(".custom-select2").select2(),$('[data-toggle="tooltip"]').tooltip(),$('[data-toggle="popover"]').popover(),$(".form-control").on("focus",function(){$(this).parent().addClass("focus")}),$(".form-control").on("focusout",function(){$(this).parent().removeClass("focus")}),$('.menu-icon, [data-toggle="left-sidebar-close"]').on("click",function(){$("body").toggleClass("sidebar-shrink"),$(".left-side-bar").toggleClass("open"),$(".mobile-menu-overlay").toggleClass("show")}),$('[data-toggle="header_search"]').on("click",function(){jQuery(".header-search").slideToggle()});$(window).width();$(document).on("touchstart click",function(t){0!=$(t.target).parents(".left-side-bar").length||$(t.target).is(".menu-icon, .menu-icon img")||($(".left-side-bar").removeClass("open"),$(".menu-icon").removeClass("open"),$(".mobile-menu-overlay").removeClass("show"))}),$("#accordion-menu").each(function(){var t=window.location.href.split("/").pop();$(this).find('a[href="'+t+'"]').addClass("active")}),$(".fa-hover").click(function(t){t.preventDefault(),CopyToClipboard($(this).find(".icon-copy").first().prop("outerHTML"),!0,"Copied")}),new ClipboardJS(".code-copy").on("success",function(t){CopyToClipboard("",!0,"Copied"),t.clearSelection()}),$(".date-picker").datepicker({language:"en",autoClose:!0,dateFormat:"dd MM yyyy"}),$(".datetimepicker").datepicker({timepicker:!0,language:"en",autoClose:!0,dateFormat:"dd MM yyyy"}),$(".datetimepicker-range").datepicker({language:"en",range:!0,multipleDates:!0,multipleDatesSeparator:" - "}),$(".month-picker").datepicker({language:"en",minView:"months",view:"months",autoClose:!0,dateFormat:"MM yyyy"}),$(".time-picker").timeDropper({mousewheel:!0,meridians:!0,init_animation:"dropdown",setCurrentTime:!1}),$(".time-picker-default").timeDropper(),$("[data-color]").each(function(){$(this).css("color",$(this).attr("data-color"))}),$("[data-bgcolor]").each(function(){$(this).css("background-color",$(this).attr("data-bgcolor"))}),$("[data-border]").each(function(){$(this).css("border",$(this).attr("data-border"))}),$("#accordion-menu").vmenuModule({Speed:400,autostart:!1,autohide:!0})}),function(n){n.fn.vmenuModule=function(t){var e,o,a=n.extend({Speed:220,autostart:!0,autohide:1},t);(o=(e=n(this)).find("ul").parent("li").children("a")).attr("data-option","off"),o.unbind("click").on("click",function(){var t=n(this);a.autohide&&t.parent().parent().find("a[data-option='on']").parent("li").children("ul").slideUp(a.Speed/1.2,function(){n(this).parent("li").children("a").attr("data-option","off"),n(this).parent("li").removeClass("show")}),"off"==t.attr("data-option")&&t.parent("li").children("ul").slideDown(a.Speed,function(){t.attr("data-option","on"),t.parent("li").addClass("show")}),"on"==t.attr("data-option")&&(t.attr("data-option","off"),t.parent("li").children("ul").slideUp(a.Speed),t.parent("li").removeClass("show"))}),a.autostart?e.find("a").each(function(){n(this).parent("li").parent("ul").slideDown(a.Speed,function(){n(this).parent("li").children("a").attr("data-option","on")})}):e.find("a.active").each(function(){n(this).parent("li").parent("ul").slideDown(a.Speed,function(){n(this).parent("li").children("a").attr("data-option","on"),n(this).parent("li").addClass("show")})})}}(window.jQuery||window.Zepto),function(){var t=window.navigator.userAgent,e=t.indexOf("MSIE ");if(0<e){parseInt(t.substring(e+5,t.indexOf(".",e)),10);document.querySelector("body").className+=" IE"}if(0<t.indexOf("Trident/")){var o=t.indexOf("rv:");parseInt(t.substring(o+3,t.indexOf(".",o)),10);document.querySelector("body").className+=" IE"}}();