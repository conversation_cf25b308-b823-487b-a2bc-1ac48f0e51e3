# Generated by Django 5.1.6 on 2025-06-25 22:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Bion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('date_creation', models.DateField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('status', models.CharField(max_length=50, null=True)),
            ],
            options={
                'db_table': 'bion',
            },
        ),
        migrations.CreateModel(
            name='Caracteristique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.Char<PERSON>ield(max_length=100, null=True)),
            ],
            options={
                'db_table': 'caracteristique',
            },
        ),
        migrations.CreateModel(
            name='EquipementStockInstance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numero_serie', models.CharField(blank=True, default='N/A', max_length=100)),
                ('date_entree', models.DateField(blank=True, null=True)),
                ('reference_entree', models.CharField(blank=True, max_length=50, null=True)),
                ('date_sortie', models.DateField(blank=True, null=True)),
                ('date_fin_garantie', models.DateField(blank=True, null=True)),
                ('destination', models.CharField(blank=True, max_length=50, null=True)),
                ('reference_sortie', models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                'db_table': 'equipement_instance_stock',
            },
        ),
        migrations.CreateModel(
            name='EquipementSystemeInstance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_numero_serie', models.CharField(blank=True, db_column='numero_serie', max_length=100)),
                ('date_installation', models.DateField(blank=True, null=True)),
                ('date_derniere_maintenance', models.DateField(blank=True, null=True)),
                ('date_remplacement', models.DateField(blank=True, null=True)),
                ('destination', models.CharField(blank=True, default='reparation', max_length=100)),
                ('statut', models.CharField(max_length=50, null=True)),
            ],
            options={
                'db_table': 'equipement_Systeme_instance',
            },
        ),
        migrations.CreateModel(
            name='Formation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('intitule', models.CharField(max_length=100, null=True)),
                ('reference', models.CharField(max_length=100, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('badge', models.ImageField(blank=True, null=True, upload_to='equipements/')),
            ],
            options={
                'db_table': 'formation',
            },
        ),
        migrations.CreateModel(
            name='Media',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='maintenance/')),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type_notification', models.CharField(choices=[('PANNE', 'Panne déclarée'), ('STOCK', 'Changement de stock'), ('PREVENTIVE', 'Maintenance preventive déclarée'), ('CORRECTIVE', 'Maintenance corrective déclarée')], max_length=100)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('date', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'Notification',
            },
        ),
        migrations.CreateModel(
            name='Servant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('prenom', models.CharField(max_length=45, null=True)),
                ('grade', models.CharField(max_length=45, null=True)),
                ('matricule', models.CharField(max_length=45, null=True)),
                ('date_affectation', models.DateField(blank=True, null=True)),
                ('specialite', models.CharField(max_length=100, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='personnels/')),
            ],
            options={
                'db_table': 'servant',
            },
        ),
        migrations.CreateModel(
            name='SousSysteme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'sous_systeme',
            },
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lieu', models.CharField(max_length=100, null=True)),
                ('place_armes', models.CharField(max_length=100, null=True)),
                ('unite', models.CharField(max_length=50, null=True)),
            ],
            options={
                'db_table': 'stock',
            },
        ),
        migrations.CreateModel(
            name='Systeme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('date_mise_en_oeuvre', models.DateField(blank=True, null=True)),
                ('status', models.CharField(max_length=50, null=True)),
                ('serial_number', models.CharField(max_length=50, null=True)),
                ('product_number', models.CharField(max_length=50, null=True)),
            ],
            options={
                'db_table': 'systeme',
            },
        ),
        migrations.CreateModel(
            name='Technicien',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('prenom', models.CharField(max_length=45, null=True)),
                ('specialite', models.CharField(max_length=100, null=True)),
                ('matricule', models.CharField(max_length=45, unique=True)),
                ('date_affectation', models.DateField(blank=True, null=True)),
                ('grade', models.CharField(max_length=45, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='personnels/')),
            ],
            options={
                'db_table': 'Technicien',
            },
        ),
        migrations.CreateModel(
            name='Cie',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('status', models.CharField(max_length=50, null=True)),
                ('date_mise_en_ouevre', models.DateField(blank=True, null=True)),
                ('bion', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='cies', to='pages.bion')),
            ],
            options={
                'db_table': 'cie',
            },
        ),
        migrations.CreateModel(
            name='Equipement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, null=True)),
                ('constructeur', models.CharField(blank=True, max_length=100, null=True)),
                ('product_numbre', models.CharField(max_length=100, null=True)),
                ('date_fin_garantie', models.DateField(blank=True, null=True)),
                ('mttr', models.FloatField(blank=True, null=True)),
                ('mtbf', models.FloatField(blank=True, null=True)),
                ('criticite', models.IntegerField(default=1, null=True)),
                ('maintenance_planifiee', models.DateField(blank=True, null=True)),
                ('type', models.CharField(blank=True, max_length=100, null=True)),
                ('media', models.ImageField(blank=True, null=True, upload_to='equipements/')),
                ('periodicite_maintenance', models.IntegerField(blank=True, null=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sous_equipements', to='pages.equipement')),
            ],
            options={
                'db_table': 'equipement',
            },
        ),
        migrations.CreateModel(
            name='Mission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('objet', models.CharField(max_length=100, null=True)),
                ('date_debut_mission', models.DateField(blank=True, null=True)),
                ('date_fin_mission', models.DateField(blank=True, null=True)),
                ('reference_mission', models.CharField(max_length=100, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('lieu', models.CharField(max_length=100, null=True)),
                ('bion_mission', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='bataillon_missions', to='pages.bion')),
                ('cie_missions', models.ManyToManyField(blank=True, related_name='missions_cie', to='pages.cie')),
            ],
            options={
                'db_table': 'mission',
            },
        ),
        migrations.CreateModel(
            name='MissionHasServant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='servant_missions', to='pages.mission')),
                ('servant', models.ManyToManyField(related_name='missions_servant', to='pages.servant')),
            ],
            options={
                'db_table': 'mission_servant',
            },
        ),
        migrations.CreateModel(
            name='MissionHasTechnicien',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='technicien_missions', to='pages.mission')),
                ('technicien', models.ManyToManyField(related_name='missions_technicien', to='pages.technicien')),
            ],
            options={
                'db_table': 'mission_technicien',
            },
        ),
        migrations.CreateModel(
            name='ServantHasFormation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_debut', models.DateField(blank=True, null=True)),
                ('date_fin', models.DateField(blank=True, null=True)),
                ('reference', models.CharField(max_length=100, null=True)),
                ('formation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='servants', to='pages.formation')),
                ('servant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='formations', to='pages.servant')),
            ],
            options={
                'db_table': 'servant_has_formation',
            },
        ),
        migrations.CreateModel(
            name='Sion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('status', models.CharField(max_length=50, null=True)),
                ('date_mise_en_ouevre', models.DateField(blank=True, null=True)),
                ('cie', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='sions', to='pages.cie')),
            ],
            options={
                'db_table': 'sion',
            },
        ),
        migrations.AddField(
            model_name='mission',
            name='sion_missions',
            field=models.ManyToManyField(blank=True, related_name='missions_sion', to='pages.sion'),
        ),
        migrations.CreateModel(
            name='Maintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type_maintenance', models.CharField(max_length=100, null=True)),
                ('statut_sous_systeme', models.CharField(max_length=50, null=True)),
                ('statut_systeme', models.CharField(max_length=50, null=True)),
                ('statut_equipement', models.CharField(max_length=50, null=True)),
                ('date_maintenance', models.DateField(blank=True, null=True)),
                ('duree', models.IntegerField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('reference_declaration_panne', models.TextField(blank=True, null=True)),
                ('reference_entree', models.TextField(blank=True, null=True)),
                ('reference_sortie', models.TextField(blank=True, null=True)),
                ('destination', models.TextField(blank=True, null=True)),
                ('date_sortie_stock', models.DateField(blank=True, null=True)),
                ('bion', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.bion')),
                ('cie', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.cie')),
                ('equipement', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='equipement_maintenance', to='pages.equipement')),
                ('equipement_de_rechange', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rechange_instance', to='pages.equipementstockinstance')),
                ('equipementsystemeInstance', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.equipementsystemeinstance')),
                ('media', models.ManyToManyField(blank=True, to='pages.media')),
                ('sion', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.sion')),
                ('sous_systeme', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.soussysteme')),
                ('systeme', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.systeme')),
                ('technicien', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='pages.technicien')),
            ],
            options={
                'db_table': 'maintenance',
            },
        ),
        migrations.CreateModel(
            name='SousSystemeHasEquipement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(blank=True, max_length=50, null=True)),
                ('quantite', models.IntegerField(blank=True, null=True)),
                ('equipement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipement_eq', to='pages.equipement')),
                ('sous_systeme', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sous_systeme_eq', to='pages.soussysteme')),
                ('systeme', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='systemes_eq', to='pages.systeme')),
            ],
            options={
                'db_table': 'sous_systeme_has_equipement',
            },
        ),
        migrations.AddField(
            model_name='equipementsystemeinstance',
            name='sous_systeme_has_instance',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipement_systeme_instances', to='pages.soussystemehasequipement'),
        ),
        migrations.CreateModel(
            name='StockHasEquipement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(max_length=50, null=True)),
                ('quantite', models.IntegerField(blank=True, null=True)),
                ('quantite_critique', models.IntegerField(blank=True, null=True)),
                ('equipement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipement_stock', to='pages.equipement')),
                ('sous_systeme', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sous_systeme_stock', to='pages.soussysteme')),
                ('stock', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='systeme_stock', to='pages.stock')),
            ],
            options={
                'db_table': 'stock_has_equipement',
            },
        ),
        migrations.AddField(
            model_name='equipementstockinstance',
            name='stock_has_instance',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='equipement_systeme_instances', to='pages.stockhasequipement'),
        ),
        migrations.CreateModel(
            name='SionHasSysteme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(max_length=50, null=True)),
                ('sion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='systemes', to='pages.sion')),
                ('systeme', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cies', to='pages.systeme')),
            ],
            options={
                'db_table': 'sion_has_systeme',
            },
        ),
        migrations.CreateModel(
            name='MaintenanceHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_panne', models.DateField(blank=True, null=True, verbose_name='Date de panne')),
                ('description_panne', models.TextField(blank=True, null=True, verbose_name='Description de la panne')),
                ('statut', models.CharField(choices=[('pending', 'En panne'), ('in_progress', 'En cours de r"paration'), ('completed', 'Réparé'), ('not_repairable', 'Non réparable'), ('external_repair', 'Réparation externe')], default='pending', max_length=20)),
                ('statut_atelier', models.CharField(choices=[('pending', 'En panne'), ('in_progress', 'En cours de r"paration'), ('completed', 'Réparé'), ('not_repairable', 'Non réparable'), ('external_repair', 'Réparation externe')], default='pending', max_length=20, verbose_name='Statut - Entrée en Atelier')),
                ('date_entree_atelier', models.DateField(blank=True, null=True, verbose_name="Date d'entrée en atelier")),
                ('description_entree_atelier', models.TextField(blank=True, null=True, verbose_name='Description entrée atelier')),
                ('lieu_reparation', models.CharField(blank=True, max_length=100, null=True, verbose_name='Lieu de réparation')),
                ('statut_externe', models.CharField(choices=[('pending', 'En panne'), ('in_progress', 'En cours de r"paration'), ('completed', 'Réparé'), ('not_repairable', 'Non réparable'), ('external_repair', 'Réparation externe')], default='pending', max_length=20, verbose_name='Statut - Réparation Externe')),
                ('date_envoi_reparation_externe', models.DateField(blank=True, null=True, verbose_name="Date d'envoi en réparation externe")),
                ('description_envoi_reparation_externe', models.TextField(blank=True, null=True, verbose_name='Description envoi réparation externe')),
                ('lieu_reparation_externe', models.CharField(blank=True, max_length=100, null=True, verbose_name='Lieu de réparation externe')),
                ('statut_reparation', models.CharField(choices=[('pending', 'En panne'), ('in_progress', 'En cours de r"paration'), ('completed', 'Réparé'), ('not_repairable', 'Non réparable'), ('external_repair', 'Réparation externe')], default='pending', max_length=20, verbose_name='Statut - Détails de la Réparation')),
                ('date_reparation', models.DateField(blank=True, null=True, verbose_name='Date de réparation')),
                ('description_reparation', models.TextField(blank=True, null=True, verbose_name='Description de la réparation')),
                ('equipement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.equipement')),
                ('equipementsystemeInstance', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.equipementsystemeinstance')),
                ('maintenance', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.maintenance')),
                ('systeme', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.systeme')),
            ],
            options={
                'verbose_name': 'Historique de maintenance',
                'verbose_name_plural': 'Historiques de maintenance',
                'db_table': 'maintenanceHistory',
            },
        ),
        migrations.CreateModel(
            name='TechnicienHasFormation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_debut', models.DateField(blank=True, null=True)),
                ('date_fin', models.DateField(blank=True, null=True)),
                ('reference', models.CharField(max_length=100, null=True)),
                ('formation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.formation')),
                ('technicien', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.technicien')),
            ],
            options={
                'db_table': 'Technicien_has_Formation',
            },
        ),
        migrations.CreateModel(
            name='TechnicienHasMaintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.maintenance')),
                ('technicien', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='pages.technicien')),
            ],
            options={
                'db_table': 'Technicien_has_Maintenance',
            },
        ),
        migrations.CreateModel(
            name='Valeur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('valeur', models.CharField(max_length=255, null=True)),
                ('caracteristique', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='valeurs', to='pages.caracteristique')),
                ('equipement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='valeurs', to='pages.equipement')),
            ],
            options={
                'db_table': 'valeur',
            },
        ),
        migrations.CreateModel(
            name='MissionHasServantRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(blank=True, max_length=100, null=True)),
                ('missionhasservant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.missionhasservant')),
                ('servant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.servant')),
            ],
            options={
                'db_table': 'mission_has_servant_role',
                'unique_together': {('missionhasservant', 'servant')},
            },
        ),
        migrations.CreateModel(
            name='SystemeHasSousSysteme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sous_systeme', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.soussysteme')),
                ('systeme', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.systeme')),
            ],
            options={
                'db_table': 'systeme_Has_Sous_Systeme',
                'unique_together': {('systeme', 'sous_systeme')},
            },
        ),
        migrations.CreateModel(
            name='MissionHasTechnicienRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(blank=True, max_length=100, null=True)),
                ('missionhastechnicien', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.missionhastechnicien')),
                ('technicien', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.technicien')),
            ],
            options={
                'db_table': 'mission_has_technicien_role',
                'unique_together': {('missionhastechnicien', 'technicien')},
            },
        ),
    ]
