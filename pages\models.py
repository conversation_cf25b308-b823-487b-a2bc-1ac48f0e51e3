from django.db import models
from django.core.exceptions import ValidationError
from model_utils import FieldTracker

class Bion(models.Model):
    nom = models.CharField(max_length=45, null=True)
    date_creation = models.DateField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True)
    
    class Meta:
        db_table = 'bion'
    def __str__(self):
        return self.nom 

class Cie(models.Model):
    nom = models.CharField(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True)
    bion = models.ForeignKey(Bion, on_delete=models.CASCADE, related_name="cies", null=False ,default=1)
    date_mise_en_ouevre = models.DateField(null=True, blank=True)
    class Meta:
        db_table = 'cie'
    def __str__(self):
        return self.nom 

class Sion(models.Model):
    nom = models.Char<PERSON>ield(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True)
    cie = models.ForeignKey(Cie, on_delete=models.CASCADE, related_name="sions", null=False ,default=1)
    date_mise_en_ouevre = models.DateField(null=True, blank=True)
    class Meta:
        db_table = 'sion'
    def __str__(self):
        return self.nom 

class Systeme(models.Model):
    nom = models.CharField(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
    date_mise_en_oeuvre = models.DateField(null=True, blank=True)
    #
    status = models.CharField(max_length=50, null=True)
    serial_number= models.CharField(max_length=50, null=True)
    product_number= models.CharField(max_length=50, null=True)
    #
  

    class Meta:
        db_table = 'systeme'
    
    def __str__(self):
        return self.nom 

class SionHasSysteme(models.Model):
    sion = models.ForeignKey(Sion, on_delete=models.CASCADE, related_name="systemes")
    systeme = models.ForeignKey(Systeme, on_delete=models.CASCADE, related_name="cies")
    status = models.CharField(max_length=50, null=True)

    class Meta:
        db_table = 'sion_has_systeme'
    
    def __str__(self):
        return f"{self.sion} - {self.systeme.nom}"



class SousSysteme(models.Model):
    nom = models.CharField(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
     

    class Meta:
        db_table = 'sous_systeme'
    
    def __str__(self):
        return self.nom 

class SystemeHasSousSysteme(models.Model):
    systeme = models.ForeignKey(Systeme, on_delete=models.CASCADE)  # Clé étrangère vers le système
    sous_systeme = models.ForeignKey(SousSysteme, on_delete=models.CASCADE)  # Clé étrangère vers le sous-système
    
    class Meta:
        unique_together = ('systeme', 'sous_systeme')   
        db_table = 'systeme_Has_Sous_Systeme'
    
    def __str__(self):
        return f"{self.systeme.nom} - {self.sous_systeme.nom}"


#####################################################################################################################################

class Equipement(models.Model):
    nom = models.CharField(max_length=100, null=True)
    constructeur = models.CharField(max_length=100, null=True, blank=True)
    product_numbre = models.CharField(max_length=100, null=True)  # Référence unique 
    date_fin_garantie = models.DateField(null=True, blank=True)
    mttr = models.FloatField(null=True, blank=True)  # Temps moyen de réparation en minutes
    mtbf = models.FloatField(null=True, blank=True)
    criticite = models.IntegerField(null=True, default=1)
    maintenance_planifiee = models.DateField(null=True, blank=True)

    type = models.CharField(max_length=100, null=True, blank=True)   
    media = models.ImageField(upload_to='equipements/', null=True, blank=True)
    periodicite_maintenance = models.IntegerField(null=True, blank=True)

    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        related_name='sous_equipements',
        null=True,
        blank=True
    )

    class Meta:
        db_table = 'equipement'

    def __str__(self):
        return self.nom


class SousSystemeHasEquipement(models.Model):
    sous_systeme = models.ForeignKey(SousSysteme, on_delete=models.CASCADE, related_name="sous_systeme_eq")
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE, related_name="equipement_eq")
    systeme=models.ForeignKey(Systeme, on_delete=models.CASCADE, related_name="systemes_eq",default=1)
    #suuprimer
    status = models.CharField(max_length=50, null=True, blank=True)
    quantite= models.IntegerField(null=True, blank=True)
    
    class Meta:
        db_table = 'sous_systeme_has_equipement'
    def __str__(self):
          return f"{self.systeme} {self.sous_systeme} {self.equipement}"

from django.db import models

class EquipementSystemeInstance(models.Model):
    sous_systeme_has_instance = models.ForeignKey(
        'SousSystemeHasEquipement',
        on_delete=models.CASCADE,
        related_name="equipement_systeme_instances"
    )

    _numero_serie = models.CharField(
    max_length=100,
    unique=False,  # ✅ enlever pour migrer
    blank=True,
    db_column="numero_serie"
)


    date_installation = models.DateField(null=True, blank=True)
    date_derniere_maintenance = models.DateField(null=True, blank=True)
    date_remplacement = models.DateField(null=True, blank=True)
    destination = models.CharField(max_length=100, blank=True, default="reparation")
    statut = models.CharField(max_length=50, null=True)

    PREFIXE = "EQ-"

    class Meta:
        db_table = 'equipement_Systeme_instance'

    def __str__(self):
        return f"{self.numero_serie} -- {self.sous_systeme_has_instance.equipement.nom} -- {self.sous_systeme_has_instance.systeme.nom}"

    @property
    def numero_serie(self):
        if self._numero_serie and self._numero_serie.startswith(self.PREFIXE):
            return self._numero_serie[len(self.PREFIXE):]
        return self._numero_serie

    @numero_serie.setter
    def numero_serie(self, value):
        if not value.startswith(self.PREFIXE):
            self._numero_serie = f"{self.PREFIXE}{value}"
        else:
            self._numero_serie = value

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)
        if is_new and not self._numero_serie:
            self._numero_serie = f"{self.PREFIXE}{self.pk}"
            # Update only the numero_serie field
            self.__class__.objects.filter(pk=self.pk).update(_numero_serie=self._numero_serie)



###########################################""

class Mission(models.Model):
    bion_mission = models.ForeignKey(
        Bion,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="bataillon_missions"
    )
    cie_missions = models.ManyToManyField(Cie, related_name="missions_cie", blank=True)
    sion_missions = models.ManyToManyField(Sion, related_name="missions_sion", blank=True)
    systeme_missions = models.ManyToManyField(Systeme, related_name="missions_systeme", blank=True)

    objet = models.CharField(max_length=100, null=True)
    date_debut_mission = models.DateField(null=True, blank=True)
    date_fin_mission = models.DateField(null=True, blank=True)
    reference_mission = models.CharField(max_length=100, null=True)
    description = models.TextField(null=True, blank=True)
    lieu = models.CharField(max_length=100, null=True)
    longitude = models.CharField(max_length=100, null=True)
    latitude = models.CharField(max_length=100, null=True)

    class Meta:
        db_table = 'mission'

    def __str__(self):
        return f"{self.objet or 'Mission'} - {self.reference_mission or ''}"

    

    
class Servant(models.Model):
    nom = models.CharField(max_length=45, null=True)
    prenom = models.CharField(max_length=45, null=True)
    grade = models.CharField(max_length=45, null=True)
    matricule = models.CharField(max_length=45, null=True)
    date_affectation = models.DateField(null=True, blank=True)
    specialite = models.CharField(max_length=100, null=True)
    photo=models.ImageField(upload_to='personnels/', null=True, blank=True)

    class Meta:
        db_table = 'servant'

    def __str__(self):
        return f"{self.nom} {self.prenom}"


























class Formation(models.Model):
    intitule = models.CharField(max_length=100, null=True)
    reference = models.CharField(max_length=100, null=True)
    description = models.TextField(null=True, blank=True)
    badge=models.ImageField(upload_to='equipements/', null=True, blank=True)

    class Meta:
        db_table = 'formation'


class ServantHasFormation(models.Model):
    servant = models.ForeignKey(Servant, on_delete=models.CASCADE, related_name="formations")
    formation = models.ForeignKey(Formation, on_delete=models.CASCADE, related_name="servants")
    date_debut = models.DateField(null=True, blank=True)
    date_fin = models.DateField(null=True, blank=True)
    reference= models.CharField(max_length=100, null=True)

    class Meta:
        db_table = 'servant_has_formation'


#############################################################
class Stock(models.Model):
    
    lieu = models.CharField(max_length=100, null=True)
    place_armes = models.CharField(max_length=100, null=True)
    unite = models.CharField(max_length=50, null=True)

    class Meta:
        db_table = 'stock'
    
    def __str__(self):
        return self.unite


class StockHasEquipement(models.Model):
    sous_systeme = models.ForeignKey(SousSysteme, on_delete=models.CASCADE, related_name="sous_systeme_stock")
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE, related_name="equipement_stock")
    stock=models.ForeignKey(Stock, on_delete=models.CASCADE, related_name="systeme_stock",default=1)
    #to delete
    status = models.CharField(max_length=50, null=True)
    quantite= models.IntegerField(null=True,blank=True)
    quantite_critique=models.IntegerField(null=True,blank=True)
    tracker = FieldTracker()

    class Meta:
        db_table = 'stock_has_equipement'
    def __str__(self):
          return f"{self.stock} {self.sous_systeme} {self.equipement}"





class EquipementStockInstance(models.Model):
    stock_has_instance = models.ForeignKey(StockHasEquipement, on_delete=models.CASCADE, related_name="equipement_systeme_instances",default=1)
    numero_serie = models.CharField(max_length=100, blank=True, default="N/A")  # Unique for each deployed equipment instance
    date_entree = models.DateField(null=True, blank=True)
    reference_entree= models.CharField(max_length=50, null=True, blank=True) 
    date_sortie = models.DateField(null=True, blank=True)
    date_fin_garantie = models.DateField(null=True, blank=True)
    destination=models.CharField(max_length=50, null=True, blank=True) 
    reference_sortie= models.CharField(max_length=50, null=True, blank=True) 
    tracker = FieldTracker()

    class Meta:
        db_table = 'equipement_instance_stock'

    def __str__(self):
        return f"{self.stock_has_instance.equipement.nom} : {self.numero_serie} "
    #{self.numero_serie}

   



######################################################################" 



#################################################################################################################################
class Notification(models.Model):
    TYPE_CHOICES = [
        ("PANNE", "Panne déclarée"),
        ("STOCK", "Changement de stock"),
        ("PREVENTIVE", "Maintenance preventive déclarée"),
        ("CORRECTIVE", "Maintenance corrective déclarée"),
    ]

    type_notification = models.CharField(max_length=100, choices=TYPE_CHOICES)
    message = models.TextField()
    is_read = models.BooleanField(default=False)  # Renommé de "vue" à "is_read"
    date = models.DateTimeField(auto_now_add=True)
    class Meta:
        db_table = 'Notification'
    def __str__(self):
        return f"{self.get_type_notification_display()} - {self.message}"
    

    
    




###########################################################################

class Technicien(models.Model):
    nom = models.CharField(max_length=45, null=True)
    prenom = models.CharField(max_length=45, null=True)
    specialite = models.CharField(max_length=100, null=True)
    matricule = models.CharField(max_length=45, unique=True)
    date_affectation= models.DateField(null=True, blank=True)
    grade= models.CharField(max_length=45, null=True)
    photo=models.ImageField(upload_to='personnels/', null=True, blank=True)
    
    class Meta:
        db_table = 'Technicien'
    
    def __str__(self):
        return f"{self.nom} {self.prenom}"




class TechnicienHasFormation(models.Model):
    technicien = models.ForeignKey(Technicien, on_delete=models.CASCADE)
    formation = models.ForeignKey(Formation, on_delete=models.CASCADE)
    date_debut = models.DateField(null=True, blank=True)
    date_fin = models.DateField(null=True, blank=True)
    reference= models.CharField(max_length=100, null=True)
     

    class Meta:
        db_table = 'Technicien_has_Formation'



##########################################################################""
class Caracteristique(models.Model):
    nom = models.CharField(max_length=100, null=True)

    class Meta:
        db_table = 'caracteristique'
    def __str__(self):
        return self.nom 

class Valeur(models.Model):
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE, related_name="valeurs")
    caracteristique = models.ForeignKey(Caracteristique, on_delete=models.CASCADE, related_name="valeurs")
    valeur = models.CharField(max_length=255, null=True)

    class Meta:
        db_table = 'valeur'

    def __str__(self):
        return self.valeur



class Media(models.Model):
    image = models.ImageField(upload_to='maintenance/')  # Champ pour téléverser l'image
    description = models.CharField(max_length=255, blank=True, null=True)  # Description optionnelle

    def __str__(self):
        return self.image.name




class Maintenance(models.Model):
    type_maintenance = models.CharField(max_length=100, null=True)
    statut_sous_systeme = models.CharField(max_length=50, null=True)
    statut_systeme = models.CharField(max_length=50, null=True)
    statut_equipement = models.CharField(max_length=50, null=True)
    equipement_de_rechange=models.ForeignKey(EquipementStockInstance, on_delete=models.CASCADE, related_name="rechange_instance", null=True, blank=True)
    date_maintenance=models.DateField(null=True, blank=True)
    duree = models.IntegerField(null=True, blank=True)
    description = models.TextField(blank=True, null=True) 
    equipementsystemeInstance= models.ForeignKey(EquipementSystemeInstance, on_delete=models.CASCADE, null=True, blank=True)
    bion = models.ForeignKey(Bion, on_delete=models.CASCADE, null=True, blank=True)
    cie = models.ForeignKey(Cie, on_delete=models.CASCADE, null=True, blank=True)
    sion = models.ForeignKey(Sion, on_delete=models.CASCADE, null=True, blank=True)
    systeme = models.ForeignKey(Systeme, on_delete=models.CASCADE, null=True, blank=True)
    sous_systeme = models.ForeignKey(SousSysteme, on_delete=models.CASCADE, null=True, blank=True)
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE, related_name="equipement_maintenance", null=True, blank=True)
    reference_declaration_panne= models.TextField(blank=True, null=True)
    reference_entree= models.TextField(blank=True, null=True)
    reference_sortie= models.TextField(blank=True, null=True)
    destination= models.TextField(blank=True, null=True)
     # Relation avec Technicien
    technicien = models.ForeignKey(Technicien, on_delete=models.SET_NULL, null=True, blank=True)
    # Si la sortie de stock concerne une instance d'équipement spécifique
    date_sortie_stock = models.DateField(null=True, blank=True) # Si c'est une relation
    media = models.ManyToManyField(Media, blank=True)
    tracker = FieldTracker()
    

    class Meta:
        db_table = 'maintenance'

    def __str__(self):
        return f"{self.type_maintenance} - {self.date_maintenance}"
    



class TechnicienHasMaintenance(models.Model):
    technicien = models.ForeignKey(Technicien, on_delete=models.CASCADE,default=1)
    maintenance = models.ForeignKey(Maintenance, on_delete=models.CASCADE)
     

    class Meta:
        db_table = 'Technicien_has_Maintenance'




 


class MaintenanceHistory(models.Model):
    STATUS_CHOICES = [
        ('pending', 'En panne'),
        ('in_progress', 'En cours de r"paration'),
        ('completed', 'Réparé'),
        ('not_repairable', 'Non réparable'),
        ('external_repair', 'Réparation externe'),
    ]

    # Informations générales
    maintenance = models.ForeignKey('Maintenance', on_delete=models.CASCADE, null=True, blank=True)
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE)
    equipementsystemeInstance= models.ForeignKey(EquipementSystemeInstance, on_delete=models.CASCADE, null=True, blank=True)
    systeme = models.ForeignKey(Systeme, on_delete=models.CASCADE)  # Lien avec le système concerné
    date_panne = models.DateField(null=True, blank=True, verbose_name="Date de panne")
    description_panne = models.TextField(blank=True, null=True, verbose_name="Description de la panne")
    statut = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
       
    )

    # Étape 1 : Entrée en Atelier
    statut_atelier = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="Statut - Entrée en Atelier"
    )
    date_entree_atelier = models.DateField(null=True, blank=True, verbose_name="Date d'entrée en atelier")
    description_entree_atelier = models.TextField(blank=True, null=True, verbose_name="Description entrée atelier")
    lieu_reparation = models.CharField(max_length=100, blank=True, null=True, verbose_name="Lieu de réparation")

    # Étape 2 : Réparation Externe
    statut_externe = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="Statut - Réparation Externe"
    )
    date_envoi_reparation_externe = models.DateField(null=True, blank=True, verbose_name="Date d'envoi en réparation externe")
    description_envoi_reparation_externe = models.TextField(blank=True, null=True, verbose_name="Description envoi réparation externe")
    lieu_reparation_externe = models.CharField(max_length=100, blank=True, null=True, verbose_name="Lieu de réparation externe")

    # Étape 3 : Détails de la Réparation
    statut_reparation = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="Statut - Détails de la Réparation"
    )
    date_reparation = models.DateField(null=True, blank=True, verbose_name="Date de réparation")
    description_reparation = models.TextField(blank=True, null=True, verbose_name="Description de la réparation")

    class Meta:
        db_table = 'maintenanceHistory'
        verbose_name = "Historique de maintenance"
        verbose_name_plural = "Historiques de maintenance"

    def __str__(self):
        return f"Historique de {self.equipement.nom}"

class MissionHasTechnicien(models.Model):
    mission = models.ForeignKey(
        Mission,
        on_delete=models.CASCADE,
        related_name="technicien_missions"
    )
    technicien = models.ManyToManyField(Technicien, related_name="missions_technicien")

    class Meta:
        db_table = 'mission_technicien'

    def __str__(self):
        return f"Techniciens de la mission {self.mission.reference_mission}"


class MissionHasServant(models.Model):
    mission = models.ForeignKey(
        Mission,
        on_delete=models.CASCADE,
        related_name="servant_missions"
    )
    servant = models.ManyToManyField(Servant, related_name="missions_servant")

    class Meta:
        db_table = 'mission_servant'

    def __str__(self):
        return f"Servants de la mission {self.mission.reference_mission}"


class MissionHasServantRole(models.Model):
    missionhasservant = models.ForeignKey(MissionHasServant, on_delete=models.CASCADE)
    servant = models.ForeignKey(Servant, on_delete=models.CASCADE)
    role = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'mission_has_servant_role'
        unique_together = ('missionhasservant', 'servant')

    def __str__(self):
        return f"{self.servant.nom} ({self.role}) - Mission {self.missionhasservant.mission.reference_mission}"


class MissionHasTechnicienRole(models.Model):
    missionhastechnicien = models.ForeignKey(MissionHasTechnicien, on_delete=models.CASCADE)
    technicien = models.ForeignKey(Technicien, on_delete=models.CASCADE)
    role = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'mission_has_technicien_role'
        unique_together = ('missionhastechnicien', 'technicien')

    def __str__(self):
        return f"{self.technicien.nom} ({self.role}) - Mission {self.missionhastechnicien.mission.reference_mission}"


class Conducteur(models.Model):
    nom = models.CharField(max_length=45, null=True)
    prenom = models.CharField(max_length=45, null=True)
    grade = models.CharField(max_length=45, null=True)
    matricule = models.CharField(max_length=45, null=True)
    date_affectation = models.DateField(null=True, blank=True)
    specialite = models.CharField(max_length=100, null=True)
    photo = models.ImageField(upload_to='personnels/', null=True, blank=True)

    class Meta:
        db_table = 'conducteur'

    def __str__(self):
        return f"{self.nom} {self.prenom}"


class ConducteurHasFormation(models.Model):
    conducteur = models.ForeignKey(Conducteur, on_delete=models.CASCADE, related_name="formations")
    formation = models.ForeignKey(Formation, on_delete=models.CASCADE, related_name="conducteurs")
    date_debut = models.DateField(null=True, blank=True)
    date_fin = models.DateField(null=True, blank=True)
    reference = models.CharField(max_length=100, null=True)

    class Meta:
        db_table = 'conducteur_has_formation'


class MissionHasConducteur(models.Model):
    mission = models.ForeignKey(
        Mission,
        on_delete=models.CASCADE,
        related_name="conducteur_missions"
    )
    conducteur = models.ManyToManyField(Conducteur, related_name="missions_conducteur")

    class Meta:
        db_table = 'mission_has_conducteur'

    def __str__(self):
        return f"Conducteurs de la mission {self.mission.reference_mission}"


class MissionHasConducteurRole(models.Model):
    missionhasconducteur = models.ForeignKey(MissionHasConducteur, on_delete=models.CASCADE)
    conducteur = models.ForeignKey(Conducteur, on_delete=models.CASCADE)
    role = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'mission_has_conducteur_role'
        unique_together = ('missionhasconducteur', 'conducteur')

    def __str__(self):
        return f"{self.conducteur.nom} ({self.role}) - Mission {self.missionhasconducteur.mission.reference_mission}"


class AutrePersonnel(models.Model):
    nom = models.CharField(max_length=45, null=True)
    prenom = models.CharField(max_length=45, null=True)
    matricule = models.CharField(max_length=45, null=True, blank=True)
    unite = models.CharField(max_length=100, null=True, blank=True)
    date_arrivee = models.DateField(null=True, blank=True)
    date_depart = models.DateField(null=True, blank=True)
    specialite = models.CharField(max_length=100, null=True, blank=True)
    
    class Meta:
        db_table = 'autre_personnel'
        
    def __str__(self):
        return f"{self.nom} {self.prenom}"


class MissionHasAutrePersonnel(models.Model):
    mission = models.ForeignKey(
        Mission,
        on_delete=models.CASCADE,
        related_name="autre_personnel_missions"
    )
    autre_personnel = models.ManyToManyField(AutrePersonnel, related_name="missions_autre_personnel")
    
    class Meta:
        db_table = 'mission_autre_personnel'
        
    def __str__(self):
        return f"Autres personnels de la mission {self.mission.reference_mission}"


class MissionHasAutrePersonnelRole(models.Model):
    missionhasautrepersonnel = models.ForeignKey(MissionHasAutrePersonnel, on_delete=models.CASCADE)
    autre_personnel = models.ForeignKey(AutrePersonnel, on_delete=models.CASCADE)
    role = models.CharField(max_length=100, null=True, blank=True)
    
    class Meta:
        db_table = 'mission_has_autre_personnel_role'
        unique_together = ('missionhasautrepersonnel', 'autre_personnel')
        
    def __str__(self):
        return f"{self.autre_personnel.nom} ({self.role}) - Mission {self.missionhasautrepersonnel.mission.reference_mission}"
