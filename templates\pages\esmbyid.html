{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
    #details-image {
        margin-top: 20px;
        width: 100%;
        max-width: 400px;
        height: 250px;
        object-fit: contain;
        display: block;
        margin: auto;
        border-radius: 8px;
        cursor: pointer;
    }

    .mapster_tooltip {
        position: absolute;
        display: none;
        background-color: rgba(0, 0, 0, 0.8);
        color: rgb(217, 244, 11);
        padding: 8px;
        border-radius: 5px;
        font-size: 14px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }
</style>

<div class="row" style="margin-left: 10px; margin-top: 10px;">
    <!-- Carte interactive -->
    <div class="col-xl-6" style="margin-top: 20px;">
        <div class="card mb-5" style="height: 600px;">
            <div class="card-header">
                <i class="fas fa-shield-alt me-1"></i> ESM
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div id="carouselecm" class="carousel slide" data-bs-ride="carousel" data-bs-interval="2000">
                    <div class="carousel-inner">
                        <div class="carousel-item active">
                            <img id="resize-img" alt="ESM Map" src="{% static 'img/esm.png' %}" usemap="#image-map" class="img-fluid"/>
                            <map name="image-map">
                                <area data-title="esm-sensor" data-tooltip="Système de détection avancé"
                                      data-image="{% static 'img/cuas.png' %}"
                                      coords="271,179,123,104" shape="rect">
                                <area data-title="CASQUE" data-tooltip="Générateur principal"
                                      data-image="{% static 'img/cuas.png' %}"
                                      coords="276,583,452,474" shape="rect">
                                <area data-title="MAT" data-tooltip="Mât de support"
                                      data-image="{% static 'img/cuas.png' %}"
                                      coords="194,184,223,416" shape="rect">
                            </map>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Affichage des détails -->
    <div class="col-md-6 col-xl-6" style="margin-top: 20px;">
        <div class="card" id="details-content">
            <div class="card-header">
                <i class="fas fa-shield-alt me-1"></i> Détail
            </div>

            <!-- Image avec lien modal -->
          <div class="p-3 text-center">
    <a id="details-image-link" href="#" data-bs-toggle="modal" data-bs-target="#zoomModal" style="display: none;">
        <img id="details-image" src="" alt="Aperçu équipement" class="img-fluid rounded" style="max-width: 100%; max-height: 250px; object-fit: contain;">
    </a>
</div>

            <div class="card-body">
                <p class="card-text" id="details-text">Veuillez cliquer sur l'équipement dans l'image pour obtenir des détails supplémentaires.</p>
                <table id="systemes-tables" class="table table-bordered" style="display: none;">
                    <thead>
                        <tr>
                            <th>Id</th>
                            <th>Système</th>
                            <th>Numéro de série</th>
                            <th>Statut</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Zoom -->
<div class="modal fade" id="zoomModal" tabindex="-1" aria-labelledby="zoomModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl modal-dialog-centered">
    <div class="modal-content bg-dark">
      <div class="modal-body p-0">
        <img id="zoomed-image" src="" alt="Zoom" class="img-fluid w-100" style="object-fit: contain; max-height: 90vh;">
      </div>
    </div>
  </div>
</div>

<!-- Tooltip -->
<div id="custom-tooltip" class="mapster_tooltip"></div>

<script>
    $(function () {
        var $image = $('#resize-img');
        $image.mapster({
            mapKey: 'data-title',
            stroke: true,
            strokeWidth: 2,
            strokeColor: '#FFEB00',
            singleSelect: true
        });

        $('area').on('mouseover', function (e) {
            var tooltipText = $(this).attr('data-tooltip');
            if (tooltipText) {
                $("#custom-tooltip").text(tooltipText).fadeIn();
            }
        }).on('mouseout', function () {
            $("#custom-tooltip").fadeOut();
        });

        $(document).on("mousemove", function (e) {
            $("#custom-tooltip").css({
                top: e.pageY + 15 + "px",
                left: e.pageX + 15 + "px"
            });
        });

        $('area').on('click', function (e) {
            e.preventDefault();
            var pieceNom = $(this).attr('data-title');
            var systemeId = "{{ id }}";

            $("#systemes-tables").hide();
            $("#details-text").html("Chargement des données...");
            $("#details-image").attr("src", "");
            $("#zoomed-image").attr("src", "");
            $("#details-image-link").hide();
            $("#systemes-tables tbody").empty();

            $.get('/get_equipements/', { partie: pieceNom, id: systemeId }, function (data) {
                if (data.error) {
                    $("#details-text").html("<p style='color: red;'>" + data.error + "</p>");
                    return;
                }

                $("#details-text").html("<p><strong>Désignation :</strong> " + data.nom + "</p>");
                if (data.image) {
                    $("#details-image").attr("src", data.image);
                    $("#zoomed-image").attr("src", data.image);
                    $("#details-image-link").show();
                }

                var compteur = 1;
                if (data.systemes.length > 0) {
                    var systemesTableBody = $("#systemes-tables tbody");
                    data.systemes.forEach(function (systeme) {
                        var row = "<tr>" +
                            "<td>" + compteur++ + "</td>" +
                            "<td>" + systeme.systeme_nom + "</td>" +
                            "<td>" + systeme.numero_serie + "</td>" +
                            "<td><span class='badge " + (['operationnel', 'ops'].includes(systeme.statut.toLowerCase()) ? 'bg-success' : 'bg-danger') + "'>" + systeme.statut + "</span></td>" +
                            "<td class='d-flex justify-content-center gap-2'>" +
                                "<a href='/pages/fiche_equipement/" + data.equipement_id + "/" + systeme.id + "' class='btn btn-success btn-sm'>" +
                                    "<i class='fa-solid fa-eye fa-sm'></i>" +
                                "</a>" +
                            "</td>" +
                        "</tr>";
                        systemesTableBody.append(row);
                    });
                    $("#systemes-tables").show();
                } else {
                    $("#details-text").append("<p style='color: red;'>Aucun système trouvé.</p>");
                }
            }).fail(function () {
                $("#details-text").html("<p style='color: red;'>Erreur de communication avec le serveur.</p>");
            });
        });
    });
</script>
{% endblock %}
