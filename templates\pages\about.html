 <!-- Timeline 7 - <PERSON>trap Brain Component -->

 {% extends 'base.html' %}
{% load static %}

{% block content %}
<section class="bsb-timeline-7 bg-light py-3 py-md-5 py-xl-8">
	<div class="container">



		<div class="card-body">
			<div class="accordion" id="accordionExample">
				<div class="accordion-item">
					<h2 class="accordion-header">
						<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
							<i class="fas fa-shield-alt me-1"></i> Caractéristiques de l'équipement :
						</button>
					</h2>
					<div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
						<div class="accordion-body">
							<div class="card mb-4">
								<div class="card-body">
									<table id="dynamicTable" class="table table-striped">
										<thead>
											<tr id="tableHeader"></tr>
										</thead>
										<tbody></tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="accordion-item">
					<h2 class="accordion-header">
						<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
							<i class="fa-solid fa-circle-chevron-down"></i> Historique de la maintenance :
						</button>
					</h2>
					<div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
						<div class="accordion-body">
							<table id="datatablesSimple" class="table table-bordered">
								<thead class="text-center">
									<tr class="text-center">
										<th class="text-center">Date</th>
										<th class="text-center">Type de maintenance</th>
										<th class="text-center">Statut</th>
										<th class="text-center">Technicien</th>
										<th class="text-center">Description</th>
										 
									</tr>
								</thead>
							
								<tbody>
									{% for maintenance in maintenances %}
									<tr>
										<td class="text-center">{{ maintenance.date_maintenance }}</td>
										<td class="text-center">{{ maintenance.type_maintenance }}</td>
										<td class="text-center">{{ maintenance.statut_equipement }}</td>
										<td class="text-center">{{ maintenance.technicien.nom }}</td>
										<td class="text-center">{{ maintenance.description }}</td>
									</tr>
								{% endfor %}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>

 
	</div>
  </section>

  {% endblock %}