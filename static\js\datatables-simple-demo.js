// Attendre que le DOM soit complètement chargé
window.addEventListener('DOMContentLoaded', (event) => {
    // Sélectionner tous les tableaux à initialiser avec leurs identifiants spécifiques
    const tables = document.querySelectorAll('#datatablesSimple, #systemes-table');

    // Vérifier si DataTables est disponible pour éviter les erreurs
    if (typeof simpleDatatables === 'undefined') {
        console.error("La bibliothèque simpleDatatables n'est pas chargée.");
        return;
    }

    // Initialiser chaque tableau sélectionné
    tables.forEach((table) => {
        if (table) {
            new simpleDatatables.DataTable(table, {
                scrollX: true,  // Activer le défilement horizontal
                responsive: true,  // Rendre le tableau responsive
                labels: {
                    placeholder: "Rechercher...",  // Texte du champ de recherche
                    noRows: "Aucune donnée disponible",  // Message si le tableau est vide
                },
                perPage: 10,  // Nombre de lignes par page
                perPageSelect: [5, 10, 20, 50],  // Options pour le nombre de lignes par page
                layout: {
                    top: "{search}",  // Ajouter la barre de recherche en haut
                    bottom: "{info}{pager}"  // Informations de pagination en bas
                },
                // Ajouter une classe personnalisée pour centrer le contenu
                columnDefs: [
                    { className: "dt-center", targets: "_all" } // Appliquer la classe à toutes les colonnes
                ]
            });
        }
    });

    // Ajouter un style CSS pour centrer le contenu
    const style = document.createElement('style');
    style.innerHTML = `
        /* Centrer horizontalement et verticalement */
        .dt-center {
            text-align: center !important; /* Centre horizontalement */
            vertical-align: middle !important; /* Centre verticalement */
        }
    `;
    document.head.appendChild(style);
});