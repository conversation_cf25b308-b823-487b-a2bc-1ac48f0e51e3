from django.db import models
from model_utils import FieldTracker

# --------- STRUCTURE ORGANISATIONNELLE ---------

class Bion(models.Model):
    nom = models.CharField(max_length=45, null=True)
    date_creation = models.DateField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True)
    class Meta:
        db_table = 'bion'
    def __str__(self):
        return self.nom or ""

class Cie(models.Model):
    nom = models.CharField(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True)
    bion = models.ForeignKey(Bion, on_delete=models.CASCADE, related_name="cies", default=1)
    date_mise_en_ouevre = models.DateField(null=True, blank=True)
    class Meta:
        db_table = 'cie'
    def __str__(self):
        return self.nom or ""

class Sion(models.Model):
    nom = models.CharField(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
    status = models.Char<PERSON>ield(max_length=50, null=True)
    cie = models.ForeignKey(Cie, on_delete=models.CASCADE, related_name="sions", default=1)
    date_mise_en_ouevre = models.DateField(null=True, blank=True)
    class Meta:
        db_table = 'sion'
    def __str__(self):
        return self.nom or ""

class Systeme(models.Model):
    nom = models.CharField(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
    date_mise_en_oeuvre = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True)
    serial_number = models.CharField(max_length=50, null=True)
    product_number = models.CharField(max_length=50, null=True)
    class Meta:
        db_table = 'systeme'
    def __str__(self):
        return self.nom or ""

class SousSysteme(models.Model):
    nom = models.CharField(max_length=45, null=True)
    description = models.TextField(null=True, blank=True)
    class Meta:
        db_table = 'sous_systeme'
    def __str__(self):
        return self.nom or ""

class SystemeHasSousSysteme(models.Model):
    systeme = models.ForeignKey(Systeme, on_delete=models.CASCADE)
    sous_systeme = models.ForeignKey(SousSysteme, on_delete=models.CASCADE)
    class Meta:
        unique_together = ('systeme', 'sous_systeme')
        db_table = 'systeme_Has_Sous_Systeme'
    def __str__(self):
        return f"{self.systeme.nom} - {self.sous_systeme.nom}"

# --------- EQUIPEMENTS ET STOCK ---------

class Equipement(models.Model):
    nom = models.CharField(max_length=100, null=True)
    constructeur = models.CharField(max_length=100, null=True, blank=True)
    product_numbre = models.CharField(max_length=100, null=True)
    date_fin_garantie = models.DateField(null=True, blank=True)
    mttr = models.FloatField(null=True, blank=True)
    mtbf = models.FloatField(null=True, blank=True)
    criticite = models.IntegerField(null=True, default=1)
    maintenance_planifiee = models.DateField(null=True, blank=True)
    type = models.CharField(max_length=100, null=True, blank=True)
    media = models.ImageField(upload_to='equipements/', null=True, blank=True)
    periodicite_maintenance = models.IntegerField(null=True, blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, related_name='sous_equipements', null=True, blank=True)
    class Meta:
        db_table = 'equipement'
    def __str__(self):
        return self.nom or ""

class SousSystemeHasEquipement(models.Model):
    sous_systeme = models.ForeignKey(SousSysteme, on_delete=models.CASCADE, related_name="sous_systeme_eq")
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE, related_name="equipement_eq")
    systeme = models.ForeignKey(Systeme, on_delete=models.CASCADE, related_name="systemes_eq", default=1)
    status = models.CharField(max_length=50, null=True, blank=True)
    quantite = models.IntegerField(null=True, blank=True)
    class Meta:
        db_table = 'sous_systeme_has_equipement'
    def __str__(self):
        return f"{self.systeme} {self.sous_systeme} {self.equipement}"

class EquipementSystemeInstance(models.Model):
    sous_systeme_has_instance = models.ForeignKey('SousSystemeHasEquipement', on_delete=models.CASCADE, related_name="equipement_systeme_instances")
    _numero_serie = models.CharField(max_length=100, blank=True, db_column="numero_serie")
    date_installation = models.DateField(null=True, blank=True)
    date_derniere_maintenance = models.DateField(null=True, blank=True)
    date_remplacement = models.DateField(null=True, blank=True)
    destination = models.CharField(max_length=100, blank=True, default="reparation")
    statut = models.CharField(max_length=50, null=True)
    PREFIXE = "EQ-"
    class Meta:
        db_table = 'equipement_Systeme_instance'
    def __str__(self):
        return f"{self.numero_serie} -- {self.sous_systeme_has_instance.equipement.nom} -- {self.sous_systeme_has_instance.systeme.nom}"
    @property
    def numero_serie(self):
        if self._numero_serie and self._numero_serie.startswith(self.PREFIXE):
            return self._numero_serie[len(self.PREFIXE):]
        return self._numero_serie
    @numero_serie.setter
    def numero_serie(self, value):
        if not value.startswith(self.PREFIXE):
            self._numero_serie = f"{self.PREFIXE}{value}"
        else:
            self._numero_serie = value
    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)
        if is_new and not self._numero_serie:
            self._numero_serie = f"{self.PREFIXE}{self.pk}"
            self.__class__.objects.filter(pk=self.pk).update(_numero_serie=self._numero_serie)

class Stock(models.Model):
    lieu = models.CharField(max_length=100, null=True)
    place_armes = models.CharField(max_length=100, null=True)
    unite = models.CharField(max_length=50, null=True)
    class Meta:
        db_table = 'stock'
    def __str__(self):
        return self.unite or ""

class StockHasEquipement(models.Model):
    sous_systeme = models.ForeignKey(SousSysteme, on_delete=models.CASCADE, related_name="sous_systeme_stock")
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE, related_name="equipement_stock")
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE, related_name="systeme_stock", default=1)
    status = models.CharField(max_length=50, null=True)
    quantite = models.IntegerField(null=True, blank=True)
    quantite_critique = models.IntegerField(null=True, blank=True)
    tracker = FieldTracker()
    class Meta:
        db_table = 'stock_has_equipement'
    def __str__(self):
        return f"{self.stock} {self.sous_systeme} {self.equipement}"

class EquipementStockInstance(models.Model):
    stock_has_instance = models.ForeignKey(StockHasEquipement, on_delete=models.CASCADE, related_name="equipement_systeme_instances", default=1)
    numero_serie = models.CharField(max_length=100, blank=True, default="N/A")
    date_entree = models.DateField(null=True, blank=True)
    reference_entree = models.CharField(max_length=50, null=True, blank=True)
    date_sortie = models.DateField(null=True, blank=True)
    date_fin_garantie = models.DateField(null=True, blank=True)
    destination = models.CharField(max_length=50, null=True, blank=True)
    reference_sortie = models.CharField(max_length=50, null=True, blank=True)
    tracker = FieldTracker()
    class Meta:
        db_table = 'equipement_instance_stock'
    def __str__(self):
        return f"{self.stock_has_instance.equipement.nom} : {self.numero_serie}"

# --------- PERSONNEL ---------

class Technicien(models.Model):
    nom = models.CharField(max_length=45, null=True)
    prenom = models.CharField(max_length=45, null=True)
    specialite = models.CharField(max_length=100, null=True)
    matricule = models.CharField(max_length=45, unique=True)
    date_affectation = models.DateField(null=True, blank=True)
    grade = models.CharField(max_length=45, null=True)
    photo = models.ImageField(upload_to='personnels/', null=True, blank=True)
    class Meta:
        db_table = 'Technicien'
    def __str__(self):
        return f"{self.nom} {self.prenom}"

class Servant(models.Model):
    nom = models.CharField(max_length=45, null=True)
    prenom = models.CharField(max_length=45, null=True)
    grade = models.CharField(max_length=45, null=True)
    matricule = models.CharField(max_length=45, null=True)
    date_affectation = models.DateField(null=True, blank=True)
    specialite = models.CharField(max_length=100, null=True)
    photo = models.ImageField(upload_to='personnels/', null=True, blank=True)
    class Meta:
        db_table = 'servant'
    def __str__(self):
        return f"{self.nom} {self.prenom}"

# --------- MAINTENANCE, MEC, WORKFLOW ---------

class Maintenance(models.Model):
    TYPE_MAINTENANCE = [
        ('CORRECTIVE', 'Corrective'),
        ('PREVENTIVE', 'Préventive'),
        ('PANNE', 'Déclaration de panne'),
        ('MAJ_LOGICIEL', 'Mise à jour logiciel'),
        ('UPGRADE', 'Mise à niveau constructeur'),
    ]
    ORIGINE_MAINTENANCE = [
        ('INTERNE', 'Interne'),
        ('UTILISATEUR', 'Utilisateur'),
        ('CONSTRUCTEUR', 'Constructeur'),
    ]
    STATUT_WORKFLOW = [
        ('NOUVELLE', 'Nouvelle'),
        ('PLANIFIEE', 'Planifiée'),
        ('EN_COURS', 'En cours'),
        ('CLOTUREE', 'Clôturée'),
    ]
    type_maintenance = models.CharField(max_length=100, choices=TYPE_MAINTENANCE, default='CORRECTIVE')
    type_specifique = models.CharField(max_length=100, choices=TYPE_MAINTENANCE, blank=True, null=True)
    origine = models.CharField(max_length=100, choices=ORIGINE_MAINTENANCE, blank=True, null=True, default='INTERNE')
    statut_workflow = models.CharField(max_length=50, choices=STATUT_WORKFLOW, default='NOUVELLE')
    date_maintenance = models.DateField(null=True, blank=True)
    duree = models.IntegerField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    statut_systeme = models.CharField(max_length=50, null=True)
    statut_sous_systeme = models.CharField(max_length=50, null=True)
    statut_equipement = models.CharField(max_length=50, null=True)
    equipement = models.ForeignKey(Equipement, on_delete=models.CASCADE, null=True, blank=True, related_name='maintenances')
    equipementsystemeInstance = models.ForeignKey(EquipementSystemeInstance, on_delete=models.SET_NULL, null=True, blank=True, related_name='maintenances')
    equipement_de_rechange = models.ForeignKey(EquipementStockInstance, on_delete=models.SET_NULL, null=True, blank=True, related_name='maintenances_rechange')
    technicien = models.ForeignKey(Technicien, on_delete=models.SET_NULL, null=True, blank=True)
    media = models.ManyToManyField('Media', blank=True, related_name="maintenances")
    class Meta:
        db_table = 'maintenance'
    def __str__(self):
        return f"Maintenance {self.get_type_maintenance_display()} du {self.date_maintenance or ''}"

# --------- NOTIFICATIONS ---------

class Notification(models.Model):
    TYPE_CHOICES = [
        ("PANNE", "Panne déclarée"),
        ("STOCK", "Changement de stock"),
        ("PREVENTIVE", "Maintenance préventive déclarée"),
        ("CORRECTIVE", "Maintenance corrective déclarée"),
        ("CLOTURE", "Maintenance clôturée"),
        ("UPGRADE", "Mise à niveau constructeur"),
    ]
    type_notification = models.CharField(max_length=100, choices=TYPE_CHOICES)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    date = models.DateTimeField(auto_now_add=True)
    class Meta:
        db_table = 'Notification'
    def __str__(self):
        return f"{self.get_type_notification_display()} - {self.message}"

class Media(models.Model):
    image = models.ImageField(upload_to='maintenance/')
    description = models.CharField(max_length=255, blank=True, null=True)
    def __str__(self):
        return self.image.name

# --------- HISTORIQUE ---------

class MaintenanceHistory(models.Model):
    maintenance = models.ForeignKey(Maintenance, on_delete=models.CASCADE, related_name="histories")
    date_entree_atelier = models.DateField(null=True, blank=True)
    description_entree_atelier = models.TextField(null=True, blank=True)
    lieu_reparation = models.CharField(max_length=100, null=True, blank=True)
    date_envoi_reparation_externe = models.DateField(null=True, blank=True)
    description_envoi_reparation_externe = models.TextField(null=True, blank=True)
    lieu_reparation_externe = models.CharField(max_length=100, null=True, blank=True)
    date_reparation = models.DateField(null=True, blank=True)
    description_reparation = models.TextField(null=True, blank=True)
    statut = models.CharField(max_length=50, null=True)
    class Meta:
        db_table = 'maintenance_history'
    def __str__(self):
        return f"Historique Maintenance {self.maintenance_id}"

# --------- FORMATION ---------

class Formation(models.Model):
    intitule = models.CharField(max_length=100, null=True)
    reference = models.CharField(max_length=100, null=True)
    description = models.TextField(null=True, blank=True)
    badge = models.ImageField(upload_to='equipements/', null=True, blank=True)
    class Meta:
        db_table = 'formation'

class TechnicienHasFormation(models.Model):
    technicien = models.ForeignKey(Technicien, on_delete=models.CASCADE)
    formation = models.ForeignKey(Formation, on_delete=models.CASCADE)
    date_debut = models.DateField(null=True, blank=True)
    date_fin = models.DateField(null=True, blank=True)
    reference = models.CharField(max_length=100, null=True)
    class Meta:
        db_table = 'Technicien_has_Formation'

class ServantHasFormation(models.Model):
    servant = models.ForeignKey(Servant, on_delete=models.CASCADE, related_name="formations")
    formation = models.ForeignKey(Formation, on_delete=models.CASCADE, related_name="servants")
    date_debut = models.DateField(null=True, blank=True)
    date_fin = models.DateField(null=True, blank=True)
    reference = models.CharField(max_length=100, null=True)
    class Meta:
        db_table = 'servant_has_formation'
