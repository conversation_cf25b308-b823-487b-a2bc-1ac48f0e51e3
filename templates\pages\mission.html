{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block content %}
<style>
#datatablesSimple td,
#datatablesSimple th,
#systemes-table td,
#systemes-table th {
    text-align: center !important;
    vertical-align: middle !important;
}
</style>

<div class="container-fluid px-4">
 <ol class="breadcrumb mb-4" style="margin-top: 10px;">
        <li class="breadcrumb-item">Déploiment</li>
        <li class="breadcrumb-item active"><a href="{% url 'mission' %}">Missions</a></li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i> Liste des missions
            </div>
            <div>
             
                <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addData">
                    <i class="fa-solid fa-plus"></i> Nouvelle mission
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table id="datatablesSimple" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Désignation</th>
                            <th>Unité déployée</th>
                            <th>Lieu</th>
                            <th>Date de début</th>
                            <th>Date de fin</th>
                            <th>Responsable</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mission in missions %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ mission.objet|default:"Non spécifié" }}</td>

                            <!-- Unité déployée -->
                            <td>
                                {% if mission.sion_missions.all or mission.cie_missions.all or mission.bion_mission %}
                                 {% if mission.bion_mission %}
                                -{{ mission.bion_mission.nom }}<br>
                                    {% endif %}    
                                  {% for cie in mission.cie_missions.all %}
                                    -{{ cie.nom }}<br>
                                    {% endfor %}

                                {% for sion in mission.sion_missions.all %}
                                    -{{ sion.nom }}<br>
                                    {% endfor %}
                                  
                                    
                                {% else %}
                                    Non spécifié
                                {% endif %}
                            </td>

                            <!-- Lieu -->
                            <td>{{ mission.lieu|default:"Non spécifié" }}</td>

                            <!-- Dates -->
                            <td>{{ mission.date_debut_mission|default:"Non spécifié" }}</td>
                            <td>{{ mission.date_fin_mission|default:"Non spécifié" }}</td>

                            <!-- Responsables -->
                            <td>
                                {% with responsables_by_mission|dictkey:mission.id as responsables %}
                                    {% if responsables %}
                                        {% for r in responsables %}
                                            {{ r.servant.grade }} {{ r.servant.nom }} {{ r.servant.prenom }}<br>
                                        {% endfor %}
                                    {% else %}
                                        Aucun responsable
                                    {% endif %}
                                {% endwith %}
                            </td>

                            <!-- Action -->
                            <td>
                                <a href="{% url 'fiche_mission' mission.id %}" class="btn btn-success btn-sm">
                                    <i class="fa-solid fa-eye fa-sm"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour création de mission -->
<div class="modal fade" id="addData" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addModalLabel">Créer une mission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
            </div>
            <div class="modal-body text-center">
                <a href="{% url 'addmission' %}" class="btn btn-danger">Créer une mission</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
