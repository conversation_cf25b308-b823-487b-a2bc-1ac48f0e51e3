# Generated by Django 5.1.6 on 2025-06-25 22:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pages', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AutrePersonnel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('prenom', models.Char<PERSON>ield(max_length=45, null=True)),
                ('matricule', models.CharField(blank=True, max_length=45, null=True)),
                ('unite', models.CharField(blank=True, max_length=100, null=True)),
                ('date_arrivee', models.DateField(blank=True, null=True)),
                ('date_depart', models.DateField(blank=True, null=True)),
                ('specialite', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'db_table': 'autre_personnel',
            },
        ),
        migrations.CreateModel(
            name='Conducteur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=45, null=True)),
                ('prenom', models.CharField(max_length=45, null=True)),
                ('grade', models.CharField(max_length=45, null=True)),
                ('matricule', models.CharField(max_length=45, null=True)),
                ('date_affectation', models.DateField(blank=True, null=True)),
                ('specialite', models.CharField(max_length=100, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='personnels/')),
            ],
            options={
                'db_table': 'conducteur',
            },
        ),
        migrations.CreateModel(
            name='ConducteurHasFormation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_debut', models.DateField(blank=True, null=True)),
                ('date_fin', models.DateField(blank=True, null=True)),
                ('reference', models.CharField(max_length=100, null=True)),
                ('conducteur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='formations', to='pages.conducteur')),
                ('formation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conducteurs', to='pages.formation')),
            ],
            options={
                'db_table': 'conducteur_has_formation',
            },
        ),
        migrations.CreateModel(
            name='MissionHasAutrePersonnel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('autre_personnel', models.ManyToManyField(related_name='missions_autre_personnel', to='pages.autrepersonnel')),
                ('mission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='autre_personnel_missions', to='pages.mission')),
            ],
            options={
                'db_table': 'mission_autre_personnel',
            },
        ),
        migrations.CreateModel(
            name='MissionHasConducteur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conducteur', models.ManyToManyField(related_name='missions_conducteur', to='pages.conducteur')),
                ('mission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conducteur_missions', to='pages.mission')),
            ],
            options={
                'db_table': 'mission_has_conducteur',
            },
        ),
        migrations.CreateModel(
            name='MissionHasAutrePersonnelRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(blank=True, max_length=100, null=True)),
                ('autre_personnel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.autrepersonnel')),
                ('missionhasautrepersonnel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.missionhasautrepersonnel')),
            ],
            options={
                'db_table': 'mission_has_autre_personnel_role',
                'unique_together': {('missionhasautrepersonnel', 'autre_personnel')},
            },
        ),
        migrations.CreateModel(
            name='MissionHasConducteurRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(blank=True, max_length=100, null=True)),
                ('conducteur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.conducteur')),
                ('missionhasconducteur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.missionhasconducteur')),
            ],
            options={
                'db_table': 'mission_has_conducteur_role',
                'unique_together': {('missionhasconducteur', 'conducteur')},
            },
        ),
    ]
