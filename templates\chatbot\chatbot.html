{% extends 'base.html' %}
{% load static %}
{% block content %}
<style>
    body {
        background-color: #f3f4f6;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .main-container {
        display: flex;
        min-height: 90vh;
    }

    .sidebar {
        margin-top: 10px;
        margin-left: 10px;
        width: 280px;
        background-color: #111827;
        color: #ffffff;
        padding: 20px;
        overflow-y: auto;
        border-top-left-radius: 16px;
        border-bottom-left-radius: 16px;
    }

    .sidebar h4 {
        margin-bottom: 20px;
        font-size: 20px;
        font-weight: bold;
    }

    .sidebar ul {
        list-style: none;
        padding-left: 0;
    }

    .sidebar li {
        padding: 10px 12px;
        background-color: #1f2937;
        border-radius: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: background 0.2s;
        font-size: 14px;
    }

    .sidebar li:hover {
        background-color: #374151;
    }

    .chat-container {
        flex: 1;
        background-color: #d5e2ea;
        padding: 40px;
        border-top-right-radius: 16px;
        border-bottom-right-radius: 16px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        margin-right: 10px;
        margin-top: 10px;
    }

    .chat-header {
        font-size: 26px;
        font-weight: 600;
        margin-bottom: 30px;
        color: #111827;
        text-align: center;
    }

    .form-label {
        font-weight: 500;
        color: #374151;
    }

    .form-control {
        height: 48px;
        font-size: 16px;
    }

    .btn-primary, .btn-secondary {
        font-size: 16px;
        padding: 12px 0;
    }

    .chat-output {
        background-color: #f9fafb;
        padding: 24px;
        border-radius: 12px;
        margin-top: 24px;
        font-size: 15px;
        line-height: 1.6;
        color: #1f2937;
    }

    .error-msg {
        color: #dc2626;
        margin-top: 20px;
        font-weight: bold;
    }

    .user-message {
        font-weight: 600;
        color: #1e3a8a;
    }

    .chatbot-message {
        margin-top: 12px;
        color: #111827;
    }

    code {
        background: #e5e7eb;
        padding: 4px 6px;
        border-radius: 6px;
    }

    #new-question-btn {
        display: none;
    }

    @media (max-width: 768px) {
        .main-container {
            flex-direction: column;
        }
        .sidebar {
            width: 100%;
            border-radius: 0;
        }
        .chat-container {
            border-radius: 0;
        }
    }
</style>

<div class="main-container">
    <!-- Sidebar gauche -->
    <div class="sidebar">
        <h4>🧠 Historique</h4>
        <ul>
            {% if conversation_log %}
                {% for name, q, resp in conversation_log %}
                    <li title="{{ q }}">
                        <a href="?replay={{ forloop.counter0 }}" style="color:inherit; text-decoration:none;">
                            {{ q|truncatechars:40 }}
                        </a>
                    </li>
                {% endfor %}
            {% else %}
                <li>Aucun historique pour l'instant.</li>
            {% endif %}
        </ul>
    </div>

    <!-- Contenu principal -->
    <div class="chat-container">
        <div class="chat-header">GE-R ChatBot</div>

        <!-- Formulaire d'envoi -->
        <div id="form-block">
            <form method="post" novalidate>
                {% csrf_token %}
                <div class="mb-3">
                    <label for="question" class="form-label">Posez votre question :</label>
                    <input type="text" class="form-control" id="question" name="question"
                           placeholder="Ex : Quelles sont les missions affectées au bataillon ?" required>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary" id="submit-btn">Envoyer</button>
                </div>
            </form>
        </div>

        <!-- Bouton "Nouvelle question" -->
        <div class="d-grid mt-3">
            <button class="btn btn-secondary" id="new-question-btn">Nouvelle question</button>
        </div>

        {% if error %}
            <div class="error-msg">{{ error }}</div>
        {% endif %}

        {% if question and final_response %}
            <div class="chat-output">
                <div class="user-message">{{ user_name }} :</div>
                <div class="mb-2">{{ question }}</div>
                <div class="chatbot-message"><strong>Chatbot :</strong> {{ final_response|safe }}</div>
            </div>
        {% endif %}

        {% if sql_history %}
            <hr>
            <h5>📊 Requêtes SQL générées</h5>
            <ul class="list-group mt-2">
                {% for date, sql in sql_history %}
                    <li class="list-group-item">
                        <code>{{ sql }}</code><br>
                        <small class="text-muted">{{ date }}</small>
                    </li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
</div>

<!-- Script gestion replay -->
<script>
document.addEventListener("DOMContentLoaded", function () {
    const formBlock = document.getElementById("form-block");
    const newBtn = document.getElementById("new-question-btn");
    const input = document.getElementById("question");
    const url = new URL(window.location.href);

    if (url.searchParams.has("replay")) {
        formBlock.style.display = "none";
        newBtn.style.display = "block";
    }

    newBtn.addEventListener("click", function () {
        // Supprimer replay de l'URL sans recharger la page
        url.searchParams.delete("replay");
        window.history.replaceState({}, document.title, url.pathname);
        // Réinitialiser champ
        if (input) input.value = "";
        // Réafficher formulaire
        formBlock.style.display = "block";
        newBtn.style.display = "none";
    });

    // Correction pour éviter de soumettre avec replay actif
    const form = document.querySelector("form");
    form.addEventListener("submit", function (e) {
        if (url.searchParams.has("replay")) {
            e.preventDefault();
            url.searchParams.delete("replay");
            window.history.replaceState({}, document.title, url.pathname);
            setTimeout(() => form.submit(), 50);
        }
    });
});
</script>
{% endblock %}
