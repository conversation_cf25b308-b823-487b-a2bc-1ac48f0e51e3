<nav class="sb-sidenav accordion sb-sidenav-dark" id="sidenavAccordion">
        <div class="sb-sidenav-menu">
            <div class="nav">
                <div class="sb-sidenav-menu-heading"></div>
                <a class="nav-link" href="{% url 'index' %}">
                    <div class="sb-nav-link-icon"><i class="fas fa-tachometer-alt"></i></div>
                    Tableau de board
                </a>

                 
                <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseLayouts" aria-expanded="false" aria-controls="collapseLayouts">
                    <div class="sb-nav-link-icon"><i class="fas fa-columns"></i></div>
                    Systèmes
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse" id="collapseLayouts" aria-labelledby="headingOne" data-bs-parent="#sidenavAccordion">
                    <nav class="sb-sidenav-menu-nested nav">
                        <!-- ESM avec sous-menu -->
                        <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseESM" aria-expanded="false" aria-controls="collapseESM">
                            <div class="sb-nav-link-icon"><i class="fas fa-folder"></i></div>
                            ESM
                            <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                        </a>
                        <div class="collapse" id="collapseESM" aria-labelledby="headingESM" data-bs-parent="#collapseLayouts">
                            <nav class="sb-sidenav-menu-nested nav">
                                <a class="nav-link" href="{% url 'systeme' id='ESM-11'%}">ESM-1</a>
                                <a class="nav-link" href="{% url 'systeme' id='ESM-12' %}">ESM-2</a>
                                <a class="nav-link" href="{% url 'systeme' id='ESM-13'%}">ESM-3</a>
                            </nav>
                        </div>
                
                        <!-- ECM avec sous-menu -->
                        <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseECM" aria-expanded="false" aria-controls="collapseECM">
                            <div class="sb-nav-link-icon"><i class="fas fa-cogs"></i></div>
                            ECM
                            <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                        </a>
                        <div class="collapse" id="collapseECM" aria-labelledby="headingECM" data-bs-parent="#collapseLayouts">
                            <nav class="sb-sidenav-menu-nested nav">
                                <a class="nav-link" href="{% url 'systeme' id='ECM-11'%}">ECM-1</a>
                                <a class="nav-link" href="{% url 'systeme' id='ECM-12'%}">ECM-2</a>
                                <a class="nav-link" href="{% url 'systeme' id='ECM-13'%}">ECM-3</a>
                            </nav>
                        </div>
                
                        <!-- CC avec sous-menu -->
                        <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseCC" aria-expanded="false" aria-controls="collapseCC">
                            <div class="sb-nav-link-icon"><i class="fas fa-server"></i></div>
                            CC
                            <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                        </a>
                        <div class="collapse" id="collapseCC" aria-labelledby="headingCC" data-bs-parent="#collapseLayouts">
                            <nav class="sb-sidenav-menu-nested nav">
                                <a class="nav-link" href="{% url 'systeme' id='BCC'%}">BCC</a>
                                <a class="nav-link" href="{% url 'systeme' id='CC-11'%}">CC-1</a>
                                <a class="nav-link" href="{% url 'systeme' id='CC-22'%}">CC-2</a>
                            </nav>
                        </div>
                
                        <!-- Maintenance avec sous-menu -->
                        <a class="nav-link" href="{% url 'systeme' id='MAINTENANCE'%}"><i class="fas fa-wrench"></i>&nbsp;Maintenance</a>
                        <a class="nav-link" href="{% url 'systeme' id='SIMULATEUR'%}"><i class="fas fa-desktop"></i>&nbsp;Simulateur</a>
                         
                
                         
                    </nav>
                </div>

                 
                <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseDep" aria-expanded="false" aria-controls="collapseDep">
                    <div class="sb-nav-link-icon"><i class="fas fa-columns"></i></div>
                    Déploiment
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse" id="collapseDep" aria-labelledby="headingOne" data-bs-parent="#sidenavAccordion">
                    <nav class="sb-sidenav-menu-nested nav">
                        <a class="nav-link" href="{% url 'mission' %}">Missions</a>
                         
                    </nav>
                </div>


                <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseMaint" aria-expanded="false" aria-controls="collapseMaint">
                    <div class="sb-nav-link-icon"><i class="fas fa-columns"></i></div>
                    Maintenance
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse" id="collapseMaint" aria-labelledby="headingOne" data-bs-parent="#sidenavAccordion">
                    <nav class="sb-sidenav-menu-nested nav">
                        <a class="nav-link" href="{% url 'intervention' %}">Interventions</a>
                        <a class="nav-link" href="{% url 'panne' %}">Historique des pannes</a>
                        
                         
                    </nav>
                </div>
             

                <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseStock" aria-expanded="false" aria-controls="collapseStock">
                    <div class="sb-nav-link-icon"><i class="fas fa-columns"></i></div>
                    Stock
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse" id="collapseStock" aria-labelledby="headingOne" data-bs-parent="#sidenavAccordion">
                    <nav class="sb-sidenav-menu-nested nav">
                     
                        <a class="nav-link" href="{% url 'bge' %}">BGE</a>
                        <a class="nav-link" href="{% url 'historique_stock' %}">Historique</a>
                        
                         
                    </nav>
                </div>


                {% if request.user.profile.role == 'admin' or request.user.profile.role == 'user' %}
           
                <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapsePersonnel" aria-expanded="false" aria-controls="collapsePersonnel">
                    <div class="sb-nav-link-icon"><i class="fas fa-columns"></i></div>
                    Personnel
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse" id="collapsePersonnel" aria-labelledby="headingOne" data-bs-parent="#sidenavAccordion">
                    <nav class="sb-sidenav-menu-nested nav">
                        <a class="nav-link" href="{% url 'personnel' %}">Pesonnel</a> 
                    </nav>
                </div>

                {% endif %}

         
              
               
            </div>
        </div>
        <div class="sb-sidenav-footer">
            <div class="small">Connecté en tant que:</div>
            {% if user.is_authenticated %}
            {{ user.username }}
        {% else %}
            Invité
        {% endif %}
        </div>
    </nav>
 