!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self)["dotlottie-player"]={})}(this,(function(exports){"use strict";function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var REACT_ELEMENT_TYPE;function _jsx(t,e,r,i){REACT_ELEMENT_TYPE||(REACT_ELEMENT_TYPE="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103);var n=t&&t.defaultProps,s=arguments.length-3;if(e||0===s||(e={children:void 0}),1===s)e.children=i;else if(s>1){for(var a=new Array(s),o=0;o<s;o++)a[o]=arguments[o+3];e.children=a}if(e&&n)for(var h in n)void 0===e[h]&&(e[h]=n[h]);else e||(e=n||{});return{$$typeof:REACT_ELEMENT_TYPE,type:t,key:void 0===r?null:""+r,ref:null,props:e,_owner:null}}function _asyncIterator(t){var e;if("undefined"!=typeof Symbol){if(Symbol.asyncIterator&&null!=(e=t[Symbol.asyncIterator]))return e.call(t);if(Symbol.iterator&&null!=(e=t[Symbol.iterator]))return e.call(t)}throw new TypeError("Object is not async iterable")}function _AwaitValue(t){this.wrapped=t}function _AsyncGenerator(t){var e,r;function i(e,r){try{var s=t[e](r),a=s.value,o=a instanceof _AwaitValue;Promise.resolve(o?a.wrapped:a).then((function(t){o?i("return"===e?"return":"next",t):n(s.done?"return":"normal",t)}),(function(t){i("throw",t)}))}catch(t){n("throw",t)}}function n(t,n){switch(t){case"return":e.resolve({value:n,done:!0});break;case"throw":e.reject(n);break;default:e.resolve({value:n,done:!1})}(e=e.next)?i(e.key,e.arg):r=null}this._invoke=function(t,n){return new Promise((function(s,a){var o={key:t,arg:n,resolve:s,reject:a,next:null};r?r=r.next=o:(e=r=o,i(t,n))}))},"function"!=typeof t.return&&(this.return=void 0)}function _wrapAsyncGenerator(t){return function(){return new _AsyncGenerator(t.apply(this,arguments))}}function _awaitAsyncGenerator(t){return new _AwaitValue(t)}function _asyncGeneratorDelegate(t,e){var r={},i=!1;function n(r,n){return i=!0,n=new Promise((function(e){e(t[r](n))})),{done:!1,value:e(n)}}return"function"==typeof Symbol&&Symbol.iterator&&(r[Symbol.iterator]=function(){return this}),r.next=function(t){return i?(i=!1,t):n("next",t)},"function"==typeof t.throw&&(r.throw=function(t){if(i)throw i=!1,t;return n("throw",t)}),"function"==typeof t.return&&(r.return=function(t){return i?(i=!1,t):n("return",t)}),r}function asyncGeneratorStep(t,e,r,i,n,s,a){try{var o=t[s](a),h=o.value}catch(t){return void r(t)}o.done?e(h):Promise.resolve(h).then(i,n)}function _asyncToGenerator(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var s=t.apply(e,r);function a(t){asyncGeneratorStep(s,i,n,a,o,"next",t)}function o(t){asyncGeneratorStep(s,i,n,a,o,"throw",t)}a(void 0)}))}}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function _createClass(t,e,r){return e&&_defineProperties(t.prototype,e),r&&_defineProperties(t,r),t}function _defineEnumerableProperties(t,e){for(var r in e){(s=e[r]).configurable=s.enumerable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,r,s)}if(Object.getOwnPropertySymbols)for(var i=Object.getOwnPropertySymbols(e),n=0;n<i.length;n++){var s,a=i[n];(s=e[a]).configurable=s.enumerable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,a,s)}return t}function _defaults(t,e){for(var r=Object.getOwnPropertyNames(e),i=0;i<r.length;i++){var n=r[i],s=Object.getOwnPropertyDescriptor(e,n);s&&s.configurable&&void 0===t[n]&&Object.defineProperty(t,n,s)}return t}function _defineProperty(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _extends(){return(_extends=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?Object(arguments[e]):{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable})))),i.forEach((function(e){_defineProperty(t,e,r[e])}))}return t}function ownKeys(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function _objectSpread2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach((function(e){_defineProperty(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&_setPrototypeOf(t,e)}function _inheritsLoose(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,_setPrototypeOf(t,e)}function _getPrototypeOf(t){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function _setPrototypeOf(t,e){return(_setPrototypeOf=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _construct(t,e,r){return(_construct=_isNativeReflectConstruct()?Reflect.construct:function(t,e,r){var i=[null];i.push.apply(i,e);var n=new(Function.bind.apply(t,i));return r&&_setPrototypeOf(n,r.prototype),n}).apply(null,arguments)}function _isNativeFunction(t){return-1!==Function.toString.call(t).indexOf("[native code]")}function _wrapNativeSuper(t){var e="function"==typeof Map?new Map:void 0;return(_wrapNativeSuper=function(t){if(null===t||!_isNativeFunction(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return _construct(t,arguments,_getPrototypeOf(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(r,t)})(t)}function _instanceof(t,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _getRequireWildcardCache(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return _getRequireWildcardCache=function(){return t},t}function _interopRequireWildcard(t){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var e=_getRequireWildcardCache();if(e&&e.has(t))return e.get(t);var r={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var s=i?Object.getOwnPropertyDescriptor(t,n):null;s&&(s.get||s.set)?Object.defineProperty(r,n,s):r[n]=t[n]}return r.default=t,e&&e.set(t,r),r}function _newArrowCheck(t,e){if(t!==e)throw new TypeError("Cannot instantiate an arrow function")}function _objectDestructuringEmpty(t){if(null==t)throw new TypeError("Cannot destructure undefined")}function _objectWithoutPropertiesLoose(t,e){if(null==t)return{};var r,i,n={},s=Object.keys(t);for(i=0;i<s.length;i++)r=s[i],e.indexOf(r)>=0||(n[r]=t[r]);return n}function _objectWithoutProperties(t,e){if(null==t)return{};var r,i,n=_objectWithoutPropertiesLoose(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(i=0;i<s.length;i++)r=s[i],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _possibleConstructorReturn(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?_assertThisInitialized(t):e}function _createSuper(t){var e=_isNativeReflectConstruct();return function(){var r,i=_getPrototypeOf(t);if(e){var n=_getPrototypeOf(this).constructor;r=Reflect.construct(i,arguments,n)}else r=i.apply(this,arguments);return _possibleConstructorReturn(this,r)}}function _superPropBase(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=_getPrototypeOf(t)););return t}function _get(t,e,r){return(_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,r){var i=_superPropBase(t,e);if(i){var n=Object.getOwnPropertyDescriptor(i,e);return n.get?n.get.call(r):n.value}})(t,e,r||t)}function set(t,e,r,i){return(set="undefined"!=typeof Reflect&&Reflect.set?Reflect.set:function(t,e,r,i){var n,s=_superPropBase(t,e);if(s){if((n=Object.getOwnPropertyDescriptor(s,e)).set)return n.set.call(i,r),!0;if(!n.writable)return!1}if(n=Object.getOwnPropertyDescriptor(i,e)){if(!n.writable)return!1;n.value=r,Object.defineProperty(i,e,n)}else _defineProperty(i,e,r);return!0})(t,e,r,i)}function _set(t,e,r,i,n){if(!set(t,e,r,i||t)&&n)throw new Error("failed to set property");return r}function _taggedTemplateLiteral(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function _taggedTemplateLiteralLoose(t,e){return e||(e=t.slice(0)),t.raw=e,t}function _readOnlyError(t){throw new TypeError('"'+t+'" is read-only')}function _writeOnlyError(t){throw new TypeError('"'+t+'" is write-only')}function _classNameTDZError(t){throw new Error('Class "'+t+'" cannot be referenced in computed property keys.')}function _temporalUndefined(){}function _tdz(t){throw new ReferenceError(t+" is not defined - temporal dead zone")}function _temporalRef(t,e){return t===_temporalUndefined?_tdz(e):t}function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _slicedToArrayLoose(t,e){return _arrayWithHoles(t)||_iterableToArrayLimitLoose(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _toArray(t){return _arrayWithHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableRest()}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayWithHoles(t){if(Array.isArray(t))return t}function _maybeArrayLike(t,e,r){if(e&&!Array.isArray(e)&&"number"==typeof e.length){var i=e.length;return _arrayLikeToArray(e,void 0!==r&&r<i?r:i)}return t(e,r)}function _iterableToArray(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function _iterableToArrayLimit(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],i=!0,n=!1,s=void 0;try{for(var a,o=t[Symbol.iterator]();!(i=(a=o.next()).done)&&(r.push(a.value),!e||r.length!==e);i=!0);}catch(t){n=!0,s=t}finally{try{i||null==o.return||o.return()}finally{if(n)throw s}}return r}}function _iterableToArrayLimitLoose(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){for(var r,i=[],n=t[Symbol.iterator]();!(r=n.next()).done&&(i.push(r.value),!e||i.length!==e););return i}}function _unsupportedIterableToArray(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(t,e):void 0}}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=new Array(e);r<e;r++)i[r]=t[r];return i}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _createForOfIteratorHelper(t,e){var r;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(r=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var i=0,n=function(){};return{s:n,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){r=t[Symbol.iterator]()},n:function(){var t=r.next();return a=t.done,t},e:function(t){o=!0,s=t},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw s}}}}function _createForOfIteratorHelperLoose(t,e){var r;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(r=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(r=t[Symbol.iterator]()).next.bind(r)}function _skipFirstGeneratorNext(t){return function(){var e=t.apply(this,arguments);return e.next(),e}}function _toPrimitive(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function _toPropertyKey(t){var e=_toPrimitive(t,"string");return"symbol"==typeof e?e:String(e)}function _initializerWarningHelper(t,e){throw new Error("Decorating class property failed. Please ensure that proposal-class-properties is enabled and runs after the decorators transform.")}function _initializerDefineProperty(t,e,r,i){r&&Object.defineProperty(t,e,{enumerable:r.enumerable,configurable:r.configurable,writable:r.writable,value:r.initializer?r.initializer.call(i):void 0})}function _applyDecoratedDescriptor(t,e,r,i,n){var s={};return Object.keys(i).forEach((function(t){s[t]=i[t]})),s.enumerable=!!s.enumerable,s.configurable=!!s.configurable,("value"in s||s.initializer)&&(s.writable=!0),s=r.slice().reverse().reduce((function(r,i){return i(t,e,r)||r}),s),n&&void 0!==s.initializer&&(s.value=s.initializer?s.initializer.call(n):void 0,s.initializer=void 0),void 0===s.initializer&&(Object.defineProperty(t,e,s),s=null),s}"function"==typeof Symbol&&Symbol.asyncIterator&&(_AsyncGenerator.prototype[Symbol.asyncIterator]=function(){return this}),_AsyncGenerator.prototype.next=function(t){return this._invoke("next",t)},_AsyncGenerator.prototype.throw=function(t){return this._invoke("throw",t)},_AsyncGenerator.prototype.return=function(t){return this._invoke("return",t)};var id=0;function _classPrivateFieldLooseKey(t){return"__private_"+id+++"_"+t}function _classPrivateFieldLooseBase(t,e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new TypeError("attempted to use private field on non-instance");return t}function _classPrivateFieldGet(t,e){return _classApplyDescriptorGet(t,_classExtractFieldDescriptor(t,e,"get"))}function _classPrivateFieldSet(t,e,r){return _classApplyDescriptorSet(t,_classExtractFieldDescriptor(t,e,"set"),r),r}function _classPrivateFieldDestructureSet(t,e){return _classApplyDescriptorDestructureSet(t,_classExtractFieldDescriptor(t,e,"set"))}function _classExtractFieldDescriptor(t,e,r){if(!e.has(t))throw new TypeError("attempted to "+r+" private field on non-instance");return e.get(t)}function _classStaticPrivateFieldSpecGet(t,e,r){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(r,"get"),_classApplyDescriptorGet(t,r)}function _classStaticPrivateFieldSpecSet(t,e,r,i){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(r,"set"),_classApplyDescriptorSet(t,r,i),i}function _classStaticPrivateMethodGet(t,e,r){return _classCheckPrivateStaticAccess(t,e),r}function _classStaticPrivateMethodSet(){throw new TypeError("attempted to set read only static private field")}function _classApplyDescriptorGet(t,e){return e.get?e.get.call(t):e.value}function _classApplyDescriptorSet(t,e,r){if(e.set)e.set.call(t,r);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=r}}function _classApplyDescriptorDestructureSet(t,e){if(e.set)return"__destrObj"in e||(e.__destrObj={set value(r){e.set.call(t,r)}}),e.__destrObj;if(!e.writable)throw new TypeError("attempted to set read only private field");return e}function _classStaticPrivateFieldDestructureSet(t,e,r){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(r,"set"),_classApplyDescriptorDestructureSet(t,r)}function _classCheckPrivateStaticAccess(t,e){if(t!==e)throw new TypeError("Private static access of wrong provenance")}function _classCheckPrivateStaticFieldDescriptor(t,e){if(void 0===t)throw new TypeError("attempted to "+e+" private static field before its declaration")}function _decorate(t,e,r,i){var n=_getDecoratorsApi();if(i)for(var s=0;s<i.length;s++)n=i[s](n);var a=e((function(t){n.initializeInstanceElements(t,o.elements)}),r),o=n.decorateClass(_coalesceClassElements(a.d.map(_createElementDescriptor)),t);return n.initializeClassElements(a.F,o.elements),n.runClassFinishers(a.F,o.finishers)}function _getDecoratorsApi(){_getDecoratorsApi=function(){return t};var t={elementsDefinitionOrder:[["method"],["field"]],initializeInstanceElements:function(t,e){["method","field"].forEach((function(r){e.forEach((function(e){e.kind===r&&"own"===e.placement&&this.defineClassElement(t,e)}),this)}),this)},initializeClassElements:function(t,e){var r=t.prototype;["method","field"].forEach((function(i){e.forEach((function(e){var n=e.placement;if(e.kind===i&&("static"===n||"prototype"===n)){var s="static"===n?t:r;this.defineClassElement(s,e)}}),this)}),this)},defineClassElement:function(t,e){var r=e.descriptor;if("field"===e.kind){var i=e.initializer;r={enumerable:r.enumerable,writable:r.writable,configurable:r.configurable,value:void 0===i?void 0:i.call(t)}}Object.defineProperty(t,e.key,r)},decorateClass:function(t,e){var r=[],i=[],n={static:[],prototype:[],own:[]};if(t.forEach((function(t){this.addElementPlacement(t,n)}),this),t.forEach((function(t){if(!_hasDecorators(t))return r.push(t);var e=this.decorateElement(t,n);r.push(e.element),r.push.apply(r,e.extras),i.push.apply(i,e.finishers)}),this),!e)return{elements:r,finishers:i};var s=this.decorateConstructor(r,e);return i.push.apply(i,s.finishers),s.finishers=i,s},addElementPlacement:function(t,e,r){var i=e[t.placement];if(!r&&-1!==i.indexOf(t.key))throw new TypeError("Duplicated element ("+t.key+")");i.push(t.key)},decorateElement:function(t,e){for(var r=[],i=[],n=t.decorators,s=n.length-1;s>=0;s--){var a=e[t.placement];a.splice(a.indexOf(t.key),1);var o=this.fromElementDescriptor(t),h=this.toElementFinisherExtras((0,n[s])(o)||o);t=h.element,this.addElementPlacement(t,e),h.finisher&&i.push(h.finisher);var l=h.extras;if(l){for(var p=0;p<l.length;p++)this.addElementPlacement(l[p],e);r.push.apply(r,l)}}return{element:t,finishers:i,extras:r}},decorateConstructor:function(t,e){for(var r=[],i=e.length-1;i>=0;i--){var n=this.fromClassDescriptor(t),s=this.toClassDescriptor((0,e[i])(n)||n);if(void 0!==s.finisher&&r.push(s.finisher),void 0!==s.elements){t=s.elements;for(var a=0;a<t.length-1;a++)for(var o=a+1;o<t.length;o++)if(t[a].key===t[o].key&&t[a].placement===t[o].placement)throw new TypeError("Duplicated element ("+t[a].key+")")}}return{elements:t,finishers:r}},fromElementDescriptor:function(t){var e={kind:t.kind,key:t.key,placement:t.placement,descriptor:t.descriptor};return Object.defineProperty(e,Symbol.toStringTag,{value:"Descriptor",configurable:!0}),"field"===t.kind&&(e.initializer=t.initializer),e},toElementDescriptors:function(t){if(void 0!==t)return _toArray(t).map((function(t){var e=this.toElementDescriptor(t);return this.disallowProperty(t,"finisher","An element descriptor"),this.disallowProperty(t,"extras","An element descriptor"),e}),this)},toElementDescriptor:function(t){var e=String(t.kind);if("method"!==e&&"field"!==e)throw new TypeError('An element descriptor\'s .kind property must be either "method" or "field", but a decorator created an element descriptor with .kind "'+e+'"');var r=_toPropertyKey(t.key),i=String(t.placement);if("static"!==i&&"prototype"!==i&&"own"!==i)throw new TypeError('An element descriptor\'s .placement property must be one of "static", "prototype" or "own", but a decorator created an element descriptor with .placement "'+i+'"');var n=t.descriptor;this.disallowProperty(t,"elements","An element descriptor");var s={kind:e,key:r,placement:i,descriptor:Object.assign({},n)};return"field"!==e?this.disallowProperty(t,"initializer","A method descriptor"):(this.disallowProperty(n,"get","The property descriptor of a field descriptor"),this.disallowProperty(n,"set","The property descriptor of a field descriptor"),this.disallowProperty(n,"value","The property descriptor of a field descriptor"),s.initializer=t.initializer),s},toElementFinisherExtras:function(t){return{element:this.toElementDescriptor(t),finisher:_optionalCallableProperty(t,"finisher"),extras:this.toElementDescriptors(t.extras)}},fromClassDescriptor:function(t){var e={kind:"class",elements:t.map(this.fromElementDescriptor,this)};return Object.defineProperty(e,Symbol.toStringTag,{value:"Descriptor",configurable:!0}),e},toClassDescriptor:function(t){var e=String(t.kind);if("class"!==e)throw new TypeError('A class descriptor\'s .kind property must be "class", but a decorator created a class descriptor with .kind "'+e+'"');this.disallowProperty(t,"key","A class descriptor"),this.disallowProperty(t,"placement","A class descriptor"),this.disallowProperty(t,"descriptor","A class descriptor"),this.disallowProperty(t,"initializer","A class descriptor"),this.disallowProperty(t,"extras","A class descriptor");var r=_optionalCallableProperty(t,"finisher");return{elements:this.toElementDescriptors(t.elements),finisher:r}},runClassFinishers:function(t,e){for(var r=0;r<e.length;r++){var i=(0,e[r])(t);if(void 0!==i){if("function"!=typeof i)throw new TypeError("Finishers must return a constructor.");t=i}}return t},disallowProperty:function(t,e,r){if(void 0!==t[e])throw new TypeError(r+" can't have a ."+e+" property.")}};return t}function _createElementDescriptor(t){var e,r=_toPropertyKey(t.key);"method"===t.kind?e={value:t.value,writable:!0,configurable:!0,enumerable:!1}:"get"===t.kind?e={get:t.value,configurable:!0,enumerable:!1}:"set"===t.kind?e={set:t.value,configurable:!0,enumerable:!1}:"field"===t.kind&&(e={configurable:!0,writable:!0,enumerable:!0});var i={kind:"field"===t.kind?"field":"method",key:r,placement:t.static?"static":"field"===t.kind?"own":"prototype",descriptor:e};return t.decorators&&(i.decorators=t.decorators),"field"===t.kind&&(i.initializer=t.value),i}function _coalesceGetterSetter(t,e){void 0!==t.descriptor.get?e.descriptor.get=t.descriptor.get:e.descriptor.set=t.descriptor.set}function _coalesceClassElements(t){for(var e=[],r=function(t){return"method"===t.kind&&t.key===s.key&&t.placement===s.placement},i=0;i<t.length;i++){var n,s=t[i];if("method"===s.kind&&(n=e.find(r)))if(_isDataDescriptor(s.descriptor)||_isDataDescriptor(n.descriptor)){if(_hasDecorators(s)||_hasDecorators(n))throw new ReferenceError("Duplicated methods ("+s.key+") can't be decorated.");n.descriptor=s.descriptor}else{if(_hasDecorators(s)){if(_hasDecorators(n))throw new ReferenceError("Decorators can't be placed on different accessors with for the same property ("+s.key+").");n.decorators=s.decorators}_coalesceGetterSetter(s,n)}else e.push(s)}return e}function _hasDecorators(t){return t.decorators&&t.decorators.length}function _isDataDescriptor(t){return void 0!==t&&!(void 0===t.value&&void 0===t.writable)}function _optionalCallableProperty(t,e){var r=t[e];if(void 0!==r&&"function"!=typeof r)throw new TypeError("Expected '"+e+"' to be a function");return r}function _classPrivateMethodGet(t,e,r){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return r}function _classPrivateMethodSet(){throw new TypeError("attempted to reassign private method")}function _wrapRegExp(t,e){_wrapRegExp=function(t,e){return new s(t,void 0,e)};var r=_wrapNativeSuper(RegExp),i=RegExp.prototype,n=new WeakMap;function s(t,e,i){var s=r.call(this,t,e);return n.set(s,i||n.get(t)),s}function a(t,e){var r=n.get(e);return Object.keys(r).reduce((function(e,i){return e[i]=t[r[i]],e}),Object.create(null))}return _inherits(s,r),s.prototype.exec=function(t){var e=i.exec.call(this,t);return e&&(e.groups=a(e,this)),e},s.prototype[Symbol.replace]=function(t,e){if("string"==typeof e){var r=n.get(this);return i[Symbol.replace].call(this,t,e.replace(/\$<([^>]+)>/g,(function(t,e){return"$"+r[e]})))}if("function"==typeof e){var s=this;return i[Symbol.replace].call(this,t,(function(){var t=[];return t.push.apply(t,arguments),"object"!=typeof t[t.length-1]&&t.push(a(t,s)),e.apply(this,t)}))}return i[Symbol.replace].call(this,t,e)},_wrapRegExp.apply(this,arguments)}
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */var extendStatics=function(t,e){return(extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function __extends(t,e){function r(){this.constructor=t}extendStatics(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var __assign=function(){return(__assign=Object.assign||function(t){for(var e,r=1,i=arguments.length;r<i;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)};function __rest(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(i=Object.getOwnPropertySymbols(t);n<i.length;n++)e.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(t,i[n])&&(r[i[n]]=t[i[n]])}return r}function __decorate(t,e,r,i){var n,s=arguments.length,a=s<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,i);else for(var o=t.length-1;o>=0;o--)(n=t[o])&&(a=(s<3?n(a):s>3?n(e,r,a):n(e,r))||a);return s>3&&a&&Object.defineProperty(e,r,a),a}function __param(t,e){return function(r,i){e(r,i,t)}}function __metadata(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function __awaiter(t,e,r,i){return new(r||(r=Promise))((function(n,s){function a(t){try{h(i.next(t))}catch(t){s(t)}}function o(t){try{h(i.throw(t))}catch(t){s(t)}}function h(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(a,o)}h((i=i.apply(t,e||[])).next())}))}function __generator(t,e){var r,i,n,s,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(s){return function(o){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(n=a.trys,(n=n.length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){a.label=s[1];break}if(6===s[0]&&a.label<n[1]){a.label=n[1],n=s;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(s);break}n[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,o])}}}function __createBinding(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}function __exportStar(t,e){for(var r in t)"default"===r||e.hasOwnProperty(r)||(e[r]=t[r])}function __values(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],i=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function __read(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,s=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a}function __spread(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(__read(arguments[e]));return t}function __spreadArrays(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;var i=Array(t),n=0;for(e=0;e<r;e++)for(var s=arguments[e],a=0,o=s.length;a<o;a++,n++)i[n]=s[a];return i}function __await(t){return this instanceof __await?(this.v=t,this):new __await(t)}function __asyncGenerator(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,n=r.apply(t,e||[]),s=[];return i={},a("next"),a("throw"),a("return"),i[Symbol.asyncIterator]=function(){return this},i;function a(t){n[t]&&(i[t]=function(e){return new Promise((function(r,i){s.push([t,e,r,i])>1||o(t,e)}))})}function o(t,e){try{!function(t){t.value instanceof __await?Promise.resolve(t.value.v).then(h,l):p(s[0][2],t)}(n[t](e))}catch(t){p(s[0][3],t)}}function h(t){o("next",t)}function l(t){o("throw",t)}function p(t,e){t(e),s.shift(),s.length&&o(s[0][0],s[0][1])}}function __asyncDelegator(t){var e,r;return e={},i("next"),i("throw",(function(t){throw t})),i("return"),e[Symbol.iterator]=function(){return this},e;function i(i,n){e[i]=t[i]?function(e){return(r=!r)?{value:__await(t[i](e)),done:"return"===i}:n?n(e):e}:n}}function __asyncValues(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t="function"==typeof __values?__values(t):t[Symbol.iterator](),e={},i("next"),i("throw"),i("return"),e[Symbol.asyncIterator]=function(){return this},e);function i(r){e[r]=t[r]&&function(e){return new Promise((function(i,n){(function(t,e,r,i){Promise.resolve(i).then((function(e){t({value:e,done:r})}),e)})(i,n,(e=t[r](e)).done,e.value)}))}}}function __makeTemplateObject(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function __importStar(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function __importDefault(t){return t&&t.__esModule?t:{default:t}}function __classPrivateFieldGet(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function __classPrivateFieldSet(t,e,r){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,r),r}
/**
   * @license
   * Copyright 2019 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */const t$3=window,e$9=t$3.ShadowRoot&&(void 0===t$3.ShadyCSS||t$3.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,s$3=Symbol(),n$4=new WeakMap;class o$6{constructor(t,e,r){if(this._$cssResult$=!0,r!==s$3)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t,this.t=e}get styleSheet(){let t=this.o;const e=this.t;if(e$9&&void 0===t){const r=void 0!==e&&1===e.length;r&&(t=n$4.get(e)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),r&&n$4.set(e,t))}return t}toString(){return this.cssText}}const r$3=t=>new o$6("string"==typeof t?t:t+"",void 0,s$3),i$3=(t,...e)=>{const r=1===t.length?t[0]:e.reduce((e,r,i)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if("number"==typeof t)return t;throw Error("Value passed to 'css' function must be a 'css' function result: "+t+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(r)+t[i+1],t[0]);return new o$6(r,t,s$3)},S$1=(t,e)=>{e$9?t.adoptedStyleSheets=e.map(t=>t instanceof CSSStyleSheet?t:t.styleSheet):e.forEach(e=>{const r=document.createElement("style"),i=t$3.litNonce;void 0!==i&&r.setAttribute("nonce",i),r.textContent=e.cssText,t.appendChild(r)})},c$1=e$9?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e="";for(const r of t.cssRules)e+=r.cssText;return r$3(e)})(t):t
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */;var s$2;const e$8=window,r$2=e$8.trustedTypes,h$2=r$2?r$2.emptyScript:"",o$5=e$8.reactiveElementPolyfillSupport,n$3={toAttribute(t,e){switch(e){case Boolean:t=t?h$2:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let r=t;switch(e){case Boolean:r=null!==t;break;case Number:r=null===t?null:Number(t);break;case Object:case Array:try{r=JSON.parse(t)}catch(t){r=null}}return r}},a$1=(t,e)=>e!==t&&(e==e||t==t),l$3={attribute:!0,type:String,converter:n$3,reflect:!1,hasChanged:a$1};class d$1 extends HTMLElement{constructor(){super(),this._$Ei=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this._$El=null,this.u()}static addInitializer(t){var e;this.finalize(),(null!==(e=this.h)&&void 0!==e?e:this.h=[]).push(t)}static get observedAttributes(){this.finalize();const t=[];return this.elementProperties.forEach((e,r)=>{const i=this._$Ep(r,e);void 0!==i&&(this._$Ev.set(i,r),t.push(i))}),t}static createProperty(t,e=l$3){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){const r="symbol"==typeof t?Symbol():"__"+t,i=this.getPropertyDescriptor(t,r,e);void 0!==i&&Object.defineProperty(this.prototype,t,i)}}static getPropertyDescriptor(t,e,r){return{get(){return this[e]},set(i){const n=this[t];this[e]=i,this.requestUpdate(t,n,r)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||l$3}static finalize(){if(this.hasOwnProperty("finalized"))return!1;this.finalized=!0;const t=Object.getPrototypeOf(this);if(t.finalize(),void 0!==t.h&&(this.h=[...t.h]),this.elementProperties=new Map(t.elementProperties),this._$Ev=new Map,this.hasOwnProperty("properties")){const t=this.properties,e=[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)];for(const r of e)this.createProperty(r,t[r])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const r=new Set(t.flat(1/0).reverse());for(const t of r)e.unshift(c$1(t))}else void 0!==t&&e.push(c$1(t));return e}static _$Ep(t,e){const r=e.attribute;return!1===r?void 0:"string"==typeof r?r:"string"==typeof t?t.toLowerCase():void 0}u(){var t;this._$E_=new Promise(t=>this.enableUpdating=t),this._$AL=new Map,this._$Eg(),this.requestUpdate(),null===(t=this.constructor.h)||void 0===t||t.forEach(t=>t(this))}addController(t){var e,r;(null!==(e=this._$ES)&&void 0!==e?e:this._$ES=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(r=t.hostConnected)||void 0===r||r.call(t))}removeController(t){var e;null===(e=this._$ES)||void 0===e||e.splice(this._$ES.indexOf(t)>>>0,1)}_$Eg(){this.constructor.elementProperties.forEach((t,e)=>{this.hasOwnProperty(e)&&(this._$Ei.set(e,this[e]),delete this[e])})}createRenderRoot(){var t;const e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return S$1(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this._$ES)||void 0===t||t.forEach(t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)})}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this._$ES)||void 0===t||t.forEach(t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)})}attributeChangedCallback(t,e,r){this._$AK(t,r)}_$EO(t,e,r=l$3){var i;const n=this.constructor._$Ep(t,r);if(void 0!==n&&!0===r.reflect){const s=(void 0!==(null===(i=r.converter)||void 0===i?void 0:i.toAttribute)?r.converter:n$3).toAttribute(e,r.type);this._$El=t,null==s?this.removeAttribute(n):this.setAttribute(n,s),this._$El=null}}_$AK(t,e){var r;const i=this.constructor,n=i._$Ev.get(t);if(void 0!==n&&this._$El!==n){const t=i.getPropertyOptions(n),s="function"==typeof t.converter?{fromAttribute:t.converter}:void 0!==(null===(r=t.converter)||void 0===r?void 0:r.fromAttribute)?t.converter:n$3;this._$El=n,this[n]=s.fromAttribute(e,t.type),this._$El=null}}requestUpdate(t,e,r){let i=!0;void 0!==t&&(((r=r||this.constructor.getPropertyOptions(t)).hasChanged||a$1)(this[t],e)?(this._$AL.has(t)||this._$AL.set(t,e),!0===r.reflect&&this._$El!==t&&(void 0===this._$EC&&(this._$EC=new Map),this._$EC.set(t,r))):i=!1),!this.isUpdatePending&&i&&(this._$E_=this._$Ej())}async _$Ej(){this.isUpdatePending=!0;try{await this._$E_}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var t;if(!this.isUpdatePending)return;this.hasUpdated,this._$Ei&&(this._$Ei.forEach((t,e)=>this[e]=t),this._$Ei=void 0);let e=!1;const r=this._$AL;try{e=this.shouldUpdate(r),e?(this.willUpdate(r),null===(t=this._$ES)||void 0===t||t.forEach(t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)}),this.update(r)):this._$Ek()}catch(t){throw e=!1,this._$Ek(),t}e&&this._$AE(r)}willUpdate(t){}_$AE(t){var e;null===(e=this._$ES)||void 0===e||e.forEach(t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)}),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$Ek(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$E_}shouldUpdate(t){return!0}update(t){void 0!==this._$EC&&(this._$EC.forEach((t,e)=>this._$EO(e,this[e],t)),this._$EC=void 0),this._$Ek()}updated(t){}firstUpdated(t){}}
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */
var t$2;d$1.finalized=!0,d$1.elementProperties=new Map,d$1.elementStyles=[],d$1.shadowRootOptions={mode:"open"},null==o$5||o$5({ReactiveElement:d$1}),(null!==(s$2=e$8.reactiveElementVersions)&&void 0!==s$2?s$2:e$8.reactiveElementVersions=[]).push("1.4.2");const i$2=window,s$1=i$2.trustedTypes,e$7=s$1?s$1.createPolicy("lit-html",{createHTML:t=>t}):void 0,o$4=`lit$${(Math.random()+"").slice(9)}$`,n$2="?"+o$4,l$2=`<${n$2}>`,h$1=document,r$1=(t="")=>h$1.createComment(t),d=t=>null===t||"object"!=typeof t&&"function"!=typeof t,u=Array.isArray,c=t=>u(t)||"function"==typeof(null==t?void 0:t[Symbol.iterator]),v=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,a=/-->/g,f=/>/g,_=RegExp(">|[ \t\n\f\r](?:([^\\s\"'>=/]+)([ \t\n\f\r]*=[ \t\n\f\r]*(?:[^ \t\n\f\r\"'`<>=]|(\"|')|))|$)","g"),m=/'/g,p=/"/g,$=/^(?:script|style|textarea|title)$/i,g=t=>(e,...r)=>({_$litType$:t,strings:e,values:r}),y=g(1),w=g(2),x=Symbol.for("lit-noChange"),b=Symbol.for("lit-nothing"),T=new WeakMap,A=h$1.createTreeWalker(h$1,129,null,!1),E=(t,e)=>{const r=t.length-1,i=[];let n,s=2===e?"<svg>":"",o=v;for(let e=0;e<r;e++){const r=t[e];let h,l,c=-1,u=0;for(;u<r.length&&(o.lastIndex=u,l=o.exec(r),null!==l);)u=o.lastIndex,o===v?"!--"===l[1]?o=a:void 0!==l[1]?o=f:void 0!==l[2]?($.test(l[2])&&(n=RegExp("</"+l[2],"g")),o=_):void 0!==l[3]&&(o=_):o===_?">"===l[0]?(o=null!=n?n:v,c=-1):void 0===l[1]?c=-2:(c=o.lastIndex-l[2].length,h=l[1],o=void 0===l[3]?_:'"'===l[3]?p:m):o===p||o===m?o=_:o===a||o===f?o=v:(o=_,n=void 0);const d=o===_&&t[e+1].startsWith("/>")?" ":"";s+=o===v?r+l$2:c>=0?(i.push(h),r.slice(0,c)+"$lit$"+r.slice(c)+o$4+d):r+o$4+(-2===c?(i.push(void 0),e):d)}const h=s+(t[r]||"<?>")+(2===e?"</svg>":"");if(!Array.isArray(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return[void 0!==e$7?e$7.createHTML(h):h,i]};class C{constructor({strings:t,_$litType$:e},r){let i;this.parts=[];let n=0,s=0;const a=t.length-1,o=this.parts,[h,l]=E(t,e);if(this.el=C.createElement(h,r),A.currentNode=this.el.content,2===e){const t=this.el.content,e=t.firstChild;e.remove(),t.append(...e.childNodes)}for(;null!==(i=A.nextNode())&&o.length<a;){if(1===i.nodeType){if(i.hasAttributes()){const t=[];for(const e of i.getAttributeNames())if(e.endsWith("$lit$")||e.startsWith(o$4)){const r=l[s++];if(t.push(e),void 0!==r){const t=i.getAttribute(r.toLowerCase()+"$lit$").split(o$4),e=/([.?@])?(.*)/.exec(r);o.push({type:1,index:n,name:e[2],strings:t,ctor:"."===e[1]?M:"?"===e[1]?k:"@"===e[1]?H:S})}else o.push({type:6,index:n})}for(const e of t)i.removeAttribute(e)}if($.test(i.tagName)){const t=i.textContent.split(o$4),e=t.length-1;if(e>0){i.textContent=s$1?s$1.emptyScript:"";for(let r=0;r<e;r++)i.append(t[r],r$1()),A.nextNode(),o.push({type:2,index:++n});i.append(t[e],r$1())}}}else if(8===i.nodeType)if(i.data===n$2)o.push({type:2,index:n});else{let t=-1;for(;-1!==(t=i.data.indexOf(o$4,t+1));)o.push({type:7,index:n}),t+=o$4.length-1}n++}}static createElement(t,e){const r=h$1.createElement("template");return r.innerHTML=t,r}}function P(t,e,r=t,i){var n,s,a,o;if(e===x)return e;let h=void 0!==i?null===(n=r._$Co)||void 0===n?void 0:n[i]:r._$Cl;const l=d(e)?void 0:e._$litDirective$;return(null==h?void 0:h.constructor)!==l&&(null===(s=null==h?void 0:h._$AO)||void 0===s||s.call(h,!1),void 0===l?h=void 0:(h=new l(t),h._$AT(t,r,i)),void 0!==i?(null!==(a=(o=r)._$Co)&&void 0!==a?a:o._$Co=[])[i]=h:r._$Cl=h),void 0!==h&&(e=P(t,h._$AS(t,e.values),h,i)),e}class V{constructor(t,e){this.u=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}v(t){var e;const{el:{content:r},parts:i}=this._$AD,n=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:h$1).importNode(r,!0);A.currentNode=n;let s=A.nextNode(),a=0,o=0,h=i[0];for(;void 0!==h;){if(a===h.index){let e;2===h.type?e=new N(s,s.nextSibling,this,t):1===h.type?e=new h.ctor(s,h.name,h.strings,this,t):6===h.type&&(e=new I(s,this,t)),this.u.push(e),h=i[++o]}a!==(null==h?void 0:h.index)&&(s=A.nextNode(),a++)}return n}p(t){let e=0;for(const r of this.u)void 0!==r&&(void 0!==r.strings?(r._$AI(t,r,e),e+=r.strings.length-2):r._$AI(t[e])),e++}}class N{constructor(t,e,r,i){var n;this.type=2,this._$AH=b,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=r,this.options=i,this._$Cm=null===(n=null==i?void 0:i.isConnected)||void 0===n||n}get _$AU(){var t,e;return null!==(e=null===(t=this._$AM)||void 0===t?void 0:t._$AU)&&void 0!==e?e:this._$Cm}get parentNode(){let t=this._$AA.parentNode;const e=this._$AM;return void 0!==e&&11===t.nodeType&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,e=this){t=P(this,t,e),d(t)?t===b||null==t||""===t?(this._$AH!==b&&this._$AR(),this._$AH=b):t!==this._$AH&&t!==x&&this.g(t):void 0!==t._$litType$?this.$(t):void 0!==t.nodeType?this.T(t):c(t)?this.k(t):this.g(t)}O(t,e=this._$AB){return this._$AA.parentNode.insertBefore(t,e)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.O(t))}g(t){this._$AH!==b&&d(this._$AH)?this._$AA.nextSibling.data=t:this.T(h$1.createTextNode(t)),this._$AH=t}$(t){var e;const{values:r,_$litType$:i}=t,n="number"==typeof i?this._$AC(t):(void 0===i.el&&(i.el=C.createElement(i.h,this.options)),i);if((null===(e=this._$AH)||void 0===e?void 0:e._$AD)===n)this._$AH.p(r);else{const t=new V(n,this),e=t.v(this.options);t.p(r),this.T(e),this._$AH=t}}_$AC(t){let e=T.get(t.strings);return void 0===e&&T.set(t.strings,e=new C(t)),e}k(t){u(this._$AH)||(this._$AH=[],this._$AR());const e=this._$AH;let r,i=0;for(const n of t)i===e.length?e.push(r=new N(this.O(r$1()),this.O(r$1()),this,this.options)):r=e[i],r._$AI(n),i++;i<e.length&&(this._$AR(r&&r._$AB.nextSibling,i),e.length=i)}_$AR(t=this._$AA.nextSibling,e){var r;for(null===(r=this._$AP)||void 0===r||r.call(this,!1,!0,e);t&&t!==this._$AB;){const e=t.nextSibling;t.remove(),t=e}}setConnected(t){var e;void 0===this._$AM&&(this._$Cm=t,null===(e=this._$AP)||void 0===e||e.call(this,t))}}class S{constructor(t,e,r,i,n){this.type=1,this._$AH=b,this._$AN=void 0,this.element=t,this.name=e,this._$AM=i,this.options=n,r.length>2||""!==r[0]||""!==r[1]?(this._$AH=Array(r.length-1).fill(new String),this.strings=r):this._$AH=b}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(t,e=this,r,i){const n=this.strings;let s=!1;if(void 0===n)t=P(this,t,e,0),s=!d(t)||t!==this._$AH&&t!==x,s&&(this._$AH=t);else{const i=t;let a,o;for(t=n[0],a=0;a<n.length-1;a++)o=P(this,i[r+a],e,a),o===x&&(o=this._$AH[a]),s||(s=!d(o)||o!==this._$AH[a]),o===b?t=b:t!==b&&(t+=(null!=o?o:"")+n[a+1]),this._$AH[a]=o}s&&!i&&this.j(t)}j(t){t===b?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:"")}}class M extends S{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===b?void 0:t}}const R=s$1?s$1.emptyScript:"";class k extends S{constructor(){super(...arguments),this.type=4}j(t){t&&t!==b?this.element.setAttribute(this.name,R):this.element.removeAttribute(this.name)}}class H extends S{constructor(t,e,r,i,n){super(t,e,r,i,n),this.type=5}_$AI(t,e=this){var r;if((t=null!==(r=P(this,t,e,0))&&void 0!==r?r:b)===x)return;const i=this._$AH,n=t===b&&i!==b||t.capture!==i.capture||t.once!==i.once||t.passive!==i.passive,s=t!==b&&(i===b||n);n&&this.element.removeEventListener(this.name,this,i),s&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){var e,r;"function"==typeof this._$AH?this._$AH.call(null!==(r=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==r?r:this.element,t):this._$AH.handleEvent(t)}}class I{constructor(t,e,r){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=r}get _$AU(){return this._$AM._$AU}_$AI(t){P(this,t)}}const L={P:"$lit$",A:o$4,M:n$2,C:1,L:E,R:V,D:c,V:P,I:N,H:S,N:k,U:H,B:M,F:I},z=i$2.litHtmlPolyfillSupport;null==z||z(C,N),(null!==(t$2=i$2.litHtmlVersions)&&void 0!==t$2?t$2:i$2.litHtmlVersions=[]).push("2.4.0");const Z=(t,e,r)=>{var i,n;const s=null!==(i=null==r?void 0:r.renderBefore)&&void 0!==i?i:e;let a=s._$litPart$;if(void 0===a){const t=null!==(n=null==r?void 0:r.renderBefore)&&void 0!==n?n:null;s._$litPart$=a=new N(e.insertBefore(r$1(),t),t,void 0,null!=r?r:{})}return a._$AI(t),a
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */};var l$1,o$3;const r=d$1;class s extends d$1{constructor(){super(...arguments),this.renderOptions={host:this},this._$Dt=void 0}createRenderRoot(){var t,e;const r=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=r.firstChild),r}update(t){const e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Dt=Z(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this._$Dt)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this._$Dt)||void 0===t||t.setConnected(!1)}render(){return x}}s.finalized=!0,s._$litElement$=!0,null===(l$1=globalThis.litElementHydrateSupport)||void 0===l$1||l$1.call(globalThis,{LitElement:s});const n$1=globalThis.litElementPolyfillSupport;null==n$1||n$1({LitElement:s});const h={_$AK:(t,e,r)=>{t._$AK(e,r)},_$AL:t=>t._$AL};(null!==(o$3=globalThis.litElementVersions)&&void 0!==o$3?o$3:globalThis.litElementVersions=[]).push("3.2.0");
/**
   * @license
   * Copyright 2022 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */
const o$2=!1,e$6=t=>e=>"function"==typeof e?((t,e)=>(customElements.define(t,e),e))(t,e):((t,e)=>{const{kind:r,elements:i}=e;return{kind:r,elements:i,finisher(e){customElements.define(t,e)}}})(t,e)
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */,i$1=(t,e)=>"method"===e.kind&&e.descriptor&&!("value"in e.descriptor)?{...e,finisher(r){r.createProperty(e.key,t)}}:{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){"function"==typeof e.initializer&&(this[e.key]=e.initializer.call(this))},finisher(r){r.createProperty(e.key,t)}};
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */function e$5(t){return(e,r)=>void 0!==r?((t,e,r)=>{e.constructor.createProperty(r,t)})(t,e,r):i$1(t,e)
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */}function t$1(t){return e$5({...t,state:!0})}
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */const e$4=(t,e,r)=>{Object.defineProperty(e,r,t)},t=(t,e)=>({kind:"method",placement:"prototype",key:e.key,descriptor:t}),o$1=({finisher:t,descriptor:e})=>(r,i)=>{var n;if(void 0===i){const i=null!==(n=r.originalKey)&&void 0!==n?n:r.key,s=null!=e?{kind:"method",placement:"prototype",key:i,descriptor:e(r.key)}:{...r,key:i};return null!=t&&(s.finisher=function(e){t(e,i)}),s}{const n=r.constructor;void 0!==e&&Object.defineProperty(r,i,e(i)),null==t||t(n,i)}}
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */;function e$3(t){return o$1({finisher:(e,r)=>{Object.assign(e.prototype[r],t)}})}
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */function i(t,e){return o$1({descriptor:r=>{const i={get(){var e,r;return null!==(r=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t))&&void 0!==r?r:null},enumerable:!0,configurable:!0};if(e){const e="symbol"==typeof r?Symbol():"__"+r;i.get=function(){var r,i;return void 0===this[e]&&(this[e]=null!==(i=null===(r=this.renderRoot)||void 0===r?void 0:r.querySelector(t))&&void 0!==i?i:null),this[e]}}return i}})}
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */function e$2(t){return o$1({descriptor:e=>({get(){var e,r;return null!==(r=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelectorAll(t))&&void 0!==r?r:[]},enumerable:!0,configurable:!0})})}
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */function e$1(t){return o$1({descriptor:e=>({async get(){var e;return await this.updateComplete,null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t)},enumerable:!0,configurable:!0})})}
/**
   * @license
   * Copyright 2021 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */var n;const e=null!=(null===(n=window.HTMLSlotElement)||void 0===n?void 0:n.prototype.assignedElements)?(t,e)=>t.assignedElements(e):(t,e)=>t.assignedNodes(e).filter(t=>t.nodeType===Node.ELEMENT_NODE);function l(t){const{slot:r,selector:i}=null!=t?t:{};return o$1({descriptor:n=>({get(){var n;const s="slot"+(r?`[name=${r}]`:":not([name])"),a=null===(n=this.renderRoot)||void 0===n?void 0:n.querySelector(s),o=null!=a?e(a,t):[];return i?o.filter(t=>t.matches(i)):o},enumerable:!0,configurable:!0})})}
/**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   */function o(t,e,r){let i,n=t;return"object"==typeof t?(n=t.slot,i=t):i={flatten:e},r?l({slot:n,flatten:e,selector:r}):o$1({descriptor:t=>({get(){var t,e;const r="slot"+(n?`[name=${n}]`:":not([name])"),s=null===(t=this.renderRoot)||void 0===t?void 0:t.querySelector(r);return null!==(e=null==s?void 0:s.assignedNodes(i))&&void 0!==e?e:[]},enumerable:!0,configurable:!0})})}var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function createCommonjsModule(t,e,r){return t(r={path:e,exports:{},require:function(t,e){return commonjsRequire(t,null==e?r.path:e)}},r.exports),r.exports}function getCjsExportFromNamespace(t){return t&&t.default||t}function commonjsRequire(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}var lottie_svg=createCommonjsModule((function(module,exports){"undefined"!=typeof navigator&&function(t,e){module.exports=e()}(0,(function(){var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(t){_useWebWorker=!!t},getWebWorker=function(){return _useWebWorker},setLocationHref=function(t){locationHref=t},getLocationHref=function(){return locationHref};function createTag(t){return document.createElement(t)}function extendPrototype(t,e){var r,i,n=t.length;for(r=0;r<n;r+=1)for(var s in i=t[r].prototype)Object.prototype.hasOwnProperty.call(i,s)&&(e.prototype[s]=i[s])}function getDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)}function createProxyFunction(t){function e(){}return e.prototype=t,e}var audioControllerFactory=function(){function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].pause()},resume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].resume()},setRate:function(t){var e,r=this.audios.length;for(e=0;e<r;e+=1)this.audios[e].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}}(),createTypedArray=function(){function t(t,e){var r,i=0,n=[];switch(t){case"int16":case"uint8c":r=1;break;default:r=1.1}for(i=0;i<e;i+=1)n.push(r);return n}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(e,r){return"float32"===e?new Float32Array(r):"int16"===e?new Int16Array(r):"uint8c"===e?new Uint8ClampedArray(r):t(e,r)}:t}();function createSizedArray(t){return Array.apply(null,{length:t})}function _typeof$6(t){return(_typeof$6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var subframeEnabled=!0,expressionsPlugin=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),_shouldRoundValues=!1,bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};function ProjectInterface$1(){return{}}!function(){var t,e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],r=e.length;for(t=0;t<r;t+=1)BMMath[e[t]]=Math[e[t]]}(),BMMath.random=Math.random,BMMath.abs=function(t){if("object"===_typeof$6(t)&&t.length){var e,r=createSizedArray(t.length),i=t.length;for(e=0;e<i;e+=1)r[e]=Math.abs(t[e]);return r}return Math.abs(t)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function roundValues(t){_shouldRoundValues=!!t}function bmRnd(t){return _shouldRoundValues?Math.round(t):t}function styleDiv(t){t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.display="block",t.style.transformOrigin="0 0",t.style.webkitTransformOrigin="0 0",t.style.backfaceVisibility="visible",t.style.webkitBackfaceVisibility="visible",t.style.transformStyle="preserve-3d",t.style.webkitTransformStyle="preserve-3d",t.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(t,e,r,i){this.type=t,this.currentTime=e,this.totalTime=r,this.direction=i<0?-1:1}function BMCompleteEvent(t,e){this.type=t,this.direction=e<0?-1:1}function BMCompleteLoopEvent(t,e,r,i){this.type=t,this.currentLoop=r,this.totalLoops=e,this.direction=i<0?-1:1}function BMSegmentStartEvent(t,e,r){this.type=t,this.firstFrame=e,this.totalFrames=r}function BMDestroyEvent(t,e){this.type=t,this.target=e}function BMRenderFrameErrorEvent(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function BMConfigErrorEvent(t){this.type="configError",this.nativeError=t}function BMAnimationConfigErrorEvent(t,e){this.type=t,this.nativeError=e}var createElementID=(_count=0,function(){return idPrefix$1+"__lottie_element_"+(_count+=1)}),_count;function HSVtoRGB(t,e,r){var i,n,s,a,o,h,l,p;switch(h=r*(1-e),l=r*(1-(o=6*t-(a=Math.floor(6*t)))*e),p=r*(1-(1-o)*e),a%6){case 0:i=r,n=p,s=h;break;case 1:i=l,n=r,s=h;break;case 2:i=h,n=r,s=p;break;case 3:i=h,n=l,s=r;break;case 4:i=p,n=h,s=r;break;case 5:i=r,n=h,s=l}return[i,n,s]}function RGBtoHSV(t,e,r){var i,n=Math.max(t,e,r),s=Math.min(t,e,r),a=n-s,o=0===n?0:a/n,h=n/255;switch(n){case s:i=0;break;case t:i=e-r+a*(e<r?6:0),i/=6*a;break;case e:i=r-t+2*a,i/=6*a;break;case r:i=t-e+4*a,i/=6*a}return[i,o,h]}function addSaturationToRGB(t,e){var r=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return r[1]+=e,r[1]>1?r[1]=1:r[1]<=0&&(r[1]=0),HSVtoRGB(r[0],r[1],r[2])}function addBrightnessToRGB(t,e){var r=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return r[2]+=e,r[2]>1?r[2]=1:r[2]<0&&(r[2]=0),HSVtoRGB(r[0],r[1],r[2])}function addHueToRGB(t,e){var r=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return r[0]+=e/360,r[0]>1?r[0]-=1:r[0]<0&&(r[0]+=1),HSVtoRGB(r[0],r[1],r[2])}var rgbToHex=function(){var t,e,r=[];for(t=0;t<256;t+=1)e=t.toString(16),r[t]=1===e.length?"0"+e:e;return function(t,e,i){return t<0&&(t=0),e<0&&(e=0),i<0&&(i=0),"#"+r[t]+r[e]+r[i]}}(),setSubframeEnabled=function(t){subframeEnabled=!!t},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(t){expressionsPlugin=t},getExpressionsPlugin=function(){return expressionsPlugin},setDefaultCurveSegments=function(t){defaultCurveSegments=t},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(t){idPrefix$1=t},getIdPrefix=function(){return idPrefix$1};function createNS(t){return document.createElementNS(svgNS,t)}function _typeof$5(t){return(_typeof$5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var dataManager=function(){var t,e,r=1,i=[],n={onmessage:function(){},postMessage:function(e){t({data:e})}},s={postMessage:function(t){n.onmessage({data:t})}};function a(){e||((e=function(e){if(window.Worker&&window.Blob&&getWebWorker()){var r=new Blob(["var _workerSelf = self; self.onmessage = ",e.toString()],{type:"text/javascript"}),i=URL.createObjectURL(r);return new Worker(i)}return t=e,n}((function(t){if(s.dataManager||(s.dataManager=function(){function t(n,s){var a,o,h,l,p,f,u=n.length;for(o=0;o<u;o+=1)if("ks"in(a=n[o])&&!a.completed){if(a.completed=!0,a.tt&&(n[o-1].td=a.tt),a.hasMask){var d=a.masksProperties;for(l=d.length,h=0;h<l;h+=1)if(d[h].pt.k.i)i(d[h].pt.k);else for(f=d[h].pt.k.length,p=0;p<f;p+=1)d[h].pt.k[p].s&&i(d[h].pt.k[p].s[0]),d[h].pt.k[p].e&&i(d[h].pt.k[p].e[0])}0===a.ty?(a.layers=e(a.refId,s),t(a.layers,s)):4===a.ty?r(a.shapes):5===a.ty&&c(a)}}function e(t,e){var r=function(t,e){for(var r=0,i=e.length;r<i;){if(e[r].id===t)return e[r];r+=1}return null}(t,e);return r?r.layers.__used?JSON.parse(JSON.stringify(r.layers)):(r.layers.__used=!0,r.layers):null}function r(t){var e,n,s;for(e=t.length-1;e>=0;e-=1)if("sh"===t[e].ty)if(t[e].ks.k.i)i(t[e].ks.k);else for(s=t[e].ks.k.length,n=0;n<s;n+=1)t[e].ks.k[n].s&&i(t[e].ks.k[n].s[0]),t[e].ks.k[n].e&&i(t[e].ks.k[n].e[0]);else"gr"===t[e].ty&&r(t[e].it)}function i(t){var e,r=t.i.length;for(e=0;e<r;e+=1)t.i[e][0]+=t.v[e][0],t.i[e][1]+=t.v[e][1],t.o[e][0]+=t.v[e][0],t.o[e][1]+=t.v[e][1]}function n(t,e){var r=e?e.split("."):[100,100,100];return t[0]>r[0]||!(r[0]>t[0])&&(t[1]>r[1]||!(r[1]>t[1])&&(t[2]>r[2]||!(r[2]>t[2])&&null))}var s,a=function(){var t=[4,4,14];function e(t){var e,r,i,n=t.length;for(e=0;e<n;e+=1)5===t[e].ty&&(r=t[e],i=void 0,i=r.t.d,r.t.d={k:[{s:i,t:0}]})}return function(r){if(n(t,r.v)&&(e(r.layers),r.assets)){var i,s=r.assets.length;for(i=0;i<s;i+=1)r.assets[i].layers&&e(r.assets[i].layers)}}}(),o=(s=[4,7,99],function(t){if(t.chars&&!n(s,t.v)){var e,i=t.chars.length;for(e=0;e<i;e+=1){var a=t.chars[e];a.data&&a.data.shapes&&(r(a.data.shapes),a.data.ip=0,a.data.op=99999,a.data.st=0,a.data.sr=1,a.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[e].t||(a.data.shapes.push({ty:"no"}),a.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}),h=function(){var t=[5,7,15];function e(t){var e,r,i,n=t.length;for(e=0;e<n;e+=1)5===t[e].ty&&(r=t[e],i=void 0,"number"==typeof(i=r.t.p).a&&(i.a={a:0,k:i.a}),"number"==typeof i.p&&(i.p={a:0,k:i.p}),"number"==typeof i.r&&(i.r={a:0,k:i.r}))}return function(r){if(n(t,r.v)&&(e(r.layers),r.assets)){var i,s=r.assets.length;for(i=0;i<s;i+=1)r.assets[i].layers&&e(r.assets[i].layers)}}}(),l=function(){var t=[4,1,9];function e(t){var r,i,n,s=t.length;for(r=0;r<s;r+=1)if("gr"===t[r].ty)e(t[r].it);else if("fl"===t[r].ty||"st"===t[r].ty)if(t[r].c.k&&t[r].c.k[0].i)for(n=t[r].c.k.length,i=0;i<n;i+=1)t[r].c.k[i].s&&(t[r].c.k[i].s[0]/=255,t[r].c.k[i].s[1]/=255,t[r].c.k[i].s[2]/=255,t[r].c.k[i].s[3]/=255),t[r].c.k[i].e&&(t[r].c.k[i].e[0]/=255,t[r].c.k[i].e[1]/=255,t[r].c.k[i].e[2]/=255,t[r].c.k[i].e[3]/=255);else t[r].c.k[0]/=255,t[r].c.k[1]/=255,t[r].c.k[2]/=255,t[r].c.k[3]/=255}function r(t){var r,i=t.length;for(r=0;r<i;r+=1)4===t[r].ty&&e(t[r].shapes)}return function(e){if(n(t,e.v)&&(r(e.layers),e.assets)){var i,s=e.assets.length;for(i=0;i<s;i+=1)e.assets[i].layers&&r(e.assets[i].layers)}}}(),p=function(){var t=[4,4,18];function e(t){var r,i,n;for(r=t.length-1;r>=0;r-=1)if("sh"===t[r].ty)if(t[r].ks.k.i)t[r].ks.k.c=t[r].closed;else for(n=t[r].ks.k.length,i=0;i<n;i+=1)t[r].ks.k[i].s&&(t[r].ks.k[i].s[0].c=t[r].closed),t[r].ks.k[i].e&&(t[r].ks.k[i].e[0].c=t[r].closed);else"gr"===t[r].ty&&e(t[r].it)}function r(t){var r,i,n,s,a,o,h=t.length;for(i=0;i<h;i+=1){if((r=t[i]).hasMask){var l=r.masksProperties;for(s=l.length,n=0;n<s;n+=1)if(l[n].pt.k.i)l[n].pt.k.c=l[n].cl;else for(o=l[n].pt.k.length,a=0;a<o;a+=1)l[n].pt.k[a].s&&(l[n].pt.k[a].s[0].c=l[n].cl),l[n].pt.k[a].e&&(l[n].pt.k[a].e[0].c=l[n].cl)}4===r.ty&&e(r.shapes)}}return function(e){if(n(t,e.v)&&(r(e.layers),e.assets)){var i,s=e.assets.length;for(i=0;i<s;i+=1)e.assets[i].layers&&r(e.assets[i].layers)}}}();function c(t){0===t.t.a.length&&t.t.p}var f={completeData:function(r){r.__complete||(l(r),a(r),o(r),h(r),p(r),t(r.layers,r.assets),function(r,i){if(r){var n=0,s=r.length;for(n=0;n<s;n+=1)1===r[n].t&&(r[n].data.layers=e(r[n].data.refId,i),t(r[n].data.layers,i))}}(r.chars,r.assets),r.__complete=!0)}};return f.checkColors=l,f.checkChars=o,f.checkPathProperties=h,f.checkShapes=p,f.completeLayers=t,f}()),s.assetLoader||(s.assetLoader=function(){function t(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===_typeof$5(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}return{load:function(e,r,i,n){var s,a=new XMLHttpRequest;try{a.responseType="json"}catch(t){}a.onreadystatechange=function(){if(4===a.readyState)if(200===a.status)s=t(a),i(s);else try{s=t(a),i(s)}catch(t){n&&n(t)}};try{a.open("GET",e,!0)}catch(t){a.open("GET",r+"/"+e,!0)}a.send()}}}()),"loadAnimation"===t.data.type)s.assetLoader.load(t.data.path,t.data.fullPath,(function(e){s.dataManager.completeData(e),s.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){s.postMessage({id:t.data.id,status:"error"})}));else if("complete"===t.data.type){var e=t.data.animation;s.dataManager.completeData(e),s.postMessage({id:t.data.id,payload:e,status:"success"})}else"loadData"===t.data.type&&s.assetLoader.load(t.data.path,t.data.fullPath,(function(e){s.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){s.postMessage({id:t.data.id,status:"error"})}))}))).onmessage=function(t){var e=t.data,r=e.id,n=i[r];i[r]=null,"success"===e.status?n.onComplete(e.payload):n.onError&&n.onError()})}function o(t,e){var n="processId_"+(r+=1);return i[n]={onComplete:t,onError:e},n}return{loadAnimation:function(t,r,i){a();var n=o(r,i);e.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:n})},loadData:function(t,r,i){a();var n=o(r,i);e.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:n})},completeAnimation:function(t,r,i){a();var n=o(r,i);e.postMessage({type:"complete",animation:t,id:n})}}}(),ImagePreloader=function(){var t=function(){var t=createTag("canvas");t.width=1,t.height=1;var e=t.getContext("2d");return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),t}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function r(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(t,e,r){var i="";if(t.e)i=t.p;else if(e){var n=t.p;-1!==n.indexOf("images/")&&(n=n.split("/")[1]),i=e+n}else i=r,i+=t.u?t.u:"",i+=t.p;return i}function n(t){var e=0,r=setInterval(function(){(t.getBBox().width||e>500)&&(this._imageLoaded(),clearInterval(r)),e+=1}.bind(this),50)}function s(t){var e={assetData:t},r=i(t,this.assetsPath,this.path);return dataManager.loadData(r,function(t){e.img=t,this._footageLoaded()}.bind(this),function(){e.img={},this._footageLoaded()}.bind(this)),e}function a(){this._imageLoaded=e.bind(this),this._footageLoaded=r.bind(this),this.testImageLoaded=n.bind(this),this.createFootageData=s.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return a.prototype={loadAssets:function(t,e){var r;this.imagesLoadedCb=e;var i=t.length;for(r=0;r<i;r+=1)t[r].layers||(t[r].t&&"seq"!==t[r].t?3===t[r].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[r]))):(this.totalImages+=1,this.images.push(this._createImageData(t[r]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,r=this.images.length;e<r;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(e){var r=i(e,this.assetsPath,this.path),n=createTag("img");n.crossOrigin="anonymous",n.addEventListener("load",this._imageLoaded,!1),n.addEventListener("error",function(){s.img=t,this._imageLoaded()}.bind(this),!1),n.src=r;var s={img:n,assetData:e};return s},createImageData:function(e){var r=i(e,this.assetsPath,this.path),n=createNS("image");isSafari?this.testImageLoaded(n):n.addEventListener("load",this._imageLoaded,!1),n.addEventListener("error",function(){s.img=t,this._imageLoaded()}.bind(this),!1),n.setAttributeNS("http://www.w3.org/1999/xlink","href",r),this._elementHelper.append?this._elementHelper.append(n):this._elementHelper.appendChild(n);var s={img:n,assetData:e};return s},imageLoaded:e,footageLoaded:r,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},a}();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var r=this._cbs[t],i=0;i<r.length;i+=1)r[i](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),function(){this.removeEventListener(t,e)}.bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var r=0,i=this._cbs[t].length;r<i;)this._cbs[t][r]===e&&(this._cbs[t].splice(r,1),r-=1,i-=1),r+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var markerParser=function(){function t(t){for(var e,r=t.split("\r\n"),i={},n=0,s=0;s<r.length;s+=1)2===(e=r[s].split(":")).length&&(i[e[0]]=e[1].trim(),n+=1);if(0===n)throw new Error;return i}return function(e){for(var r=[],i=0;i<e.length;i+=1){var n=e[i],s={time:n.tm,duration:n.dr};try{s.payload=JSON.parse(e[i].cm)}catch(r){try{s.payload=t(e[i].cm)}catch(t){s.payload={name:e[i].cm}}}r.push(s)}return r}}(),ProjectInterface=function(){function t(t){this.compositions.push(t)}return function(){function e(t){for(var e=0,r=this.compositions.length;e<r;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),renderers={},registerRenderer=function(t,e){renderers[t]=e};function getRenderer(t){return renderers[t]}function _typeof$4(t){return(_typeof$4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0)};extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var r=getRenderer(e);this.renderer=new r(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null===t.loop||void 0===t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name?t.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(t.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(t){dataManager.completeAnimation(t,this.configAnimation)},AnimationItem.prototype.setData=function(t,e){e&&"object"!==_typeof$4(e)&&(e=JSON.parse(e));var r={wrapper:t,animationData:e},i=t.attributes;r.path=i.getNamedItem("data-animation-path")?i.getNamedItem("data-animation-path").value:i.getNamedItem("data-bm-path")?i.getNamedItem("data-bm-path").value:i.getNamedItem("bm-path")?i.getNamedItem("bm-path").value:"",r.animType=i.getNamedItem("data-anim-type")?i.getNamedItem("data-anim-type").value:i.getNamedItem("data-bm-type")?i.getNamedItem("data-bm-type").value:i.getNamedItem("bm-type")?i.getNamedItem("bm-type").value:i.getNamedItem("data-bm-renderer")?i.getNamedItem("data-bm-renderer").value:i.getNamedItem("bm-renderer")?i.getNamedItem("bm-renderer").value:"canvas";var n=i.getNamedItem("data-anim-loop")?i.getNamedItem("data-anim-loop").value:i.getNamedItem("data-bm-loop")?i.getNamedItem("data-bm-loop").value:i.getNamedItem("bm-loop")?i.getNamedItem("bm-loop").value:"";"false"===n?r.loop=!1:"true"===n?r.loop=!0:""!==n&&(r.loop=parseInt(n,10));var s=i.getNamedItem("data-anim-autoplay")?i.getNamedItem("data-anim-autoplay").value:i.getNamedItem("data-bm-autoplay")?i.getNamedItem("data-bm-autoplay").value:!i.getNamedItem("bm-autoplay")||i.getNamedItem("bm-autoplay").value;r.autoplay="false"!==s,r.name=i.getNamedItem("data-name")?i.getNamedItem("data-name").value:i.getNamedItem("data-bm-name")?i.getNamedItem("data-bm-name").value:i.getNamedItem("bm-name")?i.getNamedItem("bm-name").value:"","false"===(i.getNamedItem("data-anim-prerender")?i.getNamedItem("data-anim-prerender").value:i.getNamedItem("data-bm-prerender")?i.getNamedItem("data-bm-prerender").value:i.getNamedItem("bm-prerender")?i.getNamedItem("bm-prerender").value:"")&&(r.prerender=!1),this.setParams(r)},AnimationItem.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e,r,i=this.animationData.layers,n=i.length,s=t.layers,a=s.length;for(r=0;r<a;r+=1)for(e=0;e<n;){if(i[e].id===s[r].id){i[e]=s[r];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(n=t.assets.length,e=0;e<n;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(t){this.animationData=t;var e=getExpressionsPlugin();e&&e.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||0===t.length||!this.autoloadSegments)return this.trigger("data_ready"),void(this.timeCompleted=this.totalFrames);var e=t.shift();this.timeCompleted=e.time*this.frameRate;var r=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,dataManager.loadData(r,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},AnimationItem.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=markerParser(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},AnimationItem.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=getExpressionsPlugin();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},AnimationItem.prototype.resize=function(){this.renderer.updateContainerSize()},AnimationItem.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},AnimationItem.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_pause"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},AnimationItem.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_play"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},AnimationItem.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(t){for(var e,r=0;r<this.markers.length;r+=1)if((e=this.markers[r]).payload&&e.payload.name===t)return e;return null},AnimationItem.prototype.goToAndStop=function(t,e,r){if(!r||this.name===r){var i=Number(t);if(isNaN(i)){var n=this.getMarkerData(t);n&&this.goToAndStop(n.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},AnimationItem.prototype.goToAndPlay=function(t,e,r){if(!r||this.name===r){var i=Number(t);if(isNaN(i)){var n=this.getMarkerData(t);n&&(n.duration?this.playSegments([n.time,n.time+n.duration],!0):this.goToAndStop(n.time,!0))}else this.goToAndStop(i,e,r);this.play()}},AnimationItem.prototype.advanceTime=function(t){if(!0!==this.isPaused&&!1!==this.isLoaded){var e=this.currentRawFrame+t*this.frameModifier,r=!1;e>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(r=!0,e=this.totalFrames-1):e<0?this.checkSegments(e%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(r=!0,e=0):(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(e),r&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},AnimationItem.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(t,e){var r=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?r=t:this.currentRawFrame+this.firstFrame>e&&(r=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==r&&this.goToAndStop(r,!0)},AnimationItem.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===_typeof$4(t[0])){var r,i=t.length;for(r=0;r<i;r+=1)this.segments.push(t[r])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},AnimationItem.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.renderer=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},AnimationItem.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var r=t.p;-1!==r.indexOf("images/")&&(r=r.split("/")[1]),e=this.assetsPath+r}else e=this.path,e+=t.u?t.u:"",e+=t.p;return e},AnimationItem.prototype.getAssetData=function(t){for(var e=0,r=this.assets.length;e<r;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(t,e,r){try{this.renderer.getElementByPath(t).updateDocumentData(e,r)}catch(t){}},AnimationItem.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new BMCompleteEvent(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new BMDestroyEvent(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(t,this))},AnimationItem.prototype.triggerRenderFrameError=function(t){var e=new BMRenderFrameErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},AnimationItem.prototype.triggerConfigError=function(t){var e=new BMConfigErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var animationManager=function(){var t={},e=[],r=0,i=0,n=0,s=!0,a=!1;function o(t){for(var r=0,n=t.target;r<i;)e[r].animation===n&&(e.splice(r,1),r-=1,i-=1,n.isPaused||p()),r+=1}function h(t,r){if(!t)return null;for(var n=0;n<i;){if(e[n].elem===t&&null!==e[n].elem)return e[n].animation;n+=1}var s=new AnimationItem;return c(s,t),s.setData(t,r),s}function l(){n+=1,d()}function p(){n-=1}function c(t,r){t.addEventListener("destroy",o),t.addEventListener("_active",l),t.addEventListener("_idle",p),e.push({elem:r,animation:t}),i+=1}function f(t){var o,h=t-r;for(o=0;o<i;o+=1)e[o].animation.advanceTime(h);r=t,n&&!a?window.requestAnimationFrame(f):s=!0}function u(t){r=t,window.requestAnimationFrame(f)}function d(){!a&&n&&s&&(window.requestAnimationFrame(u),s=!1)}return t.registerAnimation=h,t.loadAnimation=function(t){var e=new AnimationItem;return c(e,null),e.setParams(t),e},t.setSpeed=function(t,r){var n;for(n=0;n<i;n+=1)e[n].animation.setSpeed(t,r)},t.setDirection=function(t,r){var n;for(n=0;n<i;n+=1)e[n].animation.setDirection(t,r)},t.play=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.play(t)},t.pause=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.pause(t)},t.stop=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.stop(t)},t.togglePause=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.togglePause(t)},t.searchAnimations=function(t,e,r){var i,n=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),s=n.length;for(i=0;i<s;i+=1)r&&n[i].setAttribute("data-bm-type",r),h(n[i],t);if(e&&0===s){r||(r="svg");var a=document.getElementsByTagName("body")[0];a.innerText="";var o=createTag("div");o.style.width="100%",o.style.height="100%",o.setAttribute("data-bm-type",r),a.appendChild(o),h(o,t)}},t.resize=function(){var t;for(t=0;t<i;t+=1)e[t].animation.resize()},t.goToAndStop=function(t,r,n){var s;for(s=0;s<i;s+=1)e[s].animation.goToAndStop(t,r,n)},t.destroy=function(t){var r;for(r=i-1;r>=0;r-=1)e[r].animation.destroy(t)},t.freeze=function(){a=!0},t.unfreeze=function(){a=!1,d()},t.setVolume=function(t,r){var n;for(n=0;n<i;n+=1)e[n].animation.setVolume(t,r)},t.mute=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.mute(t)},t.unmute=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.unmute(t)},t.getRegisteredAnimations=function(){var t,r=e.length,i=[];for(t=0;t<r;t+=1)i.push(e[t].animation);return i},t}(),BezierFactory=function(){var t={getBezierEasing:function(t,r,i,n,s){var a=s||("bez_"+t+"_"+r+"_"+i+"_"+n).replace(/\./g,"p");if(e[a])return e[a];var o=new h([t,r,i,n]);return e[a]=o,o}},e={};var r="function"==typeof Float32Array;function i(t,e){return 1-3*e+3*t}function n(t,e){return 3*e-6*t}function s(t){return 3*t}function a(t,e,r){return((i(e,r)*t+n(e,r))*t+s(e))*t}function o(t,e,r){return 3*i(e,r)*t*t+2*n(e,r)*t+s(e)}function h(t){this._p=t,this._mSampleValues=r?new Float32Array(11):new Array(11),this._precomputed=!1,this.get=this.get.bind(this)}return h.prototype={get:function(t){var e=this._p[0],r=this._p[1],i=this._p[2],n=this._p[3];return this._precomputed||this._precompute(),e===r&&i===n?t:0===t?0:1===t?1:a(this._getTForX(t),r,n)},_precompute:function(){var t=this._p[0],e=this._p[1],r=this._p[2],i=this._p[3];this._precomputed=!0,t===e&&r===i||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],r=0;r<11;++r)this._mSampleValues[r]=a(.1*r,t,e)},_getTForX:function(t){for(var e=this._p[0],r=this._p[2],i=this._mSampleValues,n=0,s=1;10!==s&&i[s]<=t;++s)n+=.1;var h=n+.1*((t-i[--s])/(i[s+1]-i[s])),l=o(h,e,r);return l>=.001?function(t,e,r,i){for(var n=0;n<4;++n){var s=o(e,r,i);if(0===s)return e;e-=(a(e,r,i)-t)/s}return e}(t,h,e,r):0===l?h:function(t,e,r,i,n){var s,o,h=0;do{(s=a(o=e+(r-e)/2,i,n)-t)>0?r=o:e=o}while(Math.abs(s)>1e-7&&++h<10);return o}(t,n,n+.1,e,r)}},t}(),pooling={double:function(t){return t.concat(createSizedArray(t.length))}},poolFactory=function(t,e,r){var i=0,n=t,s=createSizedArray(n);return{newElement:function(){return i?s[i-=1]:e()},release:function(t){i===n&&(s=pooling.double(s),n*=2),r&&r(t),s[i]=t,i+=1}}},bezierLengthPool=poolFactory(8,(function(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}})),segmentsLengthPool=poolFactory(8,(function(){return{lengths:[],totalLength:0}}),(function(t){var e,r=t.lengths.length;for(e=0;e<r;e+=1)bezierLengthPool.release(t.lengths[e]);t.lengths.length=0}));function bezFunction(){var t=Math;function e(t,e,r,i,n,s){var a=t*i+e*n+r*s-n*i-s*t-r*e;return a>-.001&&a<.001}var r=function(t,e,r,i){var n,s,a,o,h,l,p=getDefaultCurveSegments(),c=0,f=[],u=[],d=bezierLengthPool.newElement();for(a=r.length,n=0;n<p;n+=1){for(h=n/(p-1),l=0,s=0;s<a;s+=1)o=bmPow(1-h,3)*t[s]+3*bmPow(1-h,2)*h*r[s]+3*(1-h)*bmPow(h,2)*i[s]+bmPow(h,3)*e[s],f[s]=o,null!==u[s]&&(l+=bmPow(f[s]-u[s],2)),u[s]=f[s];l&&(c+=l=bmSqrt(l)),d.percents[n]=h,d.lengths[n]=c}return d.addedLength=c,d};function i(t){this.segmentLength=0,this.points=new Array(t)}function n(t,e){this.partialLength=t,this.point=e}var s,a=(s={},function(t,r,a,o){var h=(t[0]+"_"+t[1]+"_"+r[0]+"_"+r[1]+"_"+a[0]+"_"+a[1]+"_"+o[0]+"_"+o[1]).replace(/\./g,"p");if(!s[h]){var l,p,c,f,u,d,m,y=getDefaultCurveSegments(),g=0,v=null;2===t.length&&(t[0]!==r[0]||t[1]!==r[1])&&e(t[0],t[1],r[0],r[1],t[0]+a[0],t[1]+a[1])&&e(t[0],t[1],r[0],r[1],r[0]+o[0],r[1]+o[1])&&(y=2);var _=new i(y);for(c=a.length,l=0;l<y;l+=1){for(m=createSizedArray(c),u=l/(y-1),d=0,p=0;p<c;p+=1)f=bmPow(1-u,3)*t[p]+3*bmPow(1-u,2)*u*(t[p]+a[p])+3*(1-u)*bmPow(u,2)*(r[p]+o[p])+bmPow(u,3)*r[p],m[p]=f,null!==v&&(d+=bmPow(m[p]-v[p],2));g+=d=bmSqrt(d),_.points[l]=new n(d,m),v=m}_.segmentLength=g,s[h]=_}return s[h]});function o(t,e){var r=e.percents,i=e.lengths,n=r.length,s=bmFloor((n-1)*t),a=t*e.addedLength,o=0;if(s===n-1||0===s||a===i[s])return r[s];for(var h=i[s]>a?-1:1,l=!0;l;)if(i[s]<=a&&i[s+1]>a?(o=(a-i[s])/(i[s+1]-i[s]),l=!1):s+=h,s<0||s>=n-1){if(s===n-1)return r[s];l=!1}return r[s]+(r[s+1]-r[s])*o}var h=createTypedArray("float32",8);return{getSegmentsLength:function(t){var e,i=segmentsLengthPool.newElement(),n=t.c,s=t.v,a=t.o,o=t.i,h=t._length,l=i.lengths,p=0;for(e=0;e<h-1;e+=1)l[e]=r(s[e],s[e+1],a[e],o[e+1]),p+=l[e].addedLength;return n&&h&&(l[e]=r(s[e],s[0],a[e],o[0]),p+=l[e].addedLength),i.totalLength=p,i},getNewSegment:function(e,r,i,n,s,a,l){s<0?s=0:s>1&&(s=1);var p,c=o(s,l),f=o(a=a>1?1:a,l),u=e.length,d=1-c,m=1-f,y=d*d*d,g=c*d*d*3,v=c*c*d*3,_=c*c*c,b=d*d*m,P=c*d*m+d*c*m+d*d*f,S=c*c*m+d*c*f+c*d*f,w=c*c*f,k=d*m*m,x=c*m*m+d*f*m+d*m*f,E=c*f*m+d*f*f+c*m*f,A=c*f*f,C=m*m*m,T=f*m*m+m*f*m+m*m*f,D=f*f*m+m*f*f+f*m*f,I=f*f*f;for(p=0;p<u;p+=1)h[4*p]=t.round(1e3*(y*e[p]+g*i[p]+v*n[p]+_*r[p]))/1e3,h[4*p+1]=t.round(1e3*(b*e[p]+P*i[p]+S*n[p]+w*r[p]))/1e3,h[4*p+2]=t.round(1e3*(k*e[p]+x*i[p]+E*n[p]+A*r[p]))/1e3,h[4*p+3]=t.round(1e3*(C*e[p]+T*i[p]+D*n[p]+I*r[p]))/1e3;return h},getPointInSegment:function(e,r,i,n,s,a){var h=o(s,a),l=1-h;return[t.round(1e3*(l*l*l*e[0]+(h*l*l+l*h*l+l*l*h)*i[0]+(h*h*l+l*h*h+h*l*h)*n[0]+h*h*h*r[0]))/1e3,t.round(1e3*(l*l*l*e[1]+(h*l*l+l*h*l+l*l*h)*i[1]+(h*h*l+l*h*h+h*l*h)*n[1]+h*h*h*r[1]))/1e3]},buildBezierData:a,pointOnLine2D:e,pointOnLine3D:function(r,i,n,s,a,o,h,l,p){if(0===n&&0===o&&0===p)return e(r,i,s,a,h,l);var c,f=t.sqrt(t.pow(s-r,2)+t.pow(a-i,2)+t.pow(o-n,2)),u=t.sqrt(t.pow(h-r,2)+t.pow(l-i,2)+t.pow(p-n,2)),d=t.sqrt(t.pow(h-s,2)+t.pow(l-a,2)+t.pow(p-o,2));return(c=f>u?f>d?f-u-d:d-u-f:d>u?d-u-f:u-f-d)>-1e-4&&c<1e-4}}}var bez=bezFunction(),PropertyFactory=function(){var t=initialDefaultFrame,e=Math.abs;function r(t,e){var r,n=this.offsetTime;"multidimensional"===this.propType&&(r=createTypedArray("float32",this.pv.length));for(var s,a,o,h,l,p,c,f,u,d=e.lastIndex,m=d,y=this.keyframes.length-1,g=!0;g;){if(s=this.keyframes[m],a=this.keyframes[m+1],m===y-1&&t>=a.t-n){s.h&&(s=a),d=0;break}if(a.t-n>t){d=m;break}m<y-1?m+=1:(d=0,g=!1)}o=this.keyframesMetadata[m]||{};var v,_=a.t-n,b=s.t-n;if(s.to){o.bezierData||(o.bezierData=bez.buildBezierData(s.s,a.s||s.e,s.to,s.ti));var P=o.bezierData;if(t>=_||t<b){var S=t>=_?P.points.length-1:0;for(l=P.points[S].point.length,h=0;h<l;h+=1)r[h]=P.points[S].point[h]}else{o.__fnct?u=o.__fnct:(u=BezierFactory.getBezierEasing(s.o.x,s.o.y,s.i.x,s.i.y,s.n).get,o.__fnct=u),p=u((t-b)/(_-b));var w,k=P.segmentLength*p,x=e.lastFrame<t&&e._lastKeyframeIndex===m?e._lastAddedLength:0;for(f=e.lastFrame<t&&e._lastKeyframeIndex===m?e._lastPoint:0,g=!0,c=P.points.length;g;){if(x+=P.points[f].partialLength,0===k||0===p||f===P.points.length-1){for(l=P.points[f].point.length,h=0;h<l;h+=1)r[h]=P.points[f].point[h];break}if(k>=x&&k<x+P.points[f+1].partialLength){for(w=(k-x)/P.points[f+1].partialLength,l=P.points[f].point.length,h=0;h<l;h+=1)r[h]=P.points[f].point[h]+(P.points[f+1].point[h]-P.points[f].point[h])*w;break}f<c-1?f+=1:g=!1}e._lastPoint=f,e._lastAddedLength=x-P.points[f].partialLength,e._lastKeyframeIndex=m}}else{var E,A,C,T,D;if(y=s.s.length,v=a.s||s.e,this.sh&&1!==s.h)if(t>=_)r[0]=v[0],r[1]=v[1],r[2]=v[2];else if(t<=b)r[0]=s.s[0],r[1]=s.s[1],r[2]=s.s[2];else{!function(t,e){var r=e[0],i=e[1],n=e[2],s=e[3],a=Math.atan2(2*i*s-2*r*n,1-2*i*i-2*n*n),o=Math.asin(2*r*i+2*n*s),h=Math.atan2(2*r*s-2*i*n,1-2*r*r-2*n*n);t[0]=a/degToRads,t[1]=o/degToRads,t[2]=h/degToRads}(r,function(t,e,r){var i,n,s,a,o,h=[],l=t[0],p=t[1],c=t[2],f=t[3],u=e[0],d=e[1],m=e[2],y=e[3];(n=l*u+p*d+c*m+f*y)<0&&(n=-n,u=-u,d=-d,m=-m,y=-y);1-n>1e-6?(i=Math.acos(n),s=Math.sin(i),a=Math.sin((1-r)*i)/s,o=Math.sin(r*i)/s):(a=1-r,o=r);return h[0]=a*l+o*u,h[1]=a*p+o*d,h[2]=a*c+o*m,h[3]=a*f+o*y,h}(i(s.s),i(v),(t-b)/(_-b)))}else for(m=0;m<y;m+=1)1!==s.h&&(t>=_?p=1:t<b?p=0:(s.o.x.constructor===Array?(o.__fnct||(o.__fnct=[]),o.__fnct[m]?u=o.__fnct[m]:(E=void 0===s.o.x[m]?s.o.x[0]:s.o.x[m],A=void 0===s.o.y[m]?s.o.y[0]:s.o.y[m],C=void 0===s.i.x[m]?s.i.x[0]:s.i.x[m],T=void 0===s.i.y[m]?s.i.y[0]:s.i.y[m],u=BezierFactory.getBezierEasing(E,A,C,T).get,o.__fnct[m]=u)):o.__fnct?u=o.__fnct:(E=s.o.x,A=s.o.y,C=s.i.x,T=s.i.y,u=BezierFactory.getBezierEasing(E,A,C,T).get,s.keyframeMetadata=u),p=u((t-b)/(_-b)))),v=a.s||s.e,D=1===s.h?s.s[m]:s.s[m]+(v[m]-s.s[m])*p,"multidimensional"===this.propType?r[m]=D:r=D}return e.lastIndex=d,r}function i(t){var e=t[0]*degToRads,r=t[1]*degToRads,i=t[2]*degToRads,n=Math.cos(e/2),s=Math.cos(r/2),a=Math.cos(i/2),o=Math.sin(e/2),h=Math.sin(r/2),l=Math.sin(i/2);return[o*h*a+n*s*l,o*s*a+n*h*l,n*h*a-o*s*l,n*s*a-o*h*l]}function n(){var e=this.comp.renderedFrame-this.offsetTime,r=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(e===this._caching.lastFrame||this._caching.lastFrame!==t&&(this._caching.lastFrame>=i&&e>=i||this._caching.lastFrame<r&&e<r))){this._caching.lastFrame>=e&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var n=this.interpolateValue(e,this._caching);this.pv=n}return this._caching.lastFrame=e,this.pv}function s(t){var r;if("unidimensional"===this.propType)r=t*this.mult,e(this.v-r)>1e-5&&(this.v=r,this._mdf=!0);else for(var i=0,n=this.v.length;i<n;)r=t[i]*this.mult,e(this.v[i]-r)>1e-5&&(this.v[i]=r,this._mdf=!0),i+=1}function a(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t;this.lock=!0,this._mdf=this._isFirstFrame;var e=this.effectsSequence.length,r=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)r=this.effectsSequence[t](r);this.setVValue(r),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function o(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function h(t,e,r,i){this.propType="unidimensional",this.mult=r||1,this.data=e,this.v=r?e.k*r:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=a,this.setVValue=s,this.addEffect=o}function l(t,e,r,i){var n;this.propType="multidimensional",this.mult=r||1,this.data=e,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var h=e.k.length;for(this.v=createTypedArray("float32",h),this.pv=createTypedArray("float32",h),this.vel=createTypedArray("float32",h),n=0;n<h;n+=1)this.v[n]=e.k[n]*this.mult,this.pv[n]=e.k[n];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=a,this.setVValue=s,this.addEffect=o}function p(e,i,h,l){this.propType="unidimensional",this.keyframes=i.k,this.keyframesMetadata=[],this.offsetTime=e.data.st,this.frameId=-1,this._caching={lastFrame:t,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=i,this.mult=h||1,this.elem=e,this.container=l,this.comp=e.comp,this.v=t,this.pv=t,this._isFirstFrame=!0,this.getValue=a,this.setVValue=s,this.interpolateValue=r,this.effectsSequence=[n.bind(this)],this.addEffect=o}function c(e,i,h,l){var p;this.propType="multidimensional";var c,f,u,d,m=i.k.length;for(p=0;p<m-1;p+=1)i.k[p].to&&i.k[p].s&&i.k[p+1]&&i.k[p+1].s&&(c=i.k[p].s,f=i.k[p+1].s,u=i.k[p].to,d=i.k[p].ti,(2===c.length&&(c[0]!==f[0]||c[1]!==f[1])&&bez.pointOnLine2D(c[0],c[1],f[0],f[1],c[0]+u[0],c[1]+u[1])&&bez.pointOnLine2D(c[0],c[1],f[0],f[1],f[0]+d[0],f[1]+d[1])||3===c.length&&(c[0]!==f[0]||c[1]!==f[1]||c[2]!==f[2])&&bez.pointOnLine3D(c[0],c[1],c[2],f[0],f[1],f[2],c[0]+u[0],c[1]+u[1],c[2]+u[2])&&bez.pointOnLine3D(c[0],c[1],c[2],f[0],f[1],f[2],f[0]+d[0],f[1]+d[1],f[2]+d[2]))&&(i.k[p].to=null,i.k[p].ti=null),c[0]===f[0]&&c[1]===f[1]&&0===u[0]&&0===u[1]&&0===d[0]&&0===d[1]&&(2===c.length||c[2]===f[2]&&0===u[2]&&0===d[2])&&(i.k[p].to=null,i.k[p].ti=null));this.effectsSequence=[n.bind(this)],this.data=i,this.keyframes=i.k,this.keyframesMetadata=[],this.offsetTime=e.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=h||1,this.elem=e,this.container=l,this.comp=e.comp,this.getValue=a,this.setVValue=s,this.interpolateValue=r,this.frameId=-1;var y=i.k[0].s.length;for(this.v=createTypedArray("float32",y),this.pv=createTypedArray("float32",y),p=0;p<y;p+=1)this.v[p]=t,this.pv[p]=t;this._caching={lastFrame:t,lastIndex:0,value:createTypedArray("float32",y)},this.addEffect=o}return{getProp:function(t,e,r,i,n){var s;if(e.k.length)if("number"==typeof e.k[0])s=new l(t,e,i,n);else switch(r){case 0:s=new p(t,e,i,n);break;case 1:s=new c(t,e,i,n)}else s=new h(t,e,i,n);return s.effectsSequence.length&&n.addDynamicProperty(s),s}}}();function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){var t;this._mdf=!1;var e=this.dynamicProperties.length;for(t=0;t<e;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=poolFactory(8,(function(){return createTypedArray("float32",2)}));function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var r=0;r<e;)this.v[r]=pointPool.newElement(),this.o[r]=pointPool.newElement(),this.i[r]=pointPool.newElement(),r+=1},ShapePath.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(t,e,r,i,n){var s;switch(this._length=Math.max(this._length,i+1),this._length>=this._maxLength&&this.doubleArrayLength(),r){case"v":s=this.v;break;case"i":s=this.i;break;case"o":s=this.o;break;default:s=[]}(!s[i]||s[i]&&!n)&&(s[i]=pointPool.newElement()),s[i][0]=t,s[i][1]=e},ShapePath.prototype.setTripleAt=function(t,e,r,i,n,s,a,o){this.setXYAt(t,e,"v",a,o),this.setXYAt(r,i,"o",a,o),this.setXYAt(n,s,"i",a,o)},ShapePath.prototype.reverse=function(){var t=new ShapePath;t.setPathData(this.c,this._length);var e=this.v,r=this.o,i=this.i,n=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],i[0][0],i[0][1],r[0][0],r[0][1],0,!1),n=1);var s,a=this._length-1,o=this._length;for(s=n;s<o;s+=1)t.setTripleAt(e[a][0],e[a][1],i[a][0],i[a][1],r[a][0],r[a][1],s,!1),a-=1;return t};var shapePool=(factory=poolFactory(4,(function(){return new ShapePath}),(function(t){var e,r=t._length;for(e=0;e<r;e+=1)pointPool.release(t.v[e]),pointPool.release(t.i[e]),pointPool.release(t.o[e]),t.v[e]=null,t.i[e]=null,t.o[e]=null;t._length=0,t.c=!1})),factory.clone=function(t){var e,r=factory.newElement(),i=void 0===t._length?t.v.length:t._length;for(r.setLength(i),r.c=t.c,e=0;e<i;e+=1)r.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return r},factory),factory;function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)shapePool.release(this.shapes[t]);this._length=0};var shapeCollectionPool=(ob={newShapeCollection:function(){return _length?pool[_length-=1]:new ShapeCollection},release:function(t){var e,r=t._length;for(e=0;e<r;e+=1)shapePool.release(t.shapes[e]);t._length=0,_length===_maxLength&&(pool=pooling.double(pool),_maxLength*=2),pool[_length]=t,_length+=1}},_length=0,_maxLength=4,pool=createSizedArray(_maxLength),ob),ob,_length,_maxLength,pool,ShapePropertyFactory=function(){function t(t,e,r){var i,n,s,a,o,h,l,p,c,f=r.lastIndex,u=this.keyframes;if(t<u[0].t-this.offsetTime)i=u[0].s[0],s=!0,f=0;else if(t>=u[u.length-1].t-this.offsetTime)i=u[u.length-1].s?u[u.length-1].s[0]:u[u.length-2].e[0],s=!0;else{for(var d,m,y,g=f,v=u.length-1,_=!0;_&&(d=u[g],!((m=u[g+1]).t-this.offsetTime>t));)g<v-1?g+=1:_=!1;if(y=this.keyframesMetadata[g]||{},f=g,!(s=1===d.h)){if(t>=m.t-this.offsetTime)p=1;else if(t<d.t-this.offsetTime)p=0;else{var b;y.__fnct?b=y.__fnct:(b=BezierFactory.getBezierEasing(d.o.x,d.o.y,d.i.x,d.i.y).get,y.__fnct=b),p=b((t-(d.t-this.offsetTime))/(m.t-this.offsetTime-(d.t-this.offsetTime)))}n=m.s?m.s[0]:d.e[0]}i=d.s[0]}for(h=e._length,l=i.i[0].length,r.lastIndex=f,a=0;a<h;a+=1)for(o=0;o<l;o+=1)c=s?i.i[a][o]:i.i[a][o]+(n.i[a][o]-i.i[a][o])*p,e.i[a][o]=c,c=s?i.o[a][o]:i.o[a][o]+(n.o[a][o]-i.o[a][o])*p,e.o[a][o]=c,c=s?i.v[a][o]:i.v[a][o]+(n.v[a][o]-i.v[a][o])*p,e.v[a][o]=c}function e(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,r=this.keyframes[this.keyframes.length-1].t-this.offsetTime,i=this._caching.lastFrame;return-999999!==i&&(i<e&&t<e||i>r&&t>r)||(this._caching.lastIndex=i<t?this._caching.lastIndex:0,this.interpolateShape(t,this.pv,this._caching)),this._caching.lastFrame=t,this.pv}function r(){this.paths=this.localShapeCollection}function i(t){(function(t,e){if(t._length!==e._length||t.c!==e.c)return!1;var r,i=t._length;for(r=0;r<i;r+=1)if(t.v[r][0]!==e.v[r][0]||t.v[r][1]!==e.v[r][1]||t.o[r][0]!==e.o[r][0]||t.o[r][1]!==e.o[r][1]||t.i[r][0]!==e.i[r][0]||t.i[r][1]!==e.i[r][1])return!1;return!0})(this.v,t)||(this.v=shapePool.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function n(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t,e;this.lock=!0,this._mdf=!1,t=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var r=this.effectsSequence.length;for(e=0;e<r;e+=1)t=this.effectsSequence[e](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function s(t,e,i){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;var n=3===i?e.pt.k:e.ks.k;this.v=shapePool.clone(n),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=r,this.effectsSequence=[]}function a(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function o(t,i,n){this.propType="shape",this.comp=t.comp,this.elem=t,this.container=t,this.offsetTime=t.data.st,this.keyframes=3===n?i.pt.k:i.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var s=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,s),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=-999999,this.reset=r,this._caching={lastFrame:-999999,lastIndex:0},this.effectsSequence=[e.bind(this)]}s.prototype.interpolateShape=t,s.prototype.getValue=n,s.prototype.setVValue=i,s.prototype.addEffect=a,o.prototype.getValue=n,o.prototype.interpolateShape=t,o.prototype.setVValue=i,o.prototype.addEffect=a;var h=function(){var t=roundCorner;function e(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return e.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var e=this.p.v[0],r=this.p.v[1],i=this.s.v[0]/2,n=this.s.v[1]/2,s=3!==this.d,a=this.v;a.v[0][0]=e,a.v[0][1]=r-n,a.v[1][0]=s?e+i:e-i,a.v[1][1]=r,a.v[2][0]=e,a.v[2][1]=r+n,a.v[3][0]=s?e-i:e+i,a.v[3][1]=r,a.i[0][0]=s?e-i*t:e+i*t,a.i[0][1]=r-n,a.i[1][0]=s?e+i:e-i,a.i[1][1]=r-n*t,a.i[2][0]=s?e+i*t:e-i*t,a.i[2][1]=r+n,a.i[3][0]=s?e-i:e+i,a.i[3][1]=r+n*t,a.o[0][0]=s?e+i*t:e-i*t,a.o[0][1]=r-n,a.o[1][0]=s?e+i:e-i,a.o[1][1]=r+n*t,a.o[2][0]=s?e-i*t:e+i*t,a.o[2][1]=r+n,a.o[3][0]=s?e-i:e+i,a.o[3][1]=r-n*t}},extendPrototype([DynamicPropertyContainer],e),e}(),l=function(){function t(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=PropertyFactory.getProp(t,e.ir,0,0,this),this.is=PropertyFactory.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(t,e.pt,0,0,this),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,degToRads,this),this.or=PropertyFactory.getProp(t,e.or,0,0,this),this.os=PropertyFactory.getProp(t,e.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return t.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var t,e,r,i,n=2*Math.floor(this.pt.v),s=2*Math.PI/n,a=!0,o=this.or.v,h=this.ir.v,l=this.os.v,p=this.is.v,c=2*Math.PI*o/(2*n),f=2*Math.PI*h/(2*n),u=-Math.PI/2;u+=this.r.v;var d=3===this.data.d?-1:1;for(this.v._length=0,t=0;t<n;t+=1){r=a?l:p,i=a?c:f;var m=(e=a?o:h)*Math.cos(u),y=e*Math.sin(u),g=0===m&&0===y?0:y/Math.sqrt(m*m+y*y),v=0===m&&0===y?0:-m/Math.sqrt(m*m+y*y);m+=+this.p.v[0],y+=+this.p.v[1],this.v.setTripleAt(m,y,m-g*i*r*d,y-v*i*r*d,m+g*i*r*d,y+v*i*r*d,t,!0),a=!a,u+=s*d}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),r=2*Math.PI/e,i=this.or.v,n=this.os.v,s=2*Math.PI*i/(4*e),a=.5*-Math.PI,o=3===this.data.d?-1:1;for(a+=this.r.v,this.v._length=0,t=0;t<e;t+=1){var h=i*Math.cos(a),l=i*Math.sin(a),p=0===h&&0===l?0:l/Math.sqrt(h*h+l*l),c=0===h&&0===l?0:-h/Math.sqrt(h*h+l*l);h+=+this.p.v[0],l+=+this.p.v[1],this.v.setTripleAt(h,l,h-p*s*n*o,l-c*s*n*o,h+p*s*n*o,l+c*s*n*o,t,!0),a+=r*o}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],t),t}(),p=function(){function t(t,e){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return t.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],r=this.s.v[0]/2,i=this.s.v[1]/2,n=bmMin(r,i,this.r.v),s=n*(1-roundCorner);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+r,e-i+n,t+r,e-i+n,t+r,e-i+s,0,!0),this.v.setTripleAt(t+r,e+i-n,t+r,e+i-s,t+r,e+i-n,1,!0),0!==n?(this.v.setTripleAt(t+r-n,e+i,t+r-n,e+i,t+r-s,e+i,2,!0),this.v.setTripleAt(t-r+n,e+i,t-r+s,e+i,t-r+n,e+i,3,!0),this.v.setTripleAt(t-r,e+i-n,t-r,e+i-n,t-r,e+i-s,4,!0),this.v.setTripleAt(t-r,e-i+n,t-r,e-i+s,t-r,e-i+n,5,!0),this.v.setTripleAt(t-r+n,e-i,t-r+n,e-i,t-r+s,e-i,6,!0),this.v.setTripleAt(t+r-n,e-i,t+r-s,e-i,t+r-n,e-i,7,!0)):(this.v.setTripleAt(t-r,e+i,t-r+s,e+i,t-r,e+i,2),this.v.setTripleAt(t-r,e-i,t-r,e-i+s,t-r,e-i,3))):(this.v.setTripleAt(t+r,e-i+n,t+r,e-i+s,t+r,e-i+n,0,!0),0!==n?(this.v.setTripleAt(t+r-n,e-i,t+r-n,e-i,t+r-s,e-i,1,!0),this.v.setTripleAt(t-r+n,e-i,t-r+s,e-i,t-r+n,e-i,2,!0),this.v.setTripleAt(t-r,e-i+n,t-r,e-i+n,t-r,e-i+s,3,!0),this.v.setTripleAt(t-r,e+i-n,t-r,e+i-s,t-r,e+i-n,4,!0),this.v.setTripleAt(t-r+n,e+i,t-r+n,e+i,t-r+s,e+i,5,!0),this.v.setTripleAt(t+r-n,e+i,t+r-s,e+i,t+r-n,e+i,6,!0),this.v.setTripleAt(t+r,e+i-n,t+r,e+i-n,t+r,e+i-s,7,!0)):(this.v.setTripleAt(t-r,e-i,t-r+s,e-i,t-r,e-i,1,!0),this.v.setTripleAt(t-r,e+i,t-r,e+i-s,t-r,e+i,2,!0),this.v.setTripleAt(t+r,e+i,t+r-s,e+i,t+r,e+i,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:r},extendPrototype([DynamicPropertyContainer],t),t}();var c={getShapeProp:function(t,e,r){var i;return 3===r||4===r?i=(3===r?e.pt:e.ks).k.length?new o(t,e,r):new s(t,e,r):5===r?i=new p(t,e):6===r?i=new h(t,e):7===r&&(i=new l(t,e)),i.k&&t.addDynamicProperty(i),i},getConstructorFunction:function(){return s},getKeyframedConstructorFunction:function(){return o}};return c}(),Matrix=function(){var t=Math.cos,e=Math.sin,r=Math.tan,i=Math.round;function n(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function s(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(i,-n,0,0,n,i,0,0,0,0,1,0,0,0,0,1)}function a(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(1,0,0,0,0,i,-n,0,0,n,i,0,0,0,0,1)}function o(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(i,0,n,0,0,1,0,0,-n,0,i,0,0,0,0,1)}function h(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(i,-n,0,0,n,i,0,0,0,0,1,0,0,0,0,1)}function l(t,e){return this._t(1,e,t,1,0,0)}function p(t,e){return this.shear(r(t),r(e))}function c(i,n){var s=t(n),a=e(n);return this._t(s,a,0,0,-a,s,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,r(i),1,0,0,0,0,1,0,0,0,0,1)._t(s,-a,0,0,a,s,0,0,0,0,1,0,0,0,0,1)}function f(t,e,r){return r||0===r||(r=1),1===t&&1===e&&1===r?this:this._t(t,0,0,0,0,e,0,0,0,0,r,0,0,0,0,1)}function u(t,e,r,i,n,s,a,o,h,l,p,c,f,u,d,m){return this.props[0]=t,this.props[1]=e,this.props[2]=r,this.props[3]=i,this.props[4]=n,this.props[5]=s,this.props[6]=a,this.props[7]=o,this.props[8]=h,this.props[9]=l,this.props[10]=p,this.props[11]=c,this.props[12]=f,this.props[13]=u,this.props[14]=d,this.props[15]=m,this}function d(t,e,r){return r=r||0,0!==t||0!==e||0!==r?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,r,1):this}function m(t,e,r,i,n,s,a,o,h,l,p,c,f,u,d,m){var y=this.props;if(1===t&&0===e&&0===r&&0===i&&0===n&&1===s&&0===a&&0===o&&0===h&&0===l&&1===p&&0===c)return y[12]=y[12]*t+y[15]*f,y[13]=y[13]*s+y[15]*u,y[14]=y[14]*p+y[15]*d,y[15]*=m,this._identityCalculated=!1,this;var g=y[0],v=y[1],_=y[2],b=y[3],P=y[4],S=y[5],w=y[6],k=y[7],x=y[8],E=y[9],A=y[10],C=y[11],T=y[12],D=y[13],I=y[14],F=y[15];return y[0]=g*t+v*n+_*h+b*f,y[1]=g*e+v*s+_*l+b*u,y[2]=g*r+v*a+_*p+b*d,y[3]=g*i+v*o+_*c+b*m,y[4]=P*t+S*n+w*h+k*f,y[5]=P*e+S*s+w*l+k*u,y[6]=P*r+S*a+w*p+k*d,y[7]=P*i+S*o+w*c+k*m,y[8]=x*t+E*n+A*h+C*f,y[9]=x*e+E*s+A*l+C*u,y[10]=x*r+E*a+A*p+C*d,y[11]=x*i+E*o+A*c+C*m,y[12]=T*t+D*n+I*h+F*f,y[13]=T*e+D*s+I*l+F*u,y[14]=T*r+D*a+I*p+F*d,y[15]=T*i+D*o+I*c+F*m,this._identityCalculated=!1,this}function y(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function g(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function v(t){var e;for(e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function _(t){var e;for(e=0;e<16;e+=1)this.props[e]=t[e]}function b(t,e,r){return{x:t*this.props[0]+e*this.props[4]+r*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+r*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+r*this.props[10]+this.props[14]}}function P(t,e,r){return t*this.props[0]+e*this.props[4]+r*this.props[8]+this.props[12]}function S(t,e,r){return t*this.props[1]+e*this.props[5]+r*this.props[9]+this.props[13]}function w(t,e,r){return t*this.props[2]+e*this.props[6]+r*this.props[10]+this.props[14]}function k(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,r=-this.props[1]/t,i=-this.props[4]/t,n=this.props[0]/t,s=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,a=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,o=new Matrix;return o.props[0]=e,o.props[1]=r,o.props[4]=i,o.props[5]=n,o.props[12]=s,o.props[13]=a,o}function x(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function E(t){var e,r=t.length,i=[];for(e=0;e<r;e+=1)i[e]=x(t[e]);return i}function A(t,e,r){var i=createTypedArray("float32",6);if(this.isIdentity())i[0]=t[0],i[1]=t[1],i[2]=e[0],i[3]=e[1],i[4]=r[0],i[5]=r[1];else{var n=this.props[0],s=this.props[1],a=this.props[4],o=this.props[5],h=this.props[12],l=this.props[13];i[0]=t[0]*n+t[1]*a+h,i[1]=t[0]*s+t[1]*o+l,i[2]=e[0]*n+e[1]*a+h,i[3]=e[0]*s+e[1]*o+l,i[4]=r[0]*n+r[1]*a+h,i[5]=r[0]*s+r[1]*o+l}return i}function C(t,e,r){return this.isIdentity()?[t,e,r]:[t*this.props[0]+e*this.props[4]+r*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+r*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+r*this.props[10]+this.props[14]]}function T(t,e){if(this.isIdentity())return t+","+e;var r=this.props;return Math.round(100*(t*r[0]+e*r[4]+r[12]))/100+","+Math.round(100*(t*r[1]+e*r[5]+r[13]))/100}function D(){for(var t=0,e=this.props,r="matrix3d(";t<16;)r+=i(1e4*e[t])/1e4,r+=15===t?")":",",t+=1;return r}function I(t){return t<1e-6&&t>0||t>-1e-6&&t<0?i(1e4*t)/1e4:t}function F(){var t=this.props;return"matrix("+I(t[0])+","+I(t[1])+","+I(t[4])+","+I(t[5])+","+I(t[12])+","+I(t[13])+")"}return function(){this.reset=n,this.rotate=s,this.rotateX=a,this.rotateY=o,this.rotateZ=h,this.skew=p,this.skewFromAxis=c,this.shear=l,this.scale=f,this.setTransform=u,this.translate=d,this.transform=m,this.applyToPoint=b,this.applyToX=P,this.applyToY=S,this.applyToZ=w,this.applyToPointArray=C,this.applyToTriplePoints=A,this.applyToPointStringified=T,this.toCSS=D,this.to2dCSS=F,this.clone=v,this.cloneFromProps=_,this.equals=g,this.inversePoints=E,this.inversePoint=x,this.getInverseMatrix=k,this._t=this.transform,this.isIdentity=y,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}}();function _typeof$3(t){return(_typeof$3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lottie={},standalone="__[STANDALONE]__",animationData="__[ANIMATIONDATA]__",renderer="";function setLocation(t){setLocationHref(t)}function searchAnimations(){!0===standalone?animationManager.searchAnimations(animationData,standalone,renderer):animationManager.searchAnimations()}function setSubframeRendering(t){setSubframeEnabled(t)}function setPrefix(t){setIdPrefix(t)}function loadAnimation(t){return!0===standalone&&(t.animationData=JSON.parse(animationData)),animationManager.loadAnimation(t)}function setQuality(t){if("string"==typeof t)switch(t){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10)}else!isNaN(t)&&t>1&&setDefaultCurveSegments(t);getDefaultCurveSegments()>=50?roundValues(!1):roundValues(!0)}function inBrowser(){return"undefined"!=typeof navigator}function installPlugin(t,e){"expressions"===t&&setExpressionsPlugin(e)}function getFactory(t){switch(t){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}function checkReady(){"complete"===document.readyState&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(t){for(var e=queryString.split("&"),r=0;r<e.length;r+=1){var i=e[r].split("=");if(decodeURIComponent(i[0])==t)return decodeURIComponent(i[1])}return null}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.9.6";var queryString="";if(standalone){var scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""};queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",renderer=getQueryVariable("renderer")}var readyStateCheckInterval=setInterval(checkReady,100);try{"object"!==_typeof$3(exports)&&(window.bodymovin=lottie)}catch(t){}var ShapeModifiers=function(){var t={},e={};return t.registerModifier=function(t,r){e[t]||(e[t]=r)},t.getModifier=function(t,r,i){return new e[t](r,i)},t}();function ShapeModifier(){}function TrimModifier(){}function PuckerAndBloatModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:shapeCollectionPool.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},ShapeModifier.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier),extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(t,e){this.s=PropertyFactory.getProp(t,e.s,0,.01,this),this.e=PropertyFactory.getProp(t,e.e,0,.01,this),this.o=PropertyFactory.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(t){t.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(t,e,r,i,n){var s=[];e<=1?s.push({s:t,e:e}):t>=1?s.push({s:t-1,e:e-1}):(s.push({s:t,e:1}),s.push({s:0,e:e-1}));var a,o,h=[],l=s.length;for(a=0;a<l;a+=1){var p,c;if(!((o=s[a]).e*n<i||o.s*n>i+r))p=o.s*n<=i?0:(o.s*n-i)/r,c=o.e*n>=i+r?1:(o.e*n-i)/r,h.push([p,c])}return h.length||h.push([0,0]),h},TrimModifier.prototype.releasePathsData=function(t){var e,r=t.length;for(e=0;e<r;e+=1)segmentsLengthPool.release(t[e]);return t.length=0,t},TrimModifier.prototype.processShapes=function(t){var e,r,i,n;if(this._mdf||t){var s=this.o.v%360/360;if(s<0&&(s+=1),(e=this.s.v>1?1+s:this.s.v<0?0+s:this.s.v+s)>(r=this.e.v>1?1+s:this.e.v<0?0+s:this.e.v+s)){var a=e;e=r,r=a}e=1e-4*Math.round(1e4*e),r=1e-4*Math.round(1e4*r),this.sValue=e,this.eValue=r}else e=this.sValue,r=this.eValue;var o,h,l,p,c,f=this.shapes.length,u=0;if(r===e)for(n=0;n<f;n+=1)this.shapes[n].localShapeCollection.releaseShapes(),this.shapes[n].shape._mdf=!0,this.shapes[n].shape.paths=this.shapes[n].localShapeCollection,this._mdf&&(this.shapes[n].pathsData.length=0);else if(1===r&&0===e||0===r&&1===e){if(this._mdf)for(n=0;n<f;n+=1)this.shapes[n].pathsData.length=0,this.shapes[n].shape._mdf=!0}else{var d,m,y=[];for(n=0;n<f;n+=1)if((d=this.shapes[n]).shape._mdf||this._mdf||t||2===this.m){if(h=(i=d.shape.paths)._length,c=0,!d.shape._mdf&&d.pathsData.length)c=d.totalShapeLength;else{for(l=this.releasePathsData(d.pathsData),o=0;o<h;o+=1)p=bez.getSegmentsLength(i.shapes[o]),l.push(p),c+=p.totalLength;d.totalShapeLength=c,d.pathsData=l}u+=c,d.shape._mdf=!0}else d.shape.paths=d.localShapeCollection;var g,v=e,_=r,b=0;for(n=f-1;n>=0;n-=1)if((d=this.shapes[n]).shape._mdf){for((m=d.localShapeCollection).releaseShapes(),2===this.m&&f>1?(g=this.calculateShapeEdges(e,r,d.totalShapeLength,b,u),b+=d.totalShapeLength):g=[[v,_]],h=g.length,o=0;o<h;o+=1){v=g[o][0],_=g[o][1],y.length=0,_<=1?y.push({s:d.totalShapeLength*v,e:d.totalShapeLength*_}):v>=1?y.push({s:d.totalShapeLength*(v-1),e:d.totalShapeLength*(_-1)}):(y.push({s:d.totalShapeLength*v,e:d.totalShapeLength}),y.push({s:0,e:d.totalShapeLength*(_-1)}));var P=this.addShapes(d,y[0]);if(y[0].s!==y[0].e){if(y.length>1)if(d.shape.paths.shapes[d.shape.paths._length-1].c){var S=P.pop();this.addPaths(P,m),P=this.addShapes(d,y[1],S)}else this.addPaths(P,m),P=this.addShapes(d,y[1]);this.addPaths(P,m)}}d.shape.paths=m}}},TrimModifier.prototype.addPaths=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)e.addShape(t[r])},TrimModifier.prototype.addSegment=function(t,e,r,i,n,s,a){n.setXYAt(e[0],e[1],"o",s),n.setXYAt(r[0],r[1],"i",s+1),a&&n.setXYAt(t[0],t[1],"v",s),n.setXYAt(i[0],i[1],"v",s+1)},TrimModifier.prototype.addSegmentFromArray=function(t,e,r,i){e.setXYAt(t[1],t[5],"o",r),e.setXYAt(t[2],t[6],"i",r+1),i&&e.setXYAt(t[0],t[4],"v",r),e.setXYAt(t[3],t[7],"v",r+1)},TrimModifier.prototype.addShapes=function(t,e,r){var i,n,s,a,o,h,l,p,c=t.pathsData,f=t.shape.paths.shapes,u=t.shape.paths._length,d=0,m=[],y=!0;for(r?(o=r._length,p=r._length):(r=shapePool.newElement(),o=0,p=0),m.push(r),i=0;i<u;i+=1){for(h=c[i].lengths,r.c=f[i].c,s=f[i].c?h.length:h.length+1,n=1;n<s;n+=1)if(d+(a=h[n-1]).addedLength<e.s)d+=a.addedLength,r.c=!1;else{if(d>e.e){r.c=!1;break}e.s<=d&&e.e>=d+a.addedLength?(this.addSegment(f[i].v[n-1],f[i].o[n-1],f[i].i[n],f[i].v[n],r,o,y),y=!1):(l=bez.getNewSegment(f[i].v[n-1],f[i].v[n],f[i].o[n-1],f[i].i[n],(e.s-d)/a.addedLength,(e.e-d)/a.addedLength,h[n-1]),this.addSegmentFromArray(l,r,o,y),y=!1,r.c=!1),d+=a.addedLength,o+=1}if(f[i].c&&h.length){if(a=h[n-1],d<=e.e){var g=h[n-1].addedLength;e.s<=d&&e.e>=d+g?(this.addSegment(f[i].v[n-1],f[i].o[n-1],f[i].i[0],f[i].v[0],r,o,y),y=!1):(l=bez.getNewSegment(f[i].v[n-1],f[i].v[0],f[i].o[n-1],f[i].i[0],(e.s-d)/g,(e.e-d)/g,h[n-1]),this.addSegmentFromArray(l,r,o,y),y=!1,r.c=!1)}else r.c=!1;d+=a.addedLength,o+=1}if(r._length&&(r.setXYAt(r.v[p][0],r.v[p][1],"i",p),r.setXYAt(r.v[r._length-1][0],r.v[r._length-1][1],"o",r._length-1)),d>e.e)break;i<u-1&&(r=shapePool.newElement(),y=!0,m.push(r),o=0)}return m},extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(t,e){var r=e/100,i=[0,0],n=t._length,s=0;for(s=0;s<n;s+=1)i[0]+=t.v[s][0],i[1]+=t.v[s][1];i[0]/=n,i[1]/=n;var a,o,h,l,p,c,f=shapePool.newElement();for(f.c=t.c,s=0;s<n;s+=1)a=t.v[s][0]+(i[0]-t.v[s][0])*r,o=t.v[s][1]+(i[1]-t.v[s][1])*r,h=t.o[s][0]+(i[0]-t.o[s][0])*-r,l=t.o[s][1]+(i[1]-t.o[s][1])*-r,p=t.i[s][0]+(i[0]-t.i[s][0])*-r,c=t.i[s][1]+(i[1]-t.i[s][1])*-r,f.setTripleAt(a,o,h,l,p,c,s);return f},PuckerAndBloatModifier.prototype.processShapes=function(t){var e,r,i,n,s,a,o=this.shapes.length,h=this.amount.v;if(0!==h)for(r=0;r<o;r+=1){if(a=(s=this.shapes[r]).localShapeCollection,s.shape._mdf||this._mdf||t)for(a.releaseShapes(),s.shape._mdf=!0,e=s.shape.paths.shapes,n=s.shape.paths._length,i=0;i<n;i+=1)a.addShape(this.processPath(e[i],h));s.shape.paths=s.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=function(){var t=[0,0];function e(t,e,r){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(r||t),e.p&&e.p.s?(this.px=PropertyFactory.getProp(t,e.p.x,0,0,this),this.py=PropertyFactory.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=PropertyFactory.getProp(t,e.p.z,0,0,this))):this.p=PropertyFactory.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=PropertyFactory.getProp(t,e.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(t,e.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(t,e.rz,0,degToRads,this),e.or.k[0].ti){var i,n=e.or.k.length;for(i=0;i<n;i+=1)e.or.k[i].to=null,e.or.k[i].ti=null}this.or=PropertyFactory.getProp(t,e.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(t,e.r||{k:0},0,degToRads,this);e.sk&&(this.sk=PropertyFactory.getProp(t,e.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(t,e.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=PropertyFactory.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return e.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(e){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||e){var r;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var i,n;if(r=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(i=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/r,0),n=this.p.getValueAtTime(this.p.keyframes[0].t/r,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(i=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/r,0),n=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/r,0)):(i=this.p.pv,n=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/r,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){i=[],n=[];var s=this.px,a=this.py;s._caching.lastFrame+s.offsetTime<=s.keyframes[0].t?(i[0]=s.getValueAtTime((s.keyframes[0].t+.01)/r,0),i[1]=a.getValueAtTime((a.keyframes[0].t+.01)/r,0),n[0]=s.getValueAtTime(s.keyframes[0].t/r,0),n[1]=a.getValueAtTime(a.keyframes[0].t/r,0)):s._caching.lastFrame+s.offsetTime>=s.keyframes[s.keyframes.length-1].t?(i[0]=s.getValueAtTime(s.keyframes[s.keyframes.length-1].t/r,0),i[1]=a.getValueAtTime(a.keyframes[a.keyframes.length-1].t/r,0),n[0]=s.getValueAtTime((s.keyframes[s.keyframes.length-1].t-.01)/r,0),n[1]=a.getValueAtTime((a.keyframes[a.keyframes.length-1].t-.01)/r,0)):(i=[s.pv,a.pv],n[0]=s.getValueAtTime((s._caching.lastFrame+s.offsetTime-.01)/r,s.offsetTime),n[1]=a.getValueAtTime((a._caching.lastFrame+a.offsetTime-.01)/r,a.offsetTime))}else i=n=t;this.v.rotate(-Math.atan2(i[1]-n[1],i[0]-n[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}},precalculateMatrix:function(){if(!this.a.k&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},extendPrototype([DynamicPropertyContainer],e),e.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},e.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty,{getTransformProperty:function(t,r,i){return new e(t,r,i)}}}();function RepeaterModifier(){}function RoundCornersModifier(){}function getFontProperties(t){for(var e=t.fStyle?t.fStyle.split(" "):[],r="normal",i="normal",n=e.length,s=0;s<n;s+=1)switch(e[s].toLowerCase()){case"italic":i="italic";break;case"bold":r="700";break;case"black":r="900";break;case"medium":r="500";break;case"regular":case"normal":r="400";break;case"light":case"thin":r="200"}return{style:i,weight:t.fWeight||r}}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(t,e.c,0,null,this),this.o=PropertyFactory.getProp(t,e.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(t,e.tr,this),this.so=PropertyFactory.getProp(t,e.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(t,e,r,i,n,s){var a=s?-1:1,o=i.s.v[0]+(1-i.s.v[0])*(1-n),h=i.s.v[1]+(1-i.s.v[1])*(1-n);t.translate(i.p.v[0]*a*n,i.p.v[1]*a*n,i.p.v[2]),e.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),e.rotate(-i.r.v*a*n),e.translate(i.a.v[0],i.a.v[1],i.a.v[2]),r.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),r.scale(s?1/o:o,s?1/h:h),r.translate(i.a.v[0],i.a.v[1],i.a.v[2])},RepeaterModifier.prototype.init=function(t,e,r,i){for(this.elem=t,this.arr=e,this.pos=r,this.elemsData=i,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[r]);r>0;)r-=1,this._elements.unshift(e[r]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e]._processed=!1,"gr"===t[e].ty&&this.resetElements(t[e].it)},RepeaterModifier.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},RepeaterModifier.prototype.changeGroupRender=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)t[r]._render=e,"gr"===t[r].ty&&this.changeGroupRender(t[r].it,e)},RepeaterModifier.prototype.processShapes=function(t){var e,r,i,n,s,a=!1;if(this._mdf||t){var o,h=Math.ceil(this.c.v);if(this._groups.length<h){for(;this._groups.length<h;){var l={it:this.cloneElements(this._elements),ty:"gr"};l.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,l),this._groups.splice(0,0,l),this._currentCopies+=1}this.elem.reloadShapes(),a=!0}for(s=0,i=0;i<=this._groups.length-1;i+=1){if(o=s<h,this._groups[i]._render=o,this.changeGroupRender(this._groups[i].it,o),!o){var p=this.elemsData[i].it,c=p[p.length-1];0!==c.transform.op.v?(c.transform.op._mdf=!0,c.transform.op.v=0):c.transform.op._mdf=!1}s+=1}this._currentCopies=h;var f=this.o.v,u=f%1,d=f>0?Math.floor(f):Math.ceil(f),m=this.pMatrix.props,y=this.rMatrix.props,g=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var v,_,b=0;if(f>0){for(;b<d;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),b+=1;u&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,u,!1),b+=u)}else if(f<0){for(;b>d;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),b-=1;u&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-u,!0),b-=u)}for(i=1===this.data.m?0:this._currentCopies-1,n=1===this.data.m?1:-1,s=this._currentCopies;s;){if(_=(r=(e=this.elemsData[i].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(i/(this._currentCopies-1)),0!==b){for((0!==i&&1===n||i!==this._currentCopies-1&&-1===n)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(y[0],y[1],y[2],y[3],y[4],y[5],y[6],y[7],y[8],y[9],y[10],y[11],y[12],y[13],y[14],y[15]),this.matrix.transform(g[0],g[1],g[2],g[3],g[4],g[5],g[6],g[7],g[8],g[9],g[10],g[11],g[12],g[13],g[14],g[15]),this.matrix.transform(m[0],m[1],m[2],m[3],m[4],m[5],m[6],m[7],m[8],m[9],m[10],m[11],m[12],m[13],m[14],m[15]),v=0;v<_;v+=1)r[v]=this.matrix.props[v];this.matrix.reset()}else for(this.matrix.reset(),v=0;v<_;v+=1)r[v]=this.matrix.props[v];b+=1,s-=1,i+=n}}else for(s=this._currentCopies,i=0,n=1;s;)r=(e=this.elemsData[i].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,s-=1,i+=n;return a},RepeaterModifier.prototype.addShape=function(){},extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(t,e){var r,i=shapePool.newElement();i.c=t.c;var n,s,a,o,h,l,p,c,f,u,d,m,y=t._length,g=0;for(r=0;r<y;r+=1)n=t.v[r],a=t.o[r],s=t.i[r],n[0]===a[0]&&n[1]===a[1]&&n[0]===s[0]&&n[1]===s[1]?0!==r&&r!==y-1||t.c?(o=0===r?t.v[y-1]:t.v[r-1],l=(h=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=d=n[0]+(o[0]-n[0])*l,c=m=n[1]-(n[1]-o[1])*l,f=p-(p-n[0])*roundCorner,u=c-(c-n[1])*roundCorner,i.setTripleAt(p,c,f,u,d,m,g),g+=1,o=r===y-1?t.v[0]:t.v[r+1],l=(h=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=f=n[0]+(o[0]-n[0])*l,c=u=n[1]+(o[1]-n[1])*l,d=p-(p-n[0])*roundCorner,m=c-(c-n[1])*roundCorner,i.setTripleAt(p,c,f,u,d,m,g),g+=1):(i.setTripleAt(n[0],n[1],a[0],a[1],s[0],s[1],g),g+=1):(i.setTripleAt(t.v[r][0],t.v[r][1],t.o[r][0],t.o[r][1],t.i[r][0],t.i[r][1],g),g+=1);return i},RoundCornersModifier.prototype.processShapes=function(t){var e,r,i,n,s,a,o=this.shapes.length,h=this.rd.v;if(0!==h)for(r=0;r<o;r+=1){if(a=(s=this.shapes[r]).localShapeCollection,s.shape._mdf||this._mdf||t)for(a.releaseShapes(),s.shape._mdf=!0,e=s.shape.paths.shapes,n=s.shape.paths._length,i=0;i<n;i+=1)a.addShape(this.processPath(e[i],h));s.shape.paths=s.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var FontManager=function(){var t={w:0,size:0,shapes:[],data:{shapes:[]}},e=[];e=e.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var r=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"],i=[65039,8205];function n(t,e){var r=createTag("span");r.setAttribute("aria-hidden",!0),r.style.fontFamily=e;var i=createTag("span");i.innerText="giItT1WQy@!-/#",r.style.position="absolute",r.style.left="-10000px",r.style.top="-10000px",r.style.fontSize="300px",r.style.fontVariant="normal",r.style.fontStyle="normal",r.style.fontWeight="normal",r.style.letterSpacing="0",r.appendChild(i),document.body.appendChild(r);var n=i.offsetWidth;return i.style.fontFamily=function(t){var e,r=t.split(","),i=r.length,n=[];for(e=0;e<i;e+=1)"sans-serif"!==r[e]&&"monospace"!==r[e]&&n.push(r[e]);return n.join(",")}(t)+", "+e,{node:i,w:n,parent:r}}function s(t,e){var r,i=document.body&&e?"svg":"canvas",n=getFontProperties(t);if("svg"===i){var s=createNS("text");s.style.fontSize="100px",s.setAttribute("font-family",t.fFamily),s.setAttribute("font-style",n.style),s.setAttribute("font-weight",n.weight),s.textContent="1",t.fClass?(s.style.fontFamily="inherit",s.setAttribute("class",t.fClass)):s.style.fontFamily=t.fFamily,e.appendChild(s),r=s}else{var a=new OffscreenCanvas(500,500).getContext("2d");a.font=n.style+" "+n.weight+" 100px "+t.fFamily,r=a}return{measureText:function(t){return"svg"===i?(r.textContent=t,r.getComputedTextLength()):r.measureText(t).width}}}var a=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};return a.isModifier=function(t,e){var i=t.toString(16)+e.toString(16);return-1!==r.indexOf(i)},a.isZeroWidthJoiner=function(t,e){return e?t===i[0]&&e===i[1]:t===i[1]},a.isCombinedCharacter=function(t){return-1!==e.indexOf(t)},a.prototype={addChars:function(t){if(t){var e;this.chars||(this.chars=[]);var r,i,n=t.length,s=this.chars.length;for(e=0;e<n;e+=1){for(r=0,i=!1;r<s;)this.chars[r].style===t[e].style&&this.chars[r].fFamily===t[e].fFamily&&this.chars[r].ch===t[e].ch&&(i=!0),r+=1;i||(this.chars.push(t[e]),s+=1)}}},addFonts:function(t,e){if(t){if(this.chars)return this.isLoaded=!0,void(this.fonts=t.list);if(!document.body)return this.isLoaded=!0,t.list.forEach((function(t){t.helper=s(t),t.cache={}})),void(this.fonts=t.list);var r,i=t.list,a=i.length,o=a;for(r=0;r<a;r+=1){var h,l,p=!0;if(i[r].loaded=!1,i[r].monoCase=n(i[r].fFamily,"monospace"),i[r].sansCase=n(i[r].fFamily,"sans-serif"),i[r].fPath){if("p"===i[r].fOrigin||3===i[r].origin){if((h=document.querySelectorAll('style[f-forigin="p"][f-family="'+i[r].fFamily+'"], style[f-origin="3"][f-family="'+i[r].fFamily+'"]')).length>0&&(p=!1),p){var c=createTag("style");c.setAttribute("f-forigin",i[r].fOrigin),c.setAttribute("f-origin",i[r].origin),c.setAttribute("f-family",i[r].fFamily),c.type="text/css",c.innerText="@font-face {font-family: "+i[r].fFamily+"; font-style: normal; src: url('"+i[r].fPath+"');}",e.appendChild(c)}}else if("g"===i[r].fOrigin||1===i[r].origin){for(h=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),l=0;l<h.length;l+=1)-1!==h[l].href.indexOf(i[r].fPath)&&(p=!1);if(p){var f=createTag("link");f.setAttribute("f-forigin",i[r].fOrigin),f.setAttribute("f-origin",i[r].origin),f.type="text/css",f.rel="stylesheet",f.href=i[r].fPath,document.body.appendChild(f)}}else if("t"===i[r].fOrigin||2===i[r].origin){for(h=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),l=0;l<h.length;l+=1)i[r].fPath===h[l].src&&(p=!1);if(p){var u=createTag("link");u.setAttribute("f-forigin",i[r].fOrigin),u.setAttribute("f-origin",i[r].origin),u.setAttribute("rel","stylesheet"),u.setAttribute("href",i[r].fPath),e.appendChild(u)}}}else i[r].loaded=!0,o-=1;i[r].helper=s(i[r],e),i[r].cache={},this.fonts.push(i[r])}0===o?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0},getCharData:function(e,r,i){for(var n=0,s=this.chars.length;n<s;){if(this.chars[n].ch===e&&this.chars[n].style===r&&this.chars[n].fFamily===i)return this.chars[n];n+=1}return("string"==typeof e&&13!==e.charCodeAt(0)||!e)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",e,r,i)),t},getFontByName:function(t){for(var e=0,r=this.fonts.length;e<r;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,r){var i=this.getFontByName(e),n=t.charCodeAt(0);if(!i.cache[n+1]){var s=i.helper;if(" "===t){var a=s.measureText("|"+t+"|"),o=s.measureText("||");i.cache[n+1]=(a-o)/100}else i.cache[n+1]=s.measureText(t)/100}return i.cache[n+1]*r},checkLoadedFonts:function(){var t,e,r,i=this.fonts.length,n=i;for(t=0;t<i;t+=1)this.fonts[t].loaded?n-=1:"n"===this.fonts[t].fOrigin||0===this.fonts[t].origin?this.fonts[t].loaded=!0:(e=this.fonts[t].monoCase.node,r=this.fonts[t].monoCase.w,e.offsetWidth!==r?(n-=1,this.fonts[t].loaded=!0):(e=this.fonts[t].sansCase.node,r=this.fonts[t].sansCase.w,e.offsetWidth!==r&&(n-=1,this.fonts[t].loaded=!0)),this.fonts[t].loaded&&(this.fonts[t].sansCase.parent.parentNode.removeChild(this.fonts[t].sansCase.parent),this.fonts[t].monoCase.parent.parentNode.removeChild(this.fonts[t].monoCase.parent)));0!==n&&Date.now()-this.initTime<5e3?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}},a}();function RenderableElement(){}RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,e=this.renderableComponents.length;for(t=0;t<e;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var MaskManagerInterface=function(){function t(t,e){this._mask=t,this._data=e}Object.defineProperty(t.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(t.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),100*this._mask.op.v}});return function(e){var r,i=createSizedArray(e.viewData.length),n=e.viewData.length;for(r=0;r<n;r+=1)i[r]=new t(e.viewData[r],e.masksProperties[r]);return function(t){for(r=0;r<n;){if(e.masksProperties[r].nm===t)return i[r];r+=1}return null}}}(),ExpressionPropertyInterface=function(){var t={pv:0,v:0,mult:1},e={pv:[0,0,0],v:[0,0,0],mult:1};function r(t,e,r){Object.defineProperty(t,"velocity",{get:function(){return e.getVelocityAtTime(e.comp.currentFrame)}}),t.numKeys=e.keyframes?e.keyframes.length:0,t.key=function(i){if(!t.numKeys)return 0;var n="";n="s"in e.keyframes[i-1]?e.keyframes[i-1].s:"e"in e.keyframes[i-2]?e.keyframes[i-2].e:e.keyframes[i-2].s;var s="unidimensional"===r?new Number(n):Object.assign({},n);return s.time=e.keyframes[i-1].t/e.elem.comp.globalData.frameRate,s.value="unidimensional"===r?n[0]:n,s},t.valueAtTime=e.getValueAtTime,t.speedAtTime=e.getSpeedAtTime,t.velocityAtTime=e.getVelocityAtTime,t.propertyGroup=e.propertyGroup}function i(){return t}return function(n){return n?"unidimensional"===n.propType?function(e){e&&"pv"in e||(e=t);var i=1/e.mult,n=e.pv*i,s=new Number(n);return s.value=n,r(s,e,"unidimensional"),function(){return e.k&&e.getValue(),n=e.v*i,s.value!==n&&((s=new Number(n)).value=n,r(s,e,"unidimensional")),s}}(n):function(t){t&&"pv"in t||(t=e);var i=1/t.mult,n=t.data&&t.data.l||t.pv.length,s=createTypedArray("float32",n),a=createTypedArray("float32",n);return s.value=a,r(s,t,"multidimensional"),function(){t.k&&t.getValue();for(var e=0;e<n;e+=1)a[e]=t.v[e]*i,s[e]=a[e];return s}}(n):i}}(),TransformExpressionInterface=function(t){function e(t){switch(t){case"scale":case"Scale":case"ADBE Scale":case 6:return e.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return e.rotation;case"ADBE Rotate X":return e.xRotation;case"ADBE Rotate Y":return e.yRotation;case"position":case"Position":case"ADBE Position":case 2:return e.position;case"ADBE Position_0":return e.xPosition;case"ADBE Position_1":return e.yPosition;case"ADBE Position_2":return e.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return e.anchorPoint;case"opacity":case"Opacity":case 11:return e.opacity;default:return null}}var r,i,n,s;return Object.defineProperty(e,"rotation",{get:ExpressionPropertyInterface(t.r||t.rz)}),Object.defineProperty(e,"zRotation",{get:ExpressionPropertyInterface(t.rz||t.r)}),Object.defineProperty(e,"xRotation",{get:ExpressionPropertyInterface(t.rx)}),Object.defineProperty(e,"yRotation",{get:ExpressionPropertyInterface(t.ry)}),Object.defineProperty(e,"scale",{get:ExpressionPropertyInterface(t.s)}),t.p?s=ExpressionPropertyInterface(t.p):(r=ExpressionPropertyInterface(t.px),i=ExpressionPropertyInterface(t.py),t.pz&&(n=ExpressionPropertyInterface(t.pz))),Object.defineProperty(e,"position",{get:function(){return t.p?s():[r(),i(),n?n():0]}}),Object.defineProperty(e,"xPosition",{get:ExpressionPropertyInterface(t.px)}),Object.defineProperty(e,"yPosition",{get:ExpressionPropertyInterface(t.py)}),Object.defineProperty(e,"zPosition",{get:ExpressionPropertyInterface(t.pz)}),Object.defineProperty(e,"anchorPoint",{get:ExpressionPropertyInterface(t.a)}),Object.defineProperty(e,"opacity",{get:ExpressionPropertyInterface(t.o)}),Object.defineProperty(e,"skew",{get:ExpressionPropertyInterface(t.sk)}),Object.defineProperty(e,"skewAxis",{get:ExpressionPropertyInterface(t.sa)}),Object.defineProperty(e,"orientation",{get:ExpressionPropertyInterface(t.or)}),e},LayerExpressionInterface=function(){function t(t){var e=new Matrix;void 0!==t?this._elem.finalTransform.mProp.getValueAtTime(t).clone(e):this._elem.finalTransform.mProp.applyToMatrix(e);return e}function e(t,e){var r=this.getMatrix(e);return r.props[12]=0,r.props[13]=0,r.props[14]=0,this.applyPoint(r,t)}function r(t,e){var r=this.getMatrix(e);return this.applyPoint(r,t)}function i(t,e){var r=this.getMatrix(e);return r.props[12]=0,r.props[13]=0,r.props[14]=0,this.invertPoint(r,t)}function n(t,e){var r=this.getMatrix(e);return this.invertPoint(r,t)}function s(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var r,i=this._elem.hierarchy.length;for(r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(t)}return t.applyToPointArray(e[0],e[1],e[2]||0)}function a(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var r,i=this._elem.hierarchy.length;for(r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(t)}return t.inversePoint(e)}function o(t){var e=new Matrix;if(e.reset(),this._elem.finalTransform.mProp.applyToMatrix(e),this._elem.hierarchy&&this._elem.hierarchy.length){var r,i=this._elem.hierarchy.length;for(r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(e);return e.inversePoint(t)}return e.inversePoint(t)}function h(){return[1,1,1,1]}return function(l){var p;function c(t){switch(t){case"ADBE Root Vectors Group":case"Contents":case 2:return c.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return p;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return c.effect;case"ADBE Text Properties":return c.textInterface;default:return null}}c.getMatrix=t,c.invertPoint=a,c.applyPoint=s,c.toWorld=r,c.toWorldVec=e,c.fromWorld=n,c.fromWorldVec=i,c.toComp=r,c.fromComp=o,c.sampleImage=h,c.sourceRectAtTime=l.sourceRectAtTime.bind(l),c._elem=l;var f=getDescriptor(p=TransformExpressionInterface(l.finalTransform.mProp),"anchorPoint");return Object.defineProperties(c,{hasParent:{get:function(){return l.hierarchy.length}},parent:{get:function(){return l.hierarchy[0].layerInterface}},rotation:getDescriptor(p,"rotation"),scale:getDescriptor(p,"scale"),position:getDescriptor(p,"position"),opacity:getDescriptor(p,"opacity"),anchorPoint:f,anchor_point:f,transform:{get:function(){return p}},active:{get:function(){return l.isInRange}}}),c.startTime=l.data.st,c.index=l.data.ind,c.source=l.data.refId,c.height=0===l.data.ty?l.data.h:100,c.width=0===l.data.ty?l.data.w:100,c.inPoint=l.data.ip/l.comp.globalData.frameRate,c.outPoint=l.data.op/l.comp.globalData.frameRate,c._name=l.data.nm,c.registerMaskInterface=function(t){c.mask=new MaskManagerInterface(t,l)},c.registerEffectsInterface=function(t){c.effect=t},c}}(),propertyGroupFactory=function(t,e){return function(r){return(r=void 0===r?1:r)<=0?t:e(r-1)}},PropertyInterface=function(t,e){var r={_name:t};return function(t){return(t=void 0===t?1:t)<=0?r:e(t-1)}},EffectsExpressionInterface=function(){function t(r,i,n,s){function a(t){for(var e=r.ef,i=0,n=e.length;i<n;){if(t===e[i].nm||t===e[i].mn||t===e[i].ix)return 5===e[i].ty?l[i]:l[i]();i+=1}throw new Error}var o,h=propertyGroupFactory(a,n),l=[],p=r.ef.length;for(o=0;o<p;o+=1)5===r.ef[o].ty?l.push(t(r.ef[o],i.effectElements[o],i.effectElements[o].propertyGroup,s)):l.push(e(i.effectElements[o],r.ef[o].ty,s,h));return"ADBE Color Control"===r.mn&&Object.defineProperty(a,"color",{get:function(){return l[0]()}}),Object.defineProperties(a,{numProperties:{get:function(){return r.np}},_name:{value:r.nm},propertyGroup:{value:h}}),a.enabled=0!==r.en,a.active=a.enabled,a}function e(t,e,r,i){var n=ExpressionPropertyInterface(t.p);return t.p.setGroupProperty&&t.p.setGroupProperty(PropertyInterface("",i)),function(){return 10===e?r.comp.compInterface(t.p.v):n()}}return{createEffectsInterface:function(e,r){if(e.effectsManager){var i,n=[],s=e.data.ef,a=e.effectsManager.effectElements.length;for(i=0;i<a;i+=1)n.push(t(s[i],e.effectsManager.effectElements[i],r,e));var o=e.data.ef||[],h=function(t){for(i=0,a=o.length;i<a;){if(t===o[i].nm||t===o[i].mn||t===o[i].ix)return n[i];i+=1}return null};return Object.defineProperty(h,"numProperties",{get:function(){return o.length}}),h}return null}}}(),CompExpressionInterface=function(t){function e(e){for(var r=0,i=t.layers.length;r<i;){if(t.layers[r].nm===e||t.layers[r].ind===e)return t.elements[r].layerInterface;r+=1}return null}return Object.defineProperty(e,"_name",{value:t.data.nm}),e.layer=e,e.pixelAspect=1,e.height=t.data.h||t.globalData.compSize.h,e.width=t.data.w||t.globalData.compSize.w,e.pixelAspect=1,e.frameDuration=1/t.globalData.frameRate,e.displayStartTime=0,e.numLayers=t.layers.length,e},ShapePathInterface=function(t,e,r){var i=e.sh;function n(t){return"Shape"===t||"shape"===t||"Path"===t||"path"===t||"ADBE Vector Shape"===t||2===t?n.path:null}var s=propertyGroupFactory(n,r);return i.setGroupProperty(PropertyInterface("Path",s)),Object.defineProperties(n,{path:{get:function(){return i.k&&i.getValue(),i}},shape:{get:function(){return i.k&&i.getValue(),i}},_name:{value:t.nm},ix:{value:t.ix},propertyIndex:{value:t.ix},mn:{value:t.mn},propertyGroup:{value:r}}),n},ShapeExpressionInterface=function(){function t(t,o,u){var d,m=[],y=t?t.length:0;for(d=0;d<y;d+=1)"gr"===t[d].ty?m.push(e(t[d],o[d],u)):"fl"===t[d].ty?m.push(r(t[d],o[d],u)):"st"===t[d].ty?m.push(s(t[d],o[d],u)):"tm"===t[d].ty?m.push(a(t[d],o[d],u)):"tr"===t[d].ty||("el"===t[d].ty?m.push(h(t[d],o[d],u)):"sr"===t[d].ty?m.push(l(t[d],o[d],u)):"sh"===t[d].ty?m.push(ShapePathInterface(t[d],o[d],u)):"rc"===t[d].ty?m.push(p(t[d],o[d],u)):"rd"===t[d].ty?m.push(c(t[d],o[d],u)):"rp"===t[d].ty?m.push(f(t[d],o[d],u)):"gf"===t[d].ty?m.push(i(t[d],o[d],u)):m.push(n(t[d],o[d])));return m}function e(e,r,i){var n=function(t){switch(t){case"ADBE Vectors Group":case"Contents":case 2:return n.content;default:return n.transform}};n.propertyGroup=propertyGroupFactory(n,i);var s=function(e,r,i){var n,s=function(t){for(var e=0,r=n.length;e<r;){if(n[e]._name===t||n[e].mn===t||n[e].propertyIndex===t||n[e].ix===t||n[e].ind===t)return n[e];e+=1}return"number"==typeof t?n[t-1]:null};s.propertyGroup=propertyGroupFactory(s,i),n=t(e.it,r.it,s.propertyGroup),s.numProperties=n.length;var a=o(e.it[e.it.length-1],r.it[r.it.length-1],s.propertyGroup);return s.transform=a,s.propertyIndex=e.cix,s._name=e.nm,s}(e,r,n.propertyGroup),a=o(e.it[e.it.length-1],r.it[r.it.length-1],n.propertyGroup);return n.content=s,n.transform=a,Object.defineProperty(n,"_name",{get:function(){return e.nm}}),n.numProperties=e.np,n.propertyIndex=e.ix,n.nm=e.nm,n.mn=e.mn,n}function r(t,e,r){function i(t){return"Color"===t||"color"===t?i.color:"Opacity"===t||"opacity"===t?i.opacity:null}return Object.defineProperties(i,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",r)),e.o.setGroupProperty(PropertyInterface("Opacity",r)),i}function i(t,e,r){function i(t){return"Start Point"===t||"start point"===t?i.startPoint:"End Point"===t||"end point"===t?i.endPoint:"Opacity"===t||"opacity"===t?i.opacity:null}return Object.defineProperties(i,{startPoint:{get:ExpressionPropertyInterface(e.s)},endPoint:{get:ExpressionPropertyInterface(e.e)},opacity:{get:ExpressionPropertyInterface(e.o)},type:{get:function(){return"a"}},_name:{value:t.nm},mn:{value:t.mn}}),e.s.setGroupProperty(PropertyInterface("Start Point",r)),e.e.setGroupProperty(PropertyInterface("End Point",r)),e.o.setGroupProperty(PropertyInterface("Opacity",r)),i}function n(){return function(){return null}}function s(t,e,r){var i,n=propertyGroupFactory(l,r),s=propertyGroupFactory(h,n);function a(r){Object.defineProperty(h,t.d[r].nm,{get:ExpressionPropertyInterface(e.d.dataProps[r].p)})}var o=t.d?t.d.length:0,h={};for(i=0;i<o;i+=1)a(i),e.d.dataProps[i].p.setGroupProperty(s);function l(t){return"Color"===t||"color"===t?l.color:"Opacity"===t||"opacity"===t?l.opacity:"Stroke Width"===t||"stroke width"===t?l.strokeWidth:null}return Object.defineProperties(l,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},strokeWidth:{get:ExpressionPropertyInterface(e.w)},dash:{get:function(){return h}},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",n)),e.o.setGroupProperty(PropertyInterface("Opacity",n)),e.w.setGroupProperty(PropertyInterface("Stroke Width",n)),l}function a(t,e,r){function i(e){return e===t.e.ix||"End"===e||"end"===e?i.end:e===t.s.ix?i.start:e===t.o.ix?i.offset:null}var n=propertyGroupFactory(i,r);return i.propertyIndex=t.ix,e.s.setGroupProperty(PropertyInterface("Start",n)),e.e.setGroupProperty(PropertyInterface("End",n)),e.o.setGroupProperty(PropertyInterface("Offset",n)),i.propertyIndex=t.ix,i.propertyGroup=r,Object.defineProperties(i,{start:{get:ExpressionPropertyInterface(e.s)},end:{get:ExpressionPropertyInterface(e.e)},offset:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm}}),i.mn=t.mn,i}function o(t,e,r){function i(e){return t.a.ix===e||"Anchor Point"===e?i.anchorPoint:t.o.ix===e||"Opacity"===e?i.opacity:t.p.ix===e||"Position"===e?i.position:t.r.ix===e||"Rotation"===e||"ADBE Vector Rotation"===e?i.rotation:t.s.ix===e||"Scale"===e?i.scale:t.sk&&t.sk.ix===e||"Skew"===e?i.skew:t.sa&&t.sa.ix===e||"Skew Axis"===e?i.skewAxis:null}var n=propertyGroupFactory(i,r);return e.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",n)),e.transform.mProps.p.setGroupProperty(PropertyInterface("Position",n)),e.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",n)),e.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",n)),e.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",n)),e.transform.mProps.sk&&(e.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",n)),e.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",n))),e.transform.op.setGroupProperty(PropertyInterface("Opacity",n)),Object.defineProperties(i,{opacity:{get:ExpressionPropertyInterface(e.transform.mProps.o)},position:{get:ExpressionPropertyInterface(e.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(e.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(e.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(e.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(e.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(e.transform.mProps.sa)},_name:{value:t.nm}}),i.ty="tr",i.mn=t.mn,i.propertyGroup=r,i}function h(t,e,r){function i(e){return t.p.ix===e?i.position:t.s.ix===e?i.size:null}var n=propertyGroupFactory(i,r);i.propertyIndex=t.ix;var s="tm"===e.sh.ty?e.sh.prop:e.sh;return s.s.setGroupProperty(PropertyInterface("Size",n)),s.p.setGroupProperty(PropertyInterface("Position",n)),Object.defineProperties(i,{size:{get:ExpressionPropertyInterface(s.s)},position:{get:ExpressionPropertyInterface(s.p)},_name:{value:t.nm}}),i.mn=t.mn,i}function l(t,e,r){function i(e){return t.p.ix===e?i.position:t.r.ix===e?i.rotation:t.pt.ix===e?i.points:t.or.ix===e||"ADBE Vector Star Outer Radius"===e?i.outerRadius:t.os.ix===e?i.outerRoundness:!t.ir||t.ir.ix!==e&&"ADBE Vector Star Inner Radius"!==e?t.is&&t.is.ix===e?i.innerRoundness:null:i.innerRadius}var n=propertyGroupFactory(i,r),s="tm"===e.sh.ty?e.sh.prop:e.sh;return i.propertyIndex=t.ix,s.or.setGroupProperty(PropertyInterface("Outer Radius",n)),s.os.setGroupProperty(PropertyInterface("Outer Roundness",n)),s.pt.setGroupProperty(PropertyInterface("Points",n)),s.p.setGroupProperty(PropertyInterface("Position",n)),s.r.setGroupProperty(PropertyInterface("Rotation",n)),t.ir&&(s.ir.setGroupProperty(PropertyInterface("Inner Radius",n)),s.is.setGroupProperty(PropertyInterface("Inner Roundness",n))),Object.defineProperties(i,{position:{get:ExpressionPropertyInterface(s.p)},rotation:{get:ExpressionPropertyInterface(s.r)},points:{get:ExpressionPropertyInterface(s.pt)},outerRadius:{get:ExpressionPropertyInterface(s.or)},outerRoundness:{get:ExpressionPropertyInterface(s.os)},innerRadius:{get:ExpressionPropertyInterface(s.ir)},innerRoundness:{get:ExpressionPropertyInterface(s.is)},_name:{value:t.nm}}),i.mn=t.mn,i}function p(t,e,r){function i(e){return t.p.ix===e?i.position:t.r.ix===e?i.roundness:t.s.ix===e||"Size"===e||"ADBE Vector Rect Size"===e?i.size:null}var n=propertyGroupFactory(i,r),s="tm"===e.sh.ty?e.sh.prop:e.sh;return i.propertyIndex=t.ix,s.p.setGroupProperty(PropertyInterface("Position",n)),s.s.setGroupProperty(PropertyInterface("Size",n)),s.r.setGroupProperty(PropertyInterface("Rotation",n)),Object.defineProperties(i,{position:{get:ExpressionPropertyInterface(s.p)},roundness:{get:ExpressionPropertyInterface(s.r)},size:{get:ExpressionPropertyInterface(s.s)},_name:{value:t.nm}}),i.mn=t.mn,i}function c(t,e,r){function i(e){return t.r.ix===e||"Round Corners 1"===e?i.radius:null}var n=propertyGroupFactory(i,r),s=e;return i.propertyIndex=t.ix,s.rd.setGroupProperty(PropertyInterface("Radius",n)),Object.defineProperties(i,{radius:{get:ExpressionPropertyInterface(s.rd)},_name:{value:t.nm}}),i.mn=t.mn,i}function f(t,e,r){function i(e){return t.c.ix===e||"Copies"===e?i.copies:t.o.ix===e||"Offset"===e?i.offset:null}var n=propertyGroupFactory(i,r),s=e;return i.propertyIndex=t.ix,s.c.setGroupProperty(PropertyInterface("Copies",n)),s.o.setGroupProperty(PropertyInterface("Offset",n)),Object.defineProperties(i,{copies:{get:ExpressionPropertyInterface(s.c)},offset:{get:ExpressionPropertyInterface(s.o)},_name:{value:t.nm}}),i.mn=t.mn,i}return function(e,r,i){var n;function s(t){if("number"==typeof t)return 0===(t=void 0===t?1:t)?i:n[t-1];for(var e=0,r=n.length;e<r;){if(n[e]._name===t)return n[e];e+=1}return null}return s.propertyGroup=propertyGroupFactory(s,(function(){return i})),n=t(e,r,s.propertyGroup),s.numProperties=n.length,s._name="Contents",s}}(),TextExpressionInterface=function(t){var e;function r(t){switch(t){case"ADBE Text Document":return r.sourceText;default:return null}}return Object.defineProperty(r,"sourceText",{get:function(){t.textProperty.getValue();var r=t.textProperty.currentData.t;return void 0!==r&&(t.textProperty.currentData.t=void 0,(e=new String(r)).value=r||new String(r)),e}}),r},getBlendMode=(blendModeEnums={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"},function(t){return blendModeEnums[t]||""}),blendModeEnums;function SliderEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function AngleEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function ColorEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,1,0,r)}function PointEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,1,0,r)}function LayerIndexEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function MaskIndexEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function CheckboxEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function NoValueEffect(){this.p={}}function EffectsManager(t,e){var r,i=t.ef||[];this.effectElements=[];var n,s=i.length;for(r=0;r<s;r+=1)n=new GroupEffect(i[r],e),this.effectElements.push(n)}function GroupEffect(t,e){this.init(t,e)}function BaseElement(){}function FrameElement(){}function _typeof$2(t){return(_typeof$2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(t,e){var r;this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var i,n=this.data.ef.length,s=this.data.ef;for(r=0;r<n;r+=1){switch(i=null,s[r].ty){case 0:i=new SliderEffect(s[r],e,this);break;case 1:i=new AngleEffect(s[r],e,this);break;case 2:i=new ColorEffect(s[r],e,this);break;case 3:i=new PointEffect(s[r],e,this);break;case 4:case 7:i=new CheckboxEffect(s[r],e,this);break;case 10:i=new LayerIndexEffect(s[r],e,this);break;case 11:i=new MaskIndexEffect(s[r],e,this);break;case 5:i=new EffectsManager(s[r],e,this);break;default:i=new NoValueEffect(s[r],e,this)}i&&this.effectElements.push(i)}},BaseElement.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){this.layerInterface=LayerExpressionInterface(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var t=EffectsExpressionInterface.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(t),0===this.data.ty||this.data.xt?this.compInterface=CompExpressionInterface(this):4===this.data.ty?(this.layerInterface.shapeInterface=ShapeExpressionInterface(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=TextExpressionInterface(this),this.layerInterface.text=this.layerInterface.textInterface)},setBlendMode:function(){var t=getBlendMode(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,r){this.globalData=e,this.comp=r,this.data=t,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){var r,i=this.dynamicProperties.length;for(r=0;r<i;r+=1)(e||this._isParent&&"transform"===this.dynamicProperties[r].propType)&&(this.dynamicProperties[r].getValue(),this.dynamicProperties[r]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}};var FootageInterface=(dataInterfaceFactory=function(t){function e(t){return"Outline"===t?e.outlineInterface():null}return e._name="Outline",e.outlineInterface=function(t){var e="",r=t.getFootageData();function i(t){if(r[t])return e=t,"object"===_typeof$2(r=r[t])?i:r;var n=t.indexOf(e);if(-1!==n){var s=parseInt(t.substr(n+e.length),10);return"object"===_typeof$2(r=r[s])?i:r}return""}return function(){return e="",r=t.getFootageData(),i}}(t),e},function(t){function e(t){return"Data"===t?e.dataInterface:null}return e._name="Data",e.dataInterface=dataInterfaceFactory(t),e}),dataInterfaceFactory;function FootageElement(t,e,r){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,r)}function AudioElement(t,e,r){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,r),this._isPlaying=!1,this._canPlay=!1;var i=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(i),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function BaseRenderer(){}function TransformElement(){}function MaskElement(t,e,r){this.data=t,this.element=e,this.globalData=r,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var i,n,s=this.globalData.defs,a=this.masksProperties?this.masksProperties.length:0;this.viewData=createSizedArray(a),this.solidPath="";var o,h,l,p,c,f,u=this.masksProperties,d=0,m=[],y=createElementID(),g="clipPath",v="clip-path";for(i=0;i<a;i+=1)if(("a"!==u[i].mode&&"n"!==u[i].mode||u[i].inv||100!==u[i].o.k||u[i].o.x)&&(g="mask",v="mask"),"s"!==u[i].mode&&"i"!==u[i].mode||0!==d?l=null:((l=createNS("rect")).setAttribute("fill","#ffffff"),l.setAttribute("width",this.element.comp.data.w||0),l.setAttribute("height",this.element.comp.data.h||0),m.push(l)),n=createNS("path"),"n"===u[i].mode)this.viewData[i]={op:PropertyFactory.getProp(this.element,u[i].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,u[i],3),elem:n,lastPath:""},s.appendChild(n);else{var _;if(d+=1,n.setAttribute("fill","s"===u[i].mode?"#000000":"#ffffff"),n.setAttribute("clip-rule","nonzero"),0!==u[i].x.k?(g="mask",v="mask",f=PropertyFactory.getProp(this.element,u[i].x,0,null,this.element),_=createElementID(),(p=createNS("filter")).setAttribute("id",_),(c=createNS("feMorphology")).setAttribute("operator","erode"),c.setAttribute("in","SourceGraphic"),c.setAttribute("radius","0"),p.appendChild(c),s.appendChild(p),n.setAttribute("stroke","s"===u[i].mode?"#000000":"#ffffff")):(c=null,f=null),this.storedData[i]={elem:n,x:f,expan:c,lastPath:"",lastOperator:"",filterId:_,lastRadius:0},"i"===u[i].mode){h=m.length;var b=createNS("g");for(o=0;o<h;o+=1)b.appendChild(m[o]);var P=createNS("mask");P.setAttribute("mask-type","alpha"),P.setAttribute("id",y+"_"+d),P.appendChild(n),s.appendChild(P),b.setAttribute("mask","url("+getLocationHref()+"#"+y+"_"+d+")"),m.length=0,m.push(b)}else m.push(n);u[i].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[i]={elem:n,lastPath:"",op:PropertyFactory.getProp(this.element,u[i].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,u[i],3),invRect:l},this.viewData[i].prop.k||this.drawPath(u[i],this.viewData[i].prop.v,this.viewData[i])}for(this.maskElement=createNS(g),a=m.length,i=0;i<a;i+=1)this.maskElement.appendChild(m[i]);d>0&&(this.maskElement.setAttribute("id",y),this.element.maskedElement.setAttribute(v,"url("+getLocationHref()+"#"+y+")"),s.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){this.layerInterface=FootageInterface(this)},FootageElement.prototype.getFootageData=function(){return this.footageData},AudioElement.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var r=this._volume*this._volumeMultiplier;this._previousVolume!==r&&(this._previousVolume=r,this.audio.volume(r))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(t){this.audio.rate(t)},AudioElement.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){},BaseRenderer.prototype.checkLayers=function(t){var e,r,i=this.layers.length;for(this.completeLayers=!0,e=i-1;e>=0;e-=1)this.elements[e]||(r=this.layers[e]).ip-r.st<=t-this.layers[e].st&&r.op-r.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t);default:return this.createNull(t)}},BaseRenderer.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(t){return new AudioElement(t,this.globalData,this)},BaseRenderer.prototype.createFootage=function(t){return new FootageElement(t,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(t){var e;this.completeLayers=!1;var r,i=t.length,n=this.layers.length;for(e=0;e<i;e+=1)for(r=0;r<n;){if(this.layers[r].id===t[e].id){this.layers[r]=t[e];break}r+=1}},BaseRenderer.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(t,e,r){for(var i=this.elements,n=this.layers,s=0,a=n.length;s<a;)n[s].ind==e&&(i[s]&&!0!==i[s]?(r.push(i[s]),i[s].setAsParent(),void 0!==n[s].parent?this.buildElementParenting(t,n[s].parent,r):t.setHierarchy(r)):(this.buildItem(s),this.addPendingElement(t))),s+=1},BaseRenderer.prototype.addPendingElement=function(t){this.pendingElements.push(t)},BaseRenderer.prototype.searchExtraCompositions=function(t){var e,r=t.length;for(e=0;e<r;e+=1)if(t[e].xt){var i=this.createComp(t[e]);i.initExpressions(),this.globalData.projectInterface.registerComposition(i)}},BaseRenderer.prototype.getElementByPath=function(t){var e,r=t.shift();if("number"==typeof r)e=this.elements[r];else{var i,n=this.elements.length;for(i=0;i<n;i+=1)if(this.elements[i].data.nm===r){e=this.elements[i];break}}return 0===t.length?e:e.getElementByPath(t)},BaseRenderer.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new FontManager,this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}},TransformElement.prototype={initTransform:function(){this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_opMdf:!1,mat:new Matrix},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,r=0,i=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;r<i;){if(this.hierarchy[r].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}r+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),r=0;r<i;r+=1)t=this.hierarchy[r].finalTransform.mProp.v.props,e.transform(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])}},globalToLocal:function(t){var e=[];e.push(this.finalTransform);for(var r,i=!0,n=this.comp;i;)n.finalTransform?(n.data.hasMask&&e.splice(0,0,n.finalTransform),n=n.comp):i=!1;var s,a=e.length;for(r=0;r<a;r+=1)s=e[r].mat.applyToPointArray(0,0,0),t=[t[0]-s[0],t[1]-s[1],0];return t},mHelper:new Matrix},MaskElement.prototype.getMaskProperty=function(t){return this.viewData[t].prop},MaskElement.prototype.renderFrame=function(t){var e,r=this.element.finalTransform.mat,i=this.masksProperties.length;for(e=0;e<i;e+=1)if((this.viewData[e].prop._mdf||t)&&this.drawPath(this.masksProperties[e],this.viewData[e].prop.v,this.viewData[e]),(this.viewData[e].op._mdf||t)&&this.viewData[e].elem.setAttribute("fill-opacity",this.viewData[e].op.v),"n"!==this.masksProperties[e].mode&&(this.viewData[e].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[e].invRect.setAttribute("transform",r.getInverseMatrix().to2dCSS()),this.storedData[e].x&&(this.storedData[e].x._mdf||t))){var n=this.storedData[e].expan;this.storedData[e].x.v<0?("erode"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="erode",this.storedData[e].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[e].filterId+")")),n.setAttribute("radius",-this.storedData[e].x.v)):("dilate"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="dilate",this.storedData[e].elem.setAttribute("filter",null)),this.storedData[e].elem.setAttribute("stroke-width",2*this.storedData[e].x.v))}},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){var t="M0,0 ";return t+=" h"+this.globalData.compSize.w,t+=" v"+this.globalData.compSize.h,t+=" h-"+this.globalData.compSize.w,t+=" v-"+this.globalData.compSize.h+" "},MaskElement.prototype.drawPath=function(t,e,r){var i,n,s=" M"+e.v[0][0]+","+e.v[0][1];for(n=e._length,i=1;i<n;i+=1)s+=" C"+e.o[i-1][0]+","+e.o[i-1][1]+" "+e.i[i][0]+","+e.i[i][1]+" "+e.v[i][0]+","+e.v[i][1];if(e.c&&n>1&&(s+=" C"+e.o[i-1][0]+","+e.o[i-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),r.lastPath!==s){var a="";r.elem&&(e.c&&(a=t.inv?this.solidPath+s:s),r.elem.setAttribute("d",a)),r.lastPath=s}},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=function(){var t={};return t.createFilter=function(t,e){var r=createNS("filter");r.setAttribute("id",t),!0!==e&&(r.setAttribute("filterUnits","objectBoundingBox"),r.setAttribute("x","0%"),r.setAttribute("y","0%"),r.setAttribute("width","100%"),r.setAttribute("height","100%"));return r},t.createAlphaToLuminanceFilter=function(){var t=createNS("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t},t}(),featureSupport=function(){var t={maskType:!0};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),t}(),registeredEffects={},idPrefix="filter_result_";function SVGEffects(t){var e,r,i="SourceGraphic",n=t.data.ef?t.data.ef.length:0,s=createElementID(),a=filtersFactory.createFilter(s,!0),o=0;for(this.filters=[],e=0;e<n;e+=1){r=null;var h=t.data.ef[e].ty;if(registeredEffects[h])r=new(0,registeredEffects[h].effect)(a,t.effectsManager.effectElements[e],t,idPrefix+o,i),i=idPrefix+o,registeredEffects[h].countsAsEffect&&(o+=1);r&&this.filters.push(r)}o&&(t.globalData.defs.appendChild(a),t.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+s+")")),this.filters.length&&t.addRenderableComponent(this)}function registerEffect(t,e,r){registeredEffects[t]={effect:e,countsAsEffect:r}}function SVGBaseElement(){}function HierarchyElement(){}function RenderableDOMElement(){}function IImageElement(t,e,r){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,r),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ProcessedElement(t,e){this.elem=t,this.pos=e}function IShapeElement(){}SVGEffects.prototype.renderFrame=function(t){var e,r=this.filters.length;for(e=0;e<r;e+=1)this.filters[e].renderFrame(t)},SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t,e,r,i=null;if(this.data.td){if(3==this.data.td||1==this.data.td){var n=createNS("mask");n.setAttribute("id",this.layerId),n.setAttribute("mask-type",3==this.data.td?"luminance":"alpha"),n.appendChild(this.layerElement),i=n,this.globalData.defs.appendChild(n),featureSupport.maskType||1!=this.data.td||(n.setAttribute("mask-type","luminance"),t=createElementID(),e=filtersFactory.createFilter(t),this.globalData.defs.appendChild(e),e.appendChild(filtersFactory.createAlphaToLuminanceFilter()),(r=createNS("g")).appendChild(this.layerElement),i=r,n.appendChild(r),r.setAttribute("filter","url("+getLocationHref()+"#"+t+")"))}else if(2==this.data.td){var s=createNS("mask");s.setAttribute("id",this.layerId),s.setAttribute("mask-type","alpha");var a=createNS("g");s.appendChild(a),t=createElementID(),e=filtersFactory.createFilter(t);var o=createNS("feComponentTransfer");o.setAttribute("in","SourceGraphic"),e.appendChild(o);var h=createNS("feFuncA");h.setAttribute("type","table"),h.setAttribute("tableValues","1.0 0.0"),o.appendChild(h),this.globalData.defs.appendChild(e);var l=createNS("rect");l.setAttribute("width",this.comp.data.w),l.setAttribute("height",this.comp.data.h),l.setAttribute("x","0"),l.setAttribute("y","0"),l.setAttribute("fill","#ffffff"),l.setAttribute("opacity","0"),a.setAttribute("filter","url("+getLocationHref()+"#"+t+")"),a.appendChild(l),a.appendChild(this.layerElement),i=a,featureSupport.maskType||(s.setAttribute("mask-type","luminance"),e.appendChild(filtersFactory.createAlphaToLuminanceFilter()),r=createNS("g"),a.appendChild(l),r.appendChild(this.layerElement),i=r,a.appendChild(r)),this.globalData.defs.appendChild(s)}}else this.data.tt?(this.matteElement.appendChild(this.layerElement),i=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var p=createNS("clipPath"),c=createNS("path");c.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var f=createElementID();if(p.setAttribute("id",f),p.appendChild(c),this.globalData.defs.appendChild(p),this.checkMasks()){var u=createNS("g");u.setAttribute("clip-path","url("+getLocationHref()+"#"+f+")"),u.appendChild(this.layerElement),this.transformedElement=u,i?i.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+f+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._matMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.mat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.mProp.o.v)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this)},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+t+")")}},HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},extendPrototype([RenderableElement,createProxyFunction({initElement:function(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initTransform(t,e,r),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],RenderableDOMElement),extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect},IShapeElement.prototype={addShapeToModifiers:function(t){var e,r=this.shapeModifiers.length;for(e=0;e<r;e+=1)this.shapeModifiers[e].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var t,e=this.shapes.length;for(t=0;t<e;t+=1)this.shapes[t].sh.reset();for(t=(e=this.shapeModifiers.length)-1;t>=0&&!this.shapeModifiers[t].processShapes(this._isFirstFrame);t-=1);}},searchProcessedElement:function(t){for(var e=this.processedElements,r=0,i=e.length;r<i;){if(e[r].elem===t)return e[r].pos;r+=1}return 0},addProcessedElement:function(t,e){for(var r=this.processedElements,i=r.length;i;)if(r[i-=1].elem===t)return void(r[i].pos=e);r.push(new ProcessedElement(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(t,e,r){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=r,this.lvl=e,this._isAnimated=!!r.k;for(var i=0,n=t.length;i<n;){if(t[i].mProps.dynamicProperties.length){this._isAnimated=!0;break}i+=1}}function SVGStyleData(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=createNS("path"),this.msElem=null}function DashProperty(t,e,r,i){var n;this.elem=t,this.frameId=-1,this.dataProps=createSizedArray(e.length),this.renderer=r,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",e.length?e.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(i);var s,a=e.length||0;for(n=0;n<a;n+=1)s=PropertyFactory.getProp(t,e[n].v,0,0,this),this.k=s.k||this.k,this.dataProps[n]={n:e[n].n,p:s};this.k||this.getValue(!0),this._isAnimated=this.k}function SVGStrokeStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=r,this._isAnimated=!!this._isAnimated}function SVGFillStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=r}function SVGNoStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=r}function GradientProperty(t,e,r){this.data=e,this.c=createTypedArray("uint8c",4*e.p);var i=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=createTypedArray("float32",i),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=i,this.initDynamicPropertyContainer(r),this.prop=PropertyFactory.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function SVGGradientFillStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,r)}function SVGGradientStrokeStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.initGradientData(t,e,r),this._isAnimated=!!this._isAnimated}function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(t,e,r){this.transform={mProps:t,op:e,container:r},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0},SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1},DashProperty.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,r=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<r;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty),extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData),extendPrototype([DynamicPropertyContainer],SVGFillStyleData),extendPrototype([DynamicPropertyContainer],SVGNoStyleData),GradientProperty.prototype.comparePoints=function(t,e){for(var r=0,i=this.o.length/2;r<i;){if(Math.abs(t[4*r]-t[4*e+2*r])>.01)return!1;r+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,r,i,n=4*this.data.p;for(e=0;e<n;e+=1)r=e%4==0?100:255,i=Math.round(this.prop.v[e]*r),this.c[e]!==i&&(this.c[e]=i,this._cmdf=!t);if(this.o.length)for(n=this.prop.v.length,e=4*this.data.p;e<n;e+=1)r=e%2==0?100:1,i=e%2==0?Math.round(100*this.prop.v[e]):this.prop.v[e],this.o[e-4*this.data.p]!==i&&(this.o[e-4*this.data.p]=i,this._omdf=!t);this._mdf=!t}},extendPrototype([DynamicPropertyContainer],GradientProperty),SVGGradientFillStyleData.prototype.initGradientData=function(t,e,r){this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.s=PropertyFactory.getProp(t,e.s,1,null,this),this.e=PropertyFactory.getProp(t,e.e,1,null,this),this.h=PropertyFactory.getProp(t,e.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(t,e.a||{k:0},0,degToRads,this),this.g=new GradientProperty(t,e.g,this),this.style=r,this.stops=[],this.setGradientData(r.pElem,e),this.setGradientOpacity(e,r),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(t,e){var r=createElementID(),i=createNS(1===e.t?"linearGradient":"radialGradient");i.setAttribute("id",r),i.setAttribute("spreadMethod","pad"),i.setAttribute("gradientUnits","userSpaceOnUse");var n,s,a,o=[];for(a=4*e.g.p,s=0;s<a;s+=4)n=createNS("stop"),i.appendChild(n),o.push(n);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+getLocationHref()+"#"+r+")"),this.gf=i,this.cst=o},SVGGradientFillStyleData.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var r,i,n,s=createNS("mask"),a=createNS("path");s.appendChild(a);var o=createElementID(),h=createElementID();s.setAttribute("id",h);var l=createNS(1===t.t?"linearGradient":"radialGradient");l.setAttribute("id",o),l.setAttribute("spreadMethod","pad"),l.setAttribute("gradientUnits","userSpaceOnUse"),n=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var p=this.stops;for(i=4*t.g.p;i<n;i+=2)(r=createNS("stop")).setAttribute("stop-color","rgb(255,255,255)"),l.appendChild(r),p.push(r);a.setAttribute("gf"===t.ty?"fill":"stroke","url("+getLocationHref()+"#"+o+")"),"gs"===t.ty&&(a.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),a.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),1===t.lj&&a.setAttribute("stroke-miterlimit",t.ml)),this.of=l,this.ms=s,this.ost=p,this.maskId=h,e.msElem=a}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData),extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);var buildShapeString=function(t,e,r,i){if(0===e)return"";var n,s=t.o,a=t.i,o=t.v,h=" M"+i.applyToPointStringified(o[0][0],o[0][1]);for(n=1;n<e;n+=1)h+=" C"+i.applyToPointStringified(s[n-1][0],s[n-1][1])+" "+i.applyToPointStringified(a[n][0],a[n][1])+" "+i.applyToPointStringified(o[n][0],o[n][1]);return r&&e&&(h+=" C"+i.applyToPointStringified(s[n-1][0],s[n-1][1])+" "+i.applyToPointStringified(a[0][0],a[0][1])+" "+i.applyToPointStringified(o[0][0],o[0][1]),h+="z"),h},SVGElementsRenderer=function(){var t=new Matrix,e=new Matrix;function r(t,e,r){(r||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(r||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function i(){}function n(r,i,n){var s,a,o,h,l,p,c,f,u,d,m,y=i.styles.length,g=i.lvl;for(p=0;p<y;p+=1){if(h=i.sh._mdf||n,i.styles[p].lvl<g){for(f=e.reset(),d=g-i.styles[p].lvl,m=i.transformers.length-1;!h&&d>0;)h=i.transformers[m].mProps._mdf||h,d-=1,m-=1;if(h)for(d=g-i.styles[p].lvl,m=i.transformers.length-1;d>0;)u=i.transformers[m].mProps.v.props,f.transform(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7],u[8],u[9],u[10],u[11],u[12],u[13],u[14],u[15]),d-=1,m-=1}else f=t;if(a=(c=i.sh.paths)._length,h){for(o="",s=0;s<a;s+=1)(l=c.shapes[s])&&l._length&&(o+=buildShapeString(l,l._length,l.c,f));i.caches[p]=o}else o=i.caches[p];i.styles[p].d+=!0===r.hd?"":o,i.styles[p]._mdf=h||i.styles[p]._mdf}}function s(t,e,r){var i=e.style;(e.c._mdf||r)&&i.pElem.setAttribute("fill","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r)&&i.pElem.setAttribute("fill-opacity",e.o.v)}function a(t,e,r){o(t,e,r),h(t,e,r)}function o(t,e,r){var i,n,s,a,o,h=e.gf,l=e.g._hasOpacity,p=e.s.v,c=e.e.v;if(e.o._mdf||r){var f="gf"===t.ty?"fill-opacity":"stroke-opacity";e.style.pElem.setAttribute(f,e.o.v)}if(e.s._mdf||r){var u=1===t.t?"x1":"cx",d="x1"===u?"y1":"cy";h.setAttribute(u,p[0]),h.setAttribute(d,p[1]),l&&!e.g._collapsable&&(e.of.setAttribute(u,p[0]),e.of.setAttribute(d,p[1]))}if(e.g._cmdf||r){i=e.cst;var m=e.g.c;for(s=i.length,n=0;n<s;n+=1)(a=i[n]).setAttribute("offset",m[4*n]+"%"),a.setAttribute("stop-color","rgb("+m[4*n+1]+","+m[4*n+2]+","+m[4*n+3]+")")}if(l&&(e.g._omdf||r)){var y=e.g.o;for(s=(i=e.g._collapsable?e.cst:e.ost).length,n=0;n<s;n+=1)a=i[n],e.g._collapsable||a.setAttribute("offset",y[2*n]+"%"),a.setAttribute("stop-opacity",y[2*n+1])}if(1===t.t)(e.e._mdf||r)&&(h.setAttribute("x2",c[0]),h.setAttribute("y2",c[1]),l&&!e.g._collapsable&&(e.of.setAttribute("x2",c[0]),e.of.setAttribute("y2",c[1])));else if((e.s._mdf||e.e._mdf||r)&&(o=Math.sqrt(Math.pow(p[0]-c[0],2)+Math.pow(p[1]-c[1],2)),h.setAttribute("r",o),l&&!e.g._collapsable&&e.of.setAttribute("r",o)),e.e._mdf||e.h._mdf||e.a._mdf||r){o||(o=Math.sqrt(Math.pow(p[0]-c[0],2)+Math.pow(p[1]-c[1],2)));var g=Math.atan2(c[1]-p[1],c[0]-p[0]),v=e.h.v;v>=1?v=.99:v<=-1&&(v=-.99);var _=o*v,b=Math.cos(g+e.a.v)*_+p[0],P=Math.sin(g+e.a.v)*_+p[1];h.setAttribute("fx",b),h.setAttribute("fy",P),l&&!e.g._collapsable&&(e.of.setAttribute("fx",b),e.of.setAttribute("fy",P))}}function h(t,e,r){var i=e.style,n=e.d;n&&(n._mdf||r)&&n.dashStr&&(i.pElem.setAttribute("stroke-dasharray",n.dashStr),i.pElem.setAttribute("stroke-dashoffset",n.dashoffset[0])),e.c&&(e.c._mdf||r)&&i.pElem.setAttribute("stroke","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r)&&i.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||r)&&(i.pElem.setAttribute("stroke-width",e.w.v),i.msElem&&i.msElem.setAttribute("stroke-width",e.w.v))}return{createRenderFunction:function(t){switch(t.ty){case"fl":return s;case"gf":return o;case"gs":return a;case"st":return h;case"sh":case"el":case"rc":case"sr":return n;case"tr":return r;case"no":return i;default:return null}}}}();function SVGShapeElement(t,e,r){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,r),this.prevViewData=[]}function LetterProps(t,e,r,i,n,s){this.o=t,this.sw=e,this.sc=r,this.fc=i,this.m=n,this.p=s,this._mdf={o:!0,sw:!!e,sc:!!r,fc:!!i,m:!0,p:!0}}function TextProperty(t,e){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){var t,e,r,i,n=this.shapes.length,s=this.stylesList.length,a=[],o=!1;for(r=0;r<s;r+=1){for(i=this.stylesList[r],o=!1,a.length=0,t=0;t<n;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(i)&&(a.push(e),o=e._isAnimated||o);a.length>1&&o&&this.setShapesAsAnimated(a)}},SVGShapeElement.prototype.setShapesAsAnimated=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(t,e){var r,i=new SVGStyleData(t,e),n=i.pElem;if("st"===t.ty)r=new SVGStrokeStyleData(this,t,i);else if("fl"===t.ty)r=new SVGFillStyleData(this,t,i);else if("gf"===t.ty||"gs"===t.ty){r=new("gf"===t.ty?SVGGradientFillStyleData:SVGGradientStrokeStyleData)(this,t,i),this.globalData.defs.appendChild(r.gf),r.maskId&&(this.globalData.defs.appendChild(r.ms),this.globalData.defs.appendChild(r.of),n.setAttribute("mask","url("+getLocationHref()+"#"+r.maskId+")"))}else"no"===t.ty&&(r=new SVGNoStyleData(this,t,i));return"st"!==t.ty&&"gs"!==t.ty||(n.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),n.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),n.setAttribute("fill-opacity","0"),1===t.lj&&n.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&n.setAttribute("fill-rule","evenodd"),t.ln&&n.setAttribute("id",t.ln),t.cl&&n.setAttribute("class",t.cl),t.bm&&(n.style["mix-blend-mode"]=getBlendMode(t.bm)),this.stylesList.push(i),this.addToAnimatedContents(t,r),r},SVGShapeElement.prototype.createGroupElement=function(t){var e=new ShapeGroupData;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=getBlendMode(t.bm)),e},SVGShapeElement.prototype.createTransformElement=function(t,e){var r=TransformPropertyFactory.getTransformProperty(this,t,this),i=new SVGTransformData(r,r.o,e);return this.addToAnimatedContents(t,i),i},SVGShapeElement.prototype.createShapeElement=function(t,e,r){var i=4;"rc"===t.ty?i=5:"el"===t.ty?i=6:"sr"===t.ty&&(i=7);var n=new SVGShapeData(e,r,ShapePropertyFactory.getShapeProp(this,t,i,this));return this.shapes.push(n),this.addShapeToModifiers(n),this.addToAnimatedContents(t,n),n},SVGShapeElement.prototype.addToAnimatedContents=function(t,e){for(var r=0,i=this.animatedContents.length;r<i;){if(this.animatedContents[r].element===e)return;r+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(t),element:e,data:t})},SVGShapeElement.prototype.setElementStyles=function(t){var e,r=t.styles,i=this.stylesList.length;for(e=0;e<i;e+=1)this.stylesList[e].closed||r.push(this.stylesList[e])},SVGShapeElement.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(t,e,r,i,n,s,a){var o,h,l,p,c,f,u=[].concat(s),d=t.length-1,m=[],y=[];for(o=d;o>=0;o-=1){if((f=this.searchProcessedElement(t[o]))?e[o]=r[f-1]:t[o]._render=a,"fl"===t[o].ty||"st"===t[o].ty||"gf"===t[o].ty||"gs"===t[o].ty||"no"===t[o].ty)f?e[o].style.closed=!1:e[o]=this.createStyleElement(t[o],n),t[o]._render&&e[o].style.pElem.parentNode!==i&&i.appendChild(e[o].style.pElem),m.push(e[o].style);else if("gr"===t[o].ty){if(f)for(l=e[o].it.length,h=0;h<l;h+=1)e[o].prevViewData[h]=e[o].it[h];else e[o]=this.createGroupElement(t[o]);this.searchShapes(t[o].it,e[o].it,e[o].prevViewData,e[o].gr,n+1,u,a),t[o]._render&&e[o].gr.parentNode!==i&&i.appendChild(e[o].gr)}else"tr"===t[o].ty?(f||(e[o]=this.createTransformElement(t[o],i)),p=e[o].transform,u.push(p)):"sh"===t[o].ty||"rc"===t[o].ty||"el"===t[o].ty||"sr"===t[o].ty?(f||(e[o]=this.createShapeElement(t[o],u,n)),this.setElementStyles(e[o])):"tm"===t[o].ty||"rd"===t[o].ty||"ms"===t[o].ty||"pb"===t[o].ty?(f?(c=e[o]).closed=!1:((c=ShapeModifiers.getModifier(t[o].ty)).init(this,t[o]),e[o]=c,this.shapeModifiers.push(c)),y.push(c)):"rp"===t[o].ty&&(f?(c=e[o]).closed=!0:(c=ShapeModifiers.getModifier(t[o].ty),e[o]=c,c.init(this,t,o,e),this.shapeModifiers.push(c),a=!1),y.push(c));this.addProcessedElement(t[o],o+1)}for(d=m.length,o=0;o<d;o+=1)m[o].closed=!0;for(d=y.length,o=0;o<d;o+=1)y[o].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){var t;this.renderModifiers();var e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){var t,e,r=this.animatedContents.length;for(t=0;t<r;t+=1)e=this.animatedContents[t],(this._isFirstFrame||e.element._isAnimated)&&!0!==e.data&&e.fn(e.data,e.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},LetterProps.prototype.update=function(t,e,r,i,n,s){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var a=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,a=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,a=!0),this.sc!==r&&(this.sc=r,this._mdf.sc=!0,a=!0),this.fc!==i&&(this.fc=i,this._mdf.fc=!0,a=!0),this.m!==n&&(this.m=n,this._mdf.m=!0,a=!0),!s.length||this.p[0]===s[0]&&this.p[1]===s[1]&&this.p[4]===s[4]&&this.p[5]===s[5]&&this.p[12]===s[12]&&this.p[13]===s[13]||(this.p=s,this._mdf.p=!0,a=!0),a},TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},TextProperty.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,r=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{var i;this.lock=!0,this._mdf=!1;var n=this.effectsSequence.length,s=t||this.data.d.k[this.keysIndex].s;for(i=0;i<n;i+=1)s=r!==this.keysIndex?this.effectsSequence[i](s,s.t):this.effectsSequence[i](this.currentData,s.t);e!==s&&this.setCurrentData(s),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},TextProperty.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,r=0,i=t.length;r<=i-1&&!(r===i-1||t[r+1].t>e);)r+=1;return this.keysIndex!==r&&(this.keysIndex=r),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(t){for(var e,r,i=[],n=0,s=t.length,a=!1;n<s;)e=t.charCodeAt(n),FontManager.isCombinedCharacter(e)?i[i.length-1]+=t.charAt(n):e>=55296&&e<=56319?(r=t.charCodeAt(n+1))>=56320&&r<=57343?(a||FontManager.isModifier(e,r)?(i[i.length-1]+=t.substr(n,2),a=!1):i.push(t.substr(n,2)),n+=1):i.push(t.charAt(n)):e>56319?(r=t.charCodeAt(n+1),FontManager.isZeroWidthJoiner(e,r)?(a=!0,i[i.length-1]+=t.substr(n,2),n+=1):i.push(t.charAt(n))):FontManager.isZeroWidthJoiner(e)?(i[i.length-1]+=t.charAt(n),a=!0):i.push(t.charAt(n)),n+=1;return i},TextProperty.prototype.completeTextData=function(t){t.__complete=!0;var e,r,i,n,s,a,o,h=this.elem.globalData.fontManager,l=this.data,p=[],c=0,f=l.m.g,u=0,d=0,m=0,y=[],g=0,v=0,_=h.getFontByName(t.f),b=0,P=getFontProperties(_);t.fWeight=P.weight,t.fStyle=P.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),r=t.finalText.length,t.finalLineHeight=t.lh;var S,w=t.tr/1e3*t.finalSize;if(t.sz)for(var k,x,E=!0,A=t.sz[0],C=t.sz[1];E;){k=0,g=0,r=(x=this.buildFinalText(t.t)).length,w=t.tr/1e3*t.finalSize;var T=-1;for(e=0;e<r;e+=1)S=x[e].charCodeAt(0),i=!1," "===x[e]?T=e:13!==S&&3!==S||(g=0,i=!0,k+=t.finalLineHeight||1.2*t.finalSize),h.chars?(o=h.getCharData(x[e],_.fStyle,_.fFamily),b=i?0:o.w*t.finalSize/100):b=h.measureText(x[e],t.f,t.finalSize),g+b>A&&" "!==x[e]?(-1===T?r+=1:e=T,k+=t.finalLineHeight||1.2*t.finalSize,x.splice(e,T===e?1:0,"\r"),T=-1,g=0):(g+=b,g+=w);k+=_.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&C<k?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=x,r=t.finalText.length,E=!1)}g=-w,b=0;var D,I=0;for(e=0;e<r;e+=1)if(i=!1,13===(S=(D=t.finalText[e]).charCodeAt(0))||3===S?(I=0,y.push(g),v=g>v?g:v,g=-2*w,n="",i=!0,m+=1):n=D,h.chars?(o=h.getCharData(D,_.fStyle,h.getFontByName(t.f).fFamily),b=i?0:o.w*t.finalSize/100):b=h.measureText(n,t.f,t.finalSize)," "===D?I+=b+w:(g+=b+w+I,I=0),p.push({l:b,an:b,add:u,n:i,anIndexes:[],val:n,line:m,animatorJustifyOffset:0}),2==f){if(u+=b,""===n||" "===n||e===r-1){for(""!==n&&" "!==n||(u-=b);d<=e;)p[d].an=u,p[d].ind=c,p[d].extra=b,d+=1;c+=1,u=0}}else if(3==f){if(u+=b,""===n||e===r-1){for(""===n&&(u-=b);d<=e;)p[d].an=u,p[d].ind=c,p[d].extra=b,d+=1;u=0,c+=1}}else p[c].ind=c,p[c].extra=0,c+=1;if(t.l=p,v=g>v?g:v,y.push(g),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=v,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=y;var F,M,O,R,z=l.a;a=z.length;var L=[];for(s=0;s<a;s+=1){for((F=z[s]).a.sc&&(t.strokeColorAnim=!0),F.a.sw&&(t.strokeWidthAnim=!0),(F.a.fc||F.a.fh||F.a.fs||F.a.fb)&&(t.fillColorAnim=!0),R=0,O=F.s.b,e=0;e<r;e+=1)(M=p[e]).anIndexes[s]=R,(1==O&&""!==M.val||2==O&&""!==M.val&&" "!==M.val||3==O&&(M.n||" "==M.val||e==r-1)||4==O&&(M.n||e==r-1))&&(1===F.s.rn&&L.push(R),R+=1);l.a[s].s.totalChars=R;var B,V=-1;if(1===F.s.rn)for(e=0;e<r;e+=1)V!=(M=p[e]).anIndexes[s]&&(V=M.anIndexes[s],B=L.splice(Math.floor(Math.random()*L.length),1)[0]),M.anIndexes[s]=B}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=_.ascent*t.finalSize/100},TextProperty.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var r=this.copyData({},this.data.d.k[e].s);r=this.copyData(r,t),this.data.d.k[e].s=r,this.recalculate(e),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},TextProperty.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=function(){var t=Math.max,e=Math.min,r=Math.floor;function i(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=PropertyFactory.getProp(t,e.s||{k:0},0,0,this),this.e="e"in e?PropertyFactory.getProp(t,e.e,0,0,this):{v:100},this.o=PropertyFactory.getProp(t,e.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(t,e.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(t,e.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(t,e.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return i.prototype={getMult:function(i){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var n=0,s=0,a=1,o=1;this.ne.v>0?n=this.ne.v/100:s=-this.ne.v/100,this.xe.v>0?a=1-this.xe.v/100:o=1+this.xe.v/100;var h=BezierFactory.getBezierEasing(n,s,a,o).get,l=0,p=this.finalS,c=this.finalE,f=this.data.sh;if(2===f)l=h(l=c===p?i>=c?1:0:t(0,e(.5/(c-p)+(i-p)/(c-p),1)));else if(3===f)l=h(l=c===p?i>=c?0:1:1-t(0,e(.5/(c-p)+(i-p)/(c-p),1)));else if(4===f)c===p?l=0:(l=t(0,e(.5/(c-p)+(i-p)/(c-p),1)))<.5?l*=2:l=1-2*(l-.5),l=h(l);else if(5===f){if(c===p)l=0;else{var u=c-p,d=-u/2+(i=e(t(0,i+.5-p),c-p)),m=u/2;l=Math.sqrt(1-d*d/(m*m))}l=h(l)}else 6===f?(c===p?l=0:(i=e(t(0,i+.5-p),c-p),l=(1+Math.cos(Math.PI+2*Math.PI*i/(c-p)))/2),l=h(l)):(i>=r(p)&&(l=t(0,e(i-p<0?e(c,1)-(p-i):c-i,1))),l=h(l));if(100!==this.sm.v){var y=.01*this.sm.v;0===y&&(y=1e-8);var g=.5-.5*y;l<g?l=0:(l=(l-g)/y)>1&&(l=1)}return l*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var e=2===this.data.r?1:100/this.data.totalChars,r=this.o.v/e,i=this.s.v/e+r,n=this.e.v/e+r;if(i>n){var s=i;i=n,n=s}this.finalS=i,this.finalE=n}},extendPrototype([DynamicPropertyContainer],i),{getTextSelectorProp:function(t,e,r){return new i(t,e,r)}}}();function TextAnimatorDataProperty(t,e,r){var i={propType:!1},n=PropertyFactory.getProp,s=e.a;this.a={r:s.r?n(t,s.r,0,degToRads,r):i,rx:s.rx?n(t,s.rx,0,degToRads,r):i,ry:s.ry?n(t,s.ry,0,degToRads,r):i,sk:s.sk?n(t,s.sk,0,degToRads,r):i,sa:s.sa?n(t,s.sa,0,degToRads,r):i,s:s.s?n(t,s.s,1,.01,r):i,a:s.a?n(t,s.a,1,0,r):i,o:s.o?n(t,s.o,0,.01,r):i,p:s.p?n(t,s.p,1,0,r):i,sw:s.sw?n(t,s.sw,0,0,r):i,sc:s.sc?n(t,s.sc,1,0,r):i,fc:s.fc?n(t,s.fc,1,0,r):i,fh:s.fh?n(t,s.fh,0,0,r):i,fs:s.fs?n(t,s.fs,0,.01,r):i,fb:s.fb?n(t,s.fb,0,.01,r):i,t:s.t?n(t,s.t,0,0,r):i},this.s=TextSelectorProp.getTextSelectorProp(t,e.s,r),this.s.t=e.s.t}function TextAnimatorProperty(t,e,r){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=r,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(r)}function ITextElement(){}TextAnimatorProperty.prototype.searchProperties=function(){var t,e,r=this._textData.a.length,i=PropertyFactory.getProp;for(t=0;t<r;t+=1)e=this._textData.a[t],this._animatorsData[t]=new TextAnimatorDataProperty(this._elem,e,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:i(this._elem,this._textData.p.a,0,0,this),f:i(this._elem,this._textData.p.f,0,0,this),l:i(this._elem,this._textData.p.l,0,0,this),r:i(this._elem,this._textData.p.r,0,0,this),p:i(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=i(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,this._mdf||this._isFirstFrame||e||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var r,i,n,s,a,o,h,l,p,c,f,u,d,m,y,g,v,_,b,P=this._moreOptions.alignment.v,S=this._animatorsData,w=this._textData,k=this.mHelper,x=this._renderType,E=this.renderedLetters.length,A=t.l;if(this._hasMaskedPath){if(b=this._pathData.m,!this._pathData.n||this._pathData._mdf){var C,T=b.v;for(this._pathData.r.v&&(T=T.reverse()),a={tLength:0,segments:[]},s=T._length-1,g=0,n=0;n<s;n+=1)C=bez.buildBezierData(T.v[n],T.v[n+1],[T.o[n][0]-T.v[n][0],T.o[n][1]-T.v[n][1]],[T.i[n+1][0]-T.v[n+1][0],T.i[n+1][1]-T.v[n+1][1]]),a.tLength+=C.segmentLength,a.segments.push(C),g+=C.segmentLength;n=s,b.v.c&&(C=bez.buildBezierData(T.v[n],T.v[0],[T.o[n][0]-T.v[n][0],T.o[n][1]-T.v[n][1]],[T.i[0][0]-T.v[0][0],T.i[0][1]-T.v[0][1]]),a.tLength+=C.segmentLength,a.segments.push(C),g+=C.segmentLength),this._pathData.pi=a}if(a=this._pathData.pi,o=this._pathData.f.v,f=0,c=1,l=0,p=!0,m=a.segments,o<0&&b.v.c)for(a.tLength<Math.abs(o)&&(o=-Math.abs(o)%a.tLength),c=(d=m[f=m.length-1].points).length-1;o<0;)o+=d[c].partialLength,(c-=1)<0&&(c=(d=m[f-=1].points).length-1);u=(d=m[f].points)[c-1],y=(h=d[c]).partialLength}s=A.length,r=0,i=0;var D,I,F,M,O,R=1.2*t.finalSize*.714,z=!0;F=S.length;var L,B,V,N,$,G,j,U,H,W,q,Z,K=-1,Y=o,X=f,J=c,Q=-1,tt="",et=this.defaultPropsArray;if(2===t.j||1===t.j){var rt=0,it=0,nt=2===t.j?-.5:-1,st=0,at=!0;for(n=0;n<s;n+=1)if(A[n].n){for(rt&&(rt+=it);st<n;)A[st].animatorJustifyOffset=rt,st+=1;rt=0,at=!0}else{for(I=0;I<F;I+=1)(D=S[I].a).t.propType&&(at&&2===t.j&&(it+=D.t.v*nt),(O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars)).length?rt+=D.t.v*O[0]*nt:rt+=D.t.v*O*nt);at=!1}for(rt&&(rt+=it);st<n;)A[st].animatorJustifyOffset=rt,st+=1}for(n=0;n<s;n+=1){if(k.reset(),N=1,A[n].n)r=0,i+=t.yOffset,i+=z?1:0,o=Y,z=!1,this._hasMaskedPath&&(c=J,u=(d=m[f=X].points)[c-1],y=(h=d[c]).partialLength,l=0),tt="",q="",H="",Z="",et=this.defaultPropsArray;else{if(this._hasMaskedPath){if(Q!==A[n].line){switch(t.j){case 1:o+=g-t.lineWidths[A[n].line];break;case 2:o+=(g-t.lineWidths[A[n].line])/2}Q=A[n].line}K!==A[n].ind&&(A[K]&&(o+=A[K].extra),o+=A[n].an/2,K=A[n].ind),o+=P[0]*A[n].an*.005;var ot=0;for(I=0;I<F;I+=1)(D=S[I].a).p.propType&&((O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars)).length?ot+=D.p.v[0]*O[0]:ot+=D.p.v[0]*O),D.a.propType&&((O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars)).length?ot+=D.a.v[0]*O[0]:ot+=D.a.v[0]*O);for(p=!0,this._pathData.a.v&&(o=.5*A[0].an+(g-this._pathData.f.v-.5*A[0].an-.5*A[A.length-1].an)*K/(s-1),o+=this._pathData.f.v);p;)l+y>=o+ot||!d?(v=(o+ot-l)/h.partialLength,B=u.point[0]+(h.point[0]-u.point[0])*v,V=u.point[1]+(h.point[1]-u.point[1])*v,k.translate(-P[0]*A[n].an*.005,-P[1]*R*.01),p=!1):d&&(l+=h.partialLength,(c+=1)>=d.length&&(c=0,m[f+=1]?d=m[f].points:b.v.c?(c=0,d=m[f=0].points):(l-=h.partialLength,d=null)),d&&(u=h,y=(h=d[c]).partialLength));L=A[n].an/2-A[n].add,k.translate(-L,0,0)}else L=A[n].an/2-A[n].add,k.translate(-L,0,0),k.translate(-P[0]*A[n].an*.005,-P[1]*R*.01,0);for(I=0;I<F;I+=1)(D=S[I].a).t.propType&&(O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars),0===r&&0===t.j||(this._hasMaskedPath?O.length?o+=D.t.v*O[0]:o+=D.t.v*O:O.length?r+=D.t.v*O[0]:r+=D.t.v*O));for(t.strokeWidthAnim&&(G=t.sw||0),t.strokeColorAnim&&($=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(j=[t.fc[0],t.fc[1],t.fc[2]]),I=0;I<F;I+=1)(D=S[I].a).a.propType&&((O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars)).length?k.translate(-D.a.v[0]*O[0],-D.a.v[1]*O[1],D.a.v[2]*O[2]):k.translate(-D.a.v[0]*O,-D.a.v[1]*O,D.a.v[2]*O));for(I=0;I<F;I+=1)(D=S[I].a).s.propType&&((O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars)).length?k.scale(1+(D.s.v[0]-1)*O[0],1+(D.s.v[1]-1)*O[1],1):k.scale(1+(D.s.v[0]-1)*O,1+(D.s.v[1]-1)*O,1));for(I=0;I<F;I+=1){if(D=S[I].a,O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars),D.sk.propType&&(O.length?k.skewFromAxis(-D.sk.v*O[0],D.sa.v*O[1]):k.skewFromAxis(-D.sk.v*O,D.sa.v*O)),D.r.propType&&(O.length?k.rotateZ(-D.r.v*O[2]):k.rotateZ(-D.r.v*O)),D.ry.propType&&(O.length?k.rotateY(D.ry.v*O[1]):k.rotateY(D.ry.v*O)),D.rx.propType&&(O.length?k.rotateX(D.rx.v*O[0]):k.rotateX(D.rx.v*O)),D.o.propType&&(O.length?N+=(D.o.v*O[0]-N)*O[0]:N+=(D.o.v*O-N)*O),t.strokeWidthAnim&&D.sw.propType&&(O.length?G+=D.sw.v*O[0]:G+=D.sw.v*O),t.strokeColorAnim&&D.sc.propType)for(U=0;U<3;U+=1)O.length?$[U]+=(D.sc.v[U]-$[U])*O[0]:$[U]+=(D.sc.v[U]-$[U])*O;if(t.fillColorAnim&&t.fc){if(D.fc.propType)for(U=0;U<3;U+=1)O.length?j[U]+=(D.fc.v[U]-j[U])*O[0]:j[U]+=(D.fc.v[U]-j[U])*O;D.fh.propType&&(j=O.length?addHueToRGB(j,D.fh.v*O[0]):addHueToRGB(j,D.fh.v*O)),D.fs.propType&&(j=O.length?addSaturationToRGB(j,D.fs.v*O[0]):addSaturationToRGB(j,D.fs.v*O)),D.fb.propType&&(j=O.length?addBrightnessToRGB(j,D.fb.v*O[0]):addBrightnessToRGB(j,D.fb.v*O))}}for(I=0;I<F;I+=1)(D=S[I].a).p.propType&&(O=S[I].s.getMult(A[n].anIndexes[I],w.a[I].s.totalChars),this._hasMaskedPath?O.length?k.translate(0,D.p.v[1]*O[0],-D.p.v[2]*O[1]):k.translate(0,D.p.v[1]*O,-D.p.v[2]*O):O.length?k.translate(D.p.v[0]*O[0],D.p.v[1]*O[1],-D.p.v[2]*O[2]):k.translate(D.p.v[0]*O,D.p.v[1]*O,-D.p.v[2]*O));if(t.strokeWidthAnim&&(H=G<0?0:G),t.strokeColorAnim&&(W="rgb("+Math.round(255*$[0])+","+Math.round(255*$[1])+","+Math.round(255*$[2])+")"),t.fillColorAnim&&t.fc&&(q="rgb("+Math.round(255*j[0])+","+Math.round(255*j[1])+","+Math.round(255*j[2])+")"),this._hasMaskedPath){if(k.translate(0,-t.ls),k.translate(0,P[1]*R*.01+i,0),this._pathData.p.v){_=(h.point[1]-u.point[1])/(h.point[0]-u.point[0]);var ht=180*Math.atan(_)/Math.PI;h.point[0]<u.point[0]&&(ht+=180),k.rotate(-ht*Math.PI/180)}k.translate(B,V,0),o-=P[0]*A[n].an*.005,A[n+1]&&K!==A[n+1].ind&&(o+=A[n].an/2,o+=.001*t.tr*t.finalSize)}else{switch(k.translate(r,i,0),t.ps&&k.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:k.translate(A[n].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[A[n].line]),0,0);break;case 2:k.translate(A[n].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[A[n].line])/2,0,0)}k.translate(0,-t.ls),k.translate(L,0,0),k.translate(P[0]*A[n].an*.005,P[1]*R*.01,0),r+=A[n].l+.001*t.tr*t.finalSize}"html"===x?tt=k.toCSS():"svg"===x?tt=k.to2dCSS():et=[k.props[0],k.props[1],k.props[2],k.props[3],k.props[4],k.props[5],k.props[6],k.props[7],k.props[8],k.props[9],k.props[10],k.props[11],k.props[12],k.props[13],k.props[14],k.props[15]],Z=N}E<=n?(M=new LetterProps(Z,H,W,q,tt,et),this.renderedLetters.push(M),E+=1,this.lettersChangedFlag=!0):(M=this.renderedLetters[n],this.lettersChangedFlag=M.update(Z,H,W,q,tt,et)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty),ITextElement.prototype.initElement=function(t,e,r){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,r),this.textProperty=new TextProperty(this,t.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(t.t,this.renderType,this),this.initTransform(t,e,r),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)},ITextElement.prototype.createPathShape=function(t,e){var r,i,n=e.length,s="";for(r=0;r<n;r+=1)"sh"===e[r].ty&&(i=e[r].ks.k,s+=buildShapeString(i,i.i.length,!0,t));return s},ITextElement.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},ITextElement.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},ITextElement.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},ITextElement.prototype.applyTextPropertiesToMatrix=function(t,e,r,i,n){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[r]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[r])/2,0,0)}e.translate(i,n,0)},ITextElement.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){};var emptyShapeData={shapes:[]};function SVGTextLottieElement(t,e,r){this.textSpans=[],this.renderType="svg",this.initElement(t,e,r)}function ISolidElement(t,e,r){this.initElement(t,e,r)}function NullElement(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initFrame(),this.initTransform(t,e,r),this.initHierarchy()}function SVGRendererBase(){}function ICompElement(){}function SVGCompElement(t,e,r){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,r),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function SVGRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var r="";if(e&&e.title){var i=createNS("title"),n=createElementID();i.setAttribute("id",n),i.textContent=e.title,this.svgElement.appendChild(i),r+=n}if(e&&e.description){var s=createNS("desc"),a=createElementID();s.setAttribute("id",a),s.textContent=e.description,this.svgElement.appendChild(s),r+=" "+a}r&&this.svgElement.setAttribute("aria-labelledby",r);var o=createNS("defs");this.svgElement.appendChild(o);var h=createNS("g");this.svgElement.appendChild(h),this.layerElement=h,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height},this.globalData={_mdf:!1,frameNum:-1,defs:o,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(t){for(var e=0,r=t.length,i=[],n="";e<r;)t[e]===String.fromCharCode(13)||t[e]===String.fromCharCode(3)?(i.push(n),n=""):n+=t[e],e+=1;return i.push(n),i},SVGTextLottieElement.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var r=t.shapes[0];if(r.it){var i=r.it[r.it.length-1];i.s&&(i.s.k[0]=e,i.s.k[1]=e)}}return t},SVGTextLottieElement.prototype.buildNewText=function(){var t,e;this.addDynamicProperty(this);var r=this.textProperty.currentData;this.renderedLetters=createSizedArray(r?r.l.length:0),r.fc?this.layerElement.setAttribute("fill",this.buildColor(r.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),r.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(r.sc)),this.layerElement.setAttribute("stroke-width",r.sw)),this.layerElement.setAttribute("font-size",r.finalSize);var i=this.globalData.fontManager.getFontByName(r.f);if(i.fClass)this.layerElement.setAttribute("class",i.fClass);else{this.layerElement.setAttribute("font-family",i.fFamily);var n=r.fWeight,s=r.fStyle;this.layerElement.setAttribute("font-style",s),this.layerElement.setAttribute("font-weight",n)}this.layerElement.setAttribute("aria-label",r.t);var a,o=r.l||[],h=!!this.globalData.fontManager.chars;e=o.length;var l=this.mHelper,p=this.data.singleShape,c=0,f=0,u=!0,d=.001*r.tr*r.finalSize;if(!p||h||r.sz){var m,y=this.textSpans.length;for(t=0;t<e;t+=1){if(this.textSpans[t]||(this.textSpans[t]={span:null,childSpan:null,glyph:null}),!h||!p||0===t){if(a=y>t?this.textSpans[t].span:createNS(h?"g":"text"),y<=t){if(a.setAttribute("stroke-linecap","butt"),a.setAttribute("stroke-linejoin","round"),a.setAttribute("stroke-miterlimit","4"),this.textSpans[t].span=a,h){var g=createNS("g");a.appendChild(g),this.textSpans[t].childSpan=g}this.textSpans[t].span=a,this.layerElement.appendChild(a)}a.style.display="inherit"}if(l.reset(),p&&(o[t].n&&(c=-d,f+=r.yOffset,f+=u?1:0,u=!1),this.applyTextPropertiesToMatrix(r,l,o[t].line,c,f),c+=o[t].l||0,c+=d),h){var v;if(1===(m=this.globalData.fontManager.getCharData(r.finalText[t],i.fStyle,this.globalData.fontManager.getFontByName(r.f).fFamily)).t)v=new SVGCompElement(m.data,this.globalData,this);else{var _=emptyShapeData;m.data&&m.data.shapes&&(_=this.buildShapeData(m.data,r.finalSize)),v=new SVGShapeElement(_,this.globalData,this)}if(this.textSpans[t].glyph){var b=this.textSpans[t].glyph;this.textSpans[t].childSpan.removeChild(b.layerElement),b.destroy()}this.textSpans[t].glyph=v,v._debug=!0,v.prepareFrame(0),v.renderFrame(),this.textSpans[t].childSpan.appendChild(v.layerElement),1===m.t&&this.textSpans[t].childSpan.setAttribute("transform","scale("+r.finalSize/100+","+r.finalSize/100+")")}else p&&a.setAttribute("transform","translate("+l.props[12]+","+l.props[13]+")"),a.textContent=o[t].val,a.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}p&&a&&a.setAttribute("d","")}else{var P=this.textContainer,S="start";switch(r.j){case 1:S="end";break;case 2:S="middle";break;default:S="start"}P.setAttribute("text-anchor",S),P.setAttribute("letter-spacing",d);var w=this.buildTextContents(r.finalText);for(e=w.length,f=r.ps?r.ps[1]+r.ascent:0,t=0;t<e;t+=1)(a=this.textSpans[t].span||createNS("tspan")).textContent=w[t],a.setAttribute("x",0),a.setAttribute("y",f),a.style.display="inherit",P.appendChild(a),this.textSpans[t]||(this.textSpans[t]={span:null,glyph:null}),this.textSpans[t].span=a,f+=r.finalLineHeight;this.layerElement.appendChild(P)}for(;t<this.textSpans.length;)this.textSpans[t].span.style.display="none",t+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},SVGTextLottieElement.prototype.getValue=function(){var t,e,r=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<r;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf&&(this._mdf=!0))},SVGTextLottieElement.prototype.renderInnerContent=function(){if((!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){var t,e;this._sizeChanged=!0;var r,i,n,s=this.textAnimator.renderedLetters,a=this.textProperty.currentData.l;for(e=a.length,t=0;t<e;t+=1)a[t].n||(r=s[t],i=this.textSpans[t].span,(n=this.textSpans[t].glyph)&&n.renderFrame(),r._mdf.m&&i.setAttribute("transform",r.m),r._mdf.o&&i.setAttribute("opacity",r.o),r._mdf.sw&&i.setAttribute("stroke-width",r.sw),r._mdf.sc&&i.setAttribute("stroke",r.sc),r._mdf.fc&&i.setAttribute("fill",r.fc))}},extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var t=createNS("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},NullElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement),extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(t){return new NullElement(t,this.globalData,this)},SVGRendererBase.prototype.createShape=function(t){return new SVGShapeElement(t,this.globalData,this)},SVGRendererBase.prototype.createText=function(t){return new SVGTextLottieElement(t,this.globalData,this)},SVGRendererBase.prototype.createImage=function(t){return new IImageElement(t,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(t){return new ISolidElement(t,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var r=createNS("clipPath"),i=createNS("rect");i.setAttribute("width",t.w),i.setAttribute("height",t.h),i.setAttribute("x",0),i.setAttribute("y",0);var n=createElementID();r.setAttribute("id",n),r.appendChild(i),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+n+")"),e.appendChild(r),this.layers=t.layers,this.elements=createSizedArray(t.layers.length)},SVGRendererBase.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){e[t]=!0;var r=this.createItem(this.layers[t]);e[t]=r,getExpressionsPlugin()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(r),r.initExpressions()),this.appendElementInPos(r,t),this.layers[t].tt&&(this.elements[t-1]&&!0!==this.elements[t-1]?r.setMatte(e[t-1].layerId):(this.buildItem(t-1),this.addPendingElement(r)))}},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,r=this.elements.length;e<r;){if(this.elements[e]===t){t.setMatte(this.elements[e-1].layerId);break}e+=1}}},SVGRendererBase.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){var e;null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var r=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=r-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<r;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(t,e){var r=t.getBaseElement();if(r){for(var i,n=0;n<e;)this.elements[n]&&!0!==this.elements[n]&&this.elements[n].getBaseElement()&&(i=this.elements[n].getBaseElement()),n+=1;i?this.layerElement.insertBefore(r,i):this.layerElement.appendChild(r)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initTransform(t,e,r),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var r,i=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&(this.elements[r].prepareFrame(this.renderedFrame-this.layers[r].st),this.elements[r]._mdf&&(this._mdf=!0))}},ICompElement.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},ICompElement.prototype.setElements=function(t){this.elements=t},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier);var Expressions=function(){var t={};return t.initExpressions=function(t){var e=0,r=[];t.renderer.compInterface=CompExpressionInterface(t.renderer),t.renderer.globalData.projectInterface.registerComposition(t.renderer),t.renderer.globalData.pushExpression=function(){e+=1},t.renderer.globalData.popExpression=function(){0===(e-=1)&&function(){var t,e=r.length;for(t=0;t<e;t+=1)r[t].release();r.length=0}()},t.renderer.globalData.registerExpressionProperty=function(t){-1===r.indexOf(t)&&r.push(t)}},t}();function _typeof$1(t){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function seedRandom(t,e){var r=this,i=e.pow(256,6),n=e.pow(2,52),s=2*n;function a(t){var e,r=t.length,i=this,n=0,s=i.i=i.j=0,a=i.S=[];for(r||(t=[r++]);n<256;)a[n]=n++;for(n=0;n<256;n++)a[n]=a[s=255&s+t[n%r]+(e=a[n])],a[s]=e;i.g=function(t){for(var e,r=0,n=i.i,s=i.j,a=i.S;t--;)e=a[n=255&n+1],r=256*r+a[255&(a[n]=a[s=255&s+e])+(a[s]=e)];return i.i=n,i.j=s,r}}function o(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function h(t,e){for(var r,i=t+"",n=0;n<i.length;)e[255&n]=255&(r^=19*e[255&n])+i.charCodeAt(n++);return l(e)}function l(t){return String.fromCharCode.apply(0,t)}e.seedrandom=function(p,c,f){var u=[],d=h(function t(e,r){var i,n=[],s=_typeof$1(e);if(r&&"object"==s)for(i in e)try{n.push(t(e[i],r-1))}catch(t){}return n.length?n:"string"==s?e:e+"\0"}((c=!0===c?{entropy:!0}:c||{}).entropy?[p,l(t)]:null===p?function(){try{void 0;var e=new Uint8Array(256);return(r.crypto||r.msCrypto).getRandomValues(e),l(e)}catch(e){var i=r.navigator,n=i&&i.plugins;return[+new Date,r,n,r.screen,l(t)]}}():p,3),u),m=new a(u),y=function(){for(var t=m.g(6),e=i,r=0;t<n;)t=256*(t+r),e*=256,r=m.g(1);for(;t>=s;)t/=2,e/=2,r>>>=1;return(t+r)/e};return y.int32=function(){return 0|m.g(4)},y.quick=function(){return m.g(4)/4294967296},y.double=y,h(l(m.S),t),(c.pass||f||function(t,r,i,n){return n&&(n.S&&o(n,m),t.state=function(){return o(m,{})}),i?(e.random=t,r):t})(y,d,"global"in c?c.global:this==e,c.state)},h(e.random(),t)}function initialize$2(t){seedRandom([],t)}var propTypes={SHAPE:"shape"};function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ExpressionManager=function(){var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null;function $bm_isInstanceOfArray(t){return t.constructor===Array||t.constructor===Float32Array}function isNumerable(t,e){return"number"===t||"boolean"===t||"string"===t||e instanceof Number}function $bm_neg(t){var e=_typeof(t);if("number"===e||"boolean"===e||t instanceof Number)return-t;if($bm_isInstanceOfArray(t)){var r,i=t.length,n=[];for(r=0;r<i;r+=1)n[r]=-t[r];return n}return t.propType?t.v:-t}initialize$2(BMMath);var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(t,e){var r=_typeof(t),i=_typeof(e);if("string"===r||"string"===i)return t+e;if(isNumerable(r,t)&&isNumerable(i,e))return t+e;if($bm_isInstanceOfArray(t)&&isNumerable(i,e))return(t=t.slice(0))[0]+=e,t;if(isNumerable(r,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t+e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var n=0,s=t.length,a=e.length,o=[];n<s||n<a;)("number"==typeof t[n]||t[n]instanceof Number)&&("number"==typeof e[n]||e[n]instanceof Number)?o[n]=t[n]+e[n]:o[n]=void 0===e[n]?t[n]:t[n]||e[n],n+=1;return o}return 0}var add=sum;function sub(t,e){var r=_typeof(t),i=_typeof(e);if(isNumerable(r,t)&&isNumerable(i,e))return"string"===r&&(t=parseInt(t,10)),"string"===i&&(e=parseInt(e,10)),t-e;if($bm_isInstanceOfArray(t)&&isNumerable(i,e))return(t=t.slice(0))[0]-=e,t;if(isNumerable(r,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t-e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var n=0,s=t.length,a=e.length,o=[];n<s||n<a;)("number"==typeof t[n]||t[n]instanceof Number)&&("number"==typeof e[n]||e[n]instanceof Number)?o[n]=t[n]-e[n]:o[n]=void 0===e[n]?t[n]:t[n]||e[n],n+=1;return o}return 0}function mul(t,e){var r,i,n,s=_typeof(t),a=_typeof(e);if(isNumerable(s,t)&&isNumerable(a,e))return t*e;if($bm_isInstanceOfArray(t)&&isNumerable(a,e)){for(n=t.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t[i]*e;return r}if(isNumerable(s,t)&&$bm_isInstanceOfArray(e)){for(n=e.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t*e[i];return r}return 0}function div(t,e){var r,i,n,s=_typeof(t),a=_typeof(e);if(isNumerable(s,t)&&isNumerable(a,e))return t/e;if($bm_isInstanceOfArray(t)&&isNumerable(a,e)){for(n=t.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t[i]/e;return r}if(isNumerable(s,t)&&$bm_isInstanceOfArray(e)){for(n=e.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t/e[i];return r}return 0}function mod(t,e){return"string"==typeof t&&(t=parseInt(t,10)),"string"==typeof e&&(e=parseInt(e,10)),t%e}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(t,e,r){if(e>r){var i=r;r=e,e=i}return Math.min(Math.max(t,e),r)}function radiansToDegrees(t){return t/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(t){return t*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(t,e){if("number"==typeof t||t instanceof Number)return e=e||0,Math.abs(t-e);var r;e||(e=helperLengthArray);var i=Math.min(t.length,e.length),n=0;for(r=0;r<i;r+=1)n+=Math.pow(e[r]-t[r],2);return Math.sqrt(n)}function normalize(t){return div(t,length(t))}function rgbToHsl(t){var e,r,i=t[0],n=t[1],s=t[2],a=Math.max(i,n,s),o=Math.min(i,n,s),h=(a+o)/2;if(a===o)e=0,r=0;else{var l=a-o;switch(r=h>.5?l/(2-a-o):l/(a+o),a){case i:e=(n-s)/l+(n<s?6:0);break;case n:e=(s-i)/l+2;break;case s:e=(i-n)/l+4}e/=6}return[e,r,h,t[3]]}function hue2rgb(t,e,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+6*(e-t)*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function hslToRgb(t){var e,r,i,n=t[0],s=t[1],a=t[2];if(0===s)e=a,i=a,r=a;else{var o=a<.5?a*(1+s):a+s-a*s,h=2*a-o;e=hue2rgb(h,o,n+1/3),r=hue2rgb(h,o,n),i=hue2rgb(h,o,n-1/3)}return[e,r,i,t[3]]}function linear(t,e,r,i,n){if(void 0!==i&&void 0!==n||(i=e,n=r,e=0,r=1),r<e){var s=r;r=e,e=s}if(t<=e)return i;if(t>=r)return n;var a,o=r===e?0:(t-e)/(r-e);if(!i.length)return i+(n-i)*o;var h=i.length,l=createTypedArray("float32",h);for(a=0;a<h;a+=1)l[a]=i[a]+(n[a]-i[a])*o;return l}function random(t,e){if(void 0===e&&(void 0===t?(t=0,e=1):(e=t,t=void 0)),e.length){var r,i=e.length;t||(t=createTypedArray("float32",i));var n=createTypedArray("float32",i),s=BMMath.random();for(r=0;r<i;r+=1)n[r]=t[r]+s*(e[r]-t[r]);return n}return void 0===t&&(t=0),t+BMMath.random()*(e-t)}function createPath(t,e,r,i){var n,s=t.length,a=shapePool.newElement();a.setPathData(!!i,s);var o,h,l=[0,0];for(n=0;n<s;n+=1)o=e&&e[n]?e[n]:l,h=r&&r[n]?r[n]:l,a.setTripleAt(t[n][0],t[n][1],h[0]+t[n][0],h[1]+t[n][1],o[0]+t[n][0],o[1]+t[n][1],n,!0);return a}function initiateExpression(elem,data,property){var val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=-1!==val.indexOf("random"),elemType=elem.data.ty,transform,$bm_transform,content,effect,thisProperty=property;thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0;var inPoint=elem.data.ip/elem.comp.globalData.frameRate,outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw?elem.data.sw:0,height=elem.data.sh?elem.data.sh:0,name=elem.data.nm,loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||!0!==this.data.hd,wiggle=function(t,e){var r,i,n=this.pv.length?this.pv.length:1,s=createTypedArray("float32",n);var a=Math.floor(5*time);for(r=0,i=0;r<a;){for(i=0;i<n;i+=1)s[i]+=-e+2*e*BMMath.random();r+=1}var o=5*time,h=o-Math.floor(o),l=createTypedArray("float32",n);if(n>1){for(i=0;i<n;i+=1)l[i]=this.pv[i]+s[i]+(-e+2*e*BMMath.random())*h;return l}return this.pv+s[0]+(-e+2*e*BMMath.random())*h}.bind(this);function loopInDuration(t,e){return loopIn(t,e,!0)}function loopOutDuration(t,e){return loopOut(t,e,!0)}thisProperty.loopIn&&(loopIn=thisProperty.loopIn.bind(thisProperty),loop_in=loopIn),thisProperty.loopOut&&(loopOut=thisProperty.loopOut.bind(thisProperty),loop_out=loopOut),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty)),this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface),time,velocity,value,text,textIndex,textTotal,selectorValue;function lookAt(t,e){var r=[e[0]-t[0],e[1]-t[1],e[2]-t[2]],i=Math.atan2(r[0],Math.sqrt(r[1]*r[1]+r[2]*r[2]))/degToRads;return[-Math.atan2(r[1],r[2])/degToRads,i,0]}function easeOut(t,e,r,i,n){return applyEase(easeOutBez,t,e,r,i,n)}function easeIn(t,e,r,i,n){return applyEase(easeInBez,t,e,r,i,n)}function ease(t,e,r,i,n){return applyEase(easeInOutBez,t,e,r,i,n)}function applyEase(t,e,r,i,n,s){void 0===n?(n=r,s=i):e=(e-r)/(i-r),e>1?e=1:e<0&&(e=0);var a=t(e);if($bm_isInstanceOfArray(n)){var o,h=n.length,l=createTypedArray("float32",h);for(o=0;o<h;o+=1)l[o]=(s[o]-n[o])*a+n[o];return l}return(s-n)*a+n}function nearestKey(t){var e,r,i,n=data.k.length;if(data.k.length&&"number"!=typeof data.k[0])if(r=-1,(t*=elem.comp.globalData.frameRate)<data.k[0].t)r=1,i=data.k[0].t;else{for(e=0;e<n-1;e+=1){if(t===data.k[e].t){r=e+1,i=data.k[e].t;break}if(t>data.k[e].t&&t<data.k[e+1].t){t-data.k[e].t>data.k[e+1].t-t?(r=e+2,i=data.k[e+1].t):(r=e+1,i=data.k[e].t);break}}-1===r&&(r=e+1,i=data.k[e].t)}else r=0,i=0;var s={};return s.index=r,s.time=i/elem.comp.globalData.frameRate,s}function key(t){var e,r,i;if(!data.k.length||"number"==typeof data.k[0])throw new Error("The property has no keyframe at index "+t);t-=1,e={time:data.k[t].t/elem.comp.globalData.frameRate,value:[]};var n=Object.prototype.hasOwnProperty.call(data.k[t],"s")?data.k[t].s:data.k[t-1].e;for(i=n.length,r=0;r<i;r+=1)e[r]=n[r],e.value[r]=n[r];return e}function framesToTime(t,e){return e||(e=elem.comp.globalData.frameRate),t/e}function timeToFrames(t,e){return t||0===t||(t=time),e||(e=elem.comp.globalData.frameRate),t*e}function seedRandom(t){BMMath.seedrandom(randSeed+t)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(t,e){return"string"==typeof value?void 0===e?value.substring(t):value.substring(t,e):""}function substr(t,e){return"string"==typeof value?void 0===e?value.substr(t):value.substr(t,e):""}function posterizeTime(t){time=0===t?0:Math.floor(time*t)/t,value=valueAtTime(time)}var index=elem.data.ind,hasParent=!(!elem.hierarchy||!elem.hierarchy.length),parent,randSeed=Math.floor(1e6*Math.random()),globalData=elem.globalData;function executeExpression(t){return value=t,this.frameExpressionId===elem.globalData.frameId&&"textSelector"!==this.propType?value:("textSelector"===this.propType&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),transform||(transform=elem.layerInterface("ADBE Transform Group"),$bm_transform=transform,transform&&(anchorPoint=transform.anchorPoint)),4!==elemType||content||(content=thisLayer("ADBE Root Vectors Group")),effect||(effect=thisLayer(4)),(hasParent=!(!elem.hierarchy||!elem.hierarchy.length))&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath],ob}(),expressionHelpers={searchExpressions:function(t,e,r){e.x&&(r.k=!0,r.x=!0,r.initiateExpression=ExpressionManager.initiateExpression,r.effectsSequence.push(r.initiateExpression(t,e,r).bind(r)))},getSpeedAtTime:function(t){var e=this.getValueAtTime(t),r=this.getValueAtTime(t+-.01),i=0;if(e.length){var n;for(n=0;n<e.length;n+=1)i+=Math.pow(r[n]-e[n],2);i=100*Math.sqrt(i)}else i=0;return i},getVelocityAtTime:function(t){if(void 0!==this.vel)return this.vel;var e,r,i=this.getValueAtTime(t),n=this.getValueAtTime(t+-.001);if(i.length)for(e=createTypedArray("float32",i.length),r=0;r<i.length;r+=1)e[r]=(n[r]-i[r])/-.001;else e=(n-i)/-.001;return e},getValueAtTime:function(t){return t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<t?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(t,this._cachingAtTime),this._cachingAtTime.lastFrame=t),this._cachingAtTime.value},getStaticValueAtTime:function(){return this.pv},setGroupProperty:function(t){this.propertyGroup=t}};function addPropertyDecorator(){function t(t,e,r){if(!this.k||!this.keyframes)return this.pv;t=t?t.toLowerCase():"";var i,n,s,a,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[l.length-1].t;if(h<=p)return this.pv;if(r?n=p-(i=e?Math.abs(p-this.elem.comp.globalData.frameRate*e):Math.max(0,p-this.elem.data.ip)):((!e||e>l.length-1)&&(e=l.length-1),i=p-(n=l[l.length-1-e].t)),"pingpong"===t){if(Math.floor((h-n)/i)%2!=0)return this.getValueAtTime((i-(h-n)%i+n)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var c=this.getValueAtTime(n/this.comp.globalData.frameRate,0),f=this.getValueAtTime(p/this.comp.globalData.frameRate,0),u=this.getValueAtTime(((h-n)%i+n)/this.comp.globalData.frameRate,0),d=Math.floor((h-n)/i);if(this.pv.length){for(a=(o=new Array(c.length)).length,s=0;s<a;s+=1)o[s]=(f[s]-c[s])*d+u[s];return o}return(f-c)*d+u}if("continue"===t){var m=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(a=(o=new Array(m.length)).length,s=0;s<a;s+=1)o[s]=m[s]+(m[s]-y[s])*((h-p)/this.comp.globalData.frameRate)/5e-4;return o}return m+(h-p)/.001*(m-y)}}return this.getValueAtTime(((h-n)%i+n)/this.comp.globalData.frameRate,0)}function e(t,e,r){if(!this.k)return this.pv;t=t?t.toLowerCase():"";var i,n,s,a,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[0].t;if(h>=p)return this.pv;if(r?n=p+(i=e?Math.abs(this.elem.comp.globalData.frameRate*e):Math.max(0,this.elem.data.op-p)):((!e||e>l.length-1)&&(e=l.length-1),i=(n=l[e].t)-p),"pingpong"===t){if(Math.floor((p-h)/i)%2==0)return this.getValueAtTime(((p-h)%i+p)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var c=this.getValueAtTime(p/this.comp.globalData.frameRate,0),f=this.getValueAtTime(n/this.comp.globalData.frameRate,0),u=this.getValueAtTime((i-(p-h)%i+p)/this.comp.globalData.frameRate,0),d=Math.floor((p-h)/i)+1;if(this.pv.length){for(a=(o=new Array(c.length)).length,s=0;s<a;s+=1)o[s]=u[s]-(f[s]-c[s])*d;return o}return u-(f-c)*d}if("continue"===t){var m=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(a=(o=new Array(m.length)).length,s=0;s<a;s+=1)o[s]=m[s]+(m[s]-y[s])*(p-h)/.001;return o}return m+(m-y)*(p-h)/.001}}return this.getValueAtTime((i-((p-h)%i+p))/this.comp.globalData.frameRate,0)}function r(t,e){if(!this.k)return this.pv;if(t=.5*(t||.4),(e=Math.floor(e||5))<=1)return this.pv;var r,i,n=this.comp.renderedFrame/this.comp.globalData.frameRate,s=n-t,a=e>1?(n+t-s)/(e-1):1,o=0,h=0;for(r=this.pv.length?createTypedArray("float32",this.pv.length):0;o<e;){if(i=this.getValueAtTime(s+o*a),this.pv.length)for(h=0;h<this.pv.length;h+=1)r[h]+=i[h];else r+=i;o+=1}if(this.pv.length)for(h=0;h<this.pv.length;h+=1)r[h]/=e;else r/=e;return r}function i(t){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var e=this._transformCachingAtTime.v;if(e.cloneFromProps(this.pre.props),this.appliedTransformations<1){var r=this.a.getValueAtTime(t);e.translate(-r[0]*this.a.mult,-r[1]*this.a.mult,r[2]*this.a.mult)}if(this.appliedTransformations<2){var i=this.s.getValueAtTime(t);e.scale(i[0]*this.s.mult,i[1]*this.s.mult,i[2]*this.s.mult)}if(this.sk&&this.appliedTransformations<3){var n=this.sk.getValueAtTime(t),s=this.sa.getValueAtTime(t);e.skewFromAxis(-n*this.sk.mult,s*this.sa.mult)}if(this.r&&this.appliedTransformations<4){var a=this.r.getValueAtTime(t);e.rotate(-a*this.r.mult)}else if(!this.r&&this.appliedTransformations<4){var o=this.rz.getValueAtTime(t),h=this.ry.getValueAtTime(t),l=this.rx.getValueAtTime(t),p=this.or.getValueAtTime(t);e.rotateZ(-o*this.rz.mult).rotateY(h*this.ry.mult).rotateX(l*this.rx.mult).rotateZ(-p[2]*this.or.mult).rotateY(p[1]*this.or.mult).rotateX(p[0]*this.or.mult)}if(this.data.p&&this.data.p.s){var c=this.px.getValueAtTime(t),f=this.py.getValueAtTime(t);if(this.data.p.z){var u=this.pz.getValueAtTime(t);e.translate(c*this.px.mult,f*this.py.mult,-u*this.pz.mult)}else e.translate(c*this.px.mult,f*this.py.mult,0)}else{var d=this.p.getValueAtTime(t);e.translate(d[0]*this.p.mult,d[1]*this.p.mult,-d[2]*this.p.mult)}return e}function n(){return this.v.clone(new Matrix)}var s=TransformPropertyFactory.getTransformProperty;TransformPropertyFactory.getTransformProperty=function(t,e,r){var a=s(t,e,r);return a.dynamicProperties.length?a.getValueAtTime=i.bind(a):a.getValueAtTime=n.bind(a),a.setGroupProperty=expressionHelpers.setGroupProperty,a};var a=PropertyFactory.getProp;PropertyFactory.getProp=function(i,n,s,o,h){var l=a(i,n,s,o,h);l.kf?l.getValueAtTime=expressionHelpers.getValueAtTime.bind(l):l.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(l),l.setGroupProperty=expressionHelpers.setGroupProperty,l.loopOut=t,l.loopIn=e,l.smooth=r,l.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(l),l.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(l),l.numKeys=1===n.a?n.k.length:0,l.propertyIndex=n.ix;var p=0;return 0!==s&&(p=createTypedArray("float32",1===n.a?n.k[0].s.length:n.k.length)),l._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:p},expressionHelpers.searchExpressions(i,n,l),l.k&&h.addDynamicProperty(l),l};var o=ShapePropertyFactory.getConstructorFunction(),h=ShapePropertyFactory.getKeyframedConstructorFunction();function l(){}l.prototype={vertices:function(t,e){this.k&&this.getValue();var r,i=this.v;void 0!==e&&(i=this.getValueAtTime(e,0));var n=i._length,s=i[t],a=i.v,o=createSizedArray(n);for(r=0;r<n;r+=1)o[r]="i"===t||"o"===t?[s[r][0]-a[r][0],s[r][1]-a[r][1]]:[s[r][0],s[r][1]];return o},points:function(t){return this.vertices("v",t)},inTangents:function(t){return this.vertices("i",t)},outTangents:function(t){return this.vertices("o",t)},isClosed:function(){return this.v.c},pointOnPath:function(t,e){var r=this.v;void 0!==e&&(r=this.getValueAtTime(e,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(r));for(var i,n=this._segmentsLength,s=n.lengths,a=n.totalLength*t,o=0,h=s.length,l=0;o<h;){if(l+s[o].addedLength>a){var p=o,c=r.c&&o===h-1?0:o+1,f=(a-l)/s[o].addedLength;i=bez.getPointInSegment(r.v[p],r.v[c],r.o[p],r.i[c],f,s[o]);break}l+=s[o].addedLength,o+=1}return i||(i=r.c?[r.v[0][0],r.v[0][1]]:[r.v[r._length-1][0],r.v[r._length-1][1]]),i},vectorOnPath:function(t,e,r){1==t?t=this.v.c:0==t&&(t=.999);var i=this.pointOnPath(t,e),n=this.pointOnPath(t+.001,e),s=n[0]-i[0],a=n[1]-i[1],o=Math.sqrt(Math.pow(s,2)+Math.pow(a,2));return 0===o?[0,0]:"tangent"===r?[s/o,a/o]:[-a/o,s/o]},tangentOnPath:function(t,e){return this.vectorOnPath(t,e,"tangent")},normalOnPath:function(t,e){return this.vectorOnPath(t,e,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([l],o),extendPrototype([l],h),h.prototype.getValueAtTime=function(t){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<t?this._caching.lastIndex:0,this._cachingAtTime.lastTime=t,this.interpolateShape(t,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue},h.prototype.initiateExpression=ExpressionManager.initiateExpression;var p=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(t,e,r,i,n){var s=p(t,e,r,i,n);return s.propertyIndex=e.ix,s.lock=!1,3===r?expressionHelpers.searchExpressions(t,e.pt,s):4===r&&expressionHelpers.searchExpressions(t,e.ks,s),s.k&&t.addDynamicProperty(s),s}}function initialize$1(){addPropertyDecorator()}function addDecorator(){TextProperty.prototype.getExpressionValue=function(t,e){var r=this.calculateExpression(e);if(t.t!==r){var i={};return this.copyData(i,t),i.t=r.toString(),i.__complete=!1,i}return t},TextProperty.prototype.searchProperty=function(){var t=this.searchKeyframes(),e=this.searchExpressions();return this.kf=t||e,this.kf},TextProperty.prototype.searchExpressions=function(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}}function initialize(){addDecorator()}function SVGComposableEffect(){}function SVGTintFilter(t,e,r,i,n){this.filterManager=e;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","linearRGB"),s.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),s.setAttribute("result",i+"_tint_1"),t.appendChild(s),(s=createNS("feColorMatrix")).setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),s.setAttribute("result",i+"_tint_2"),t.appendChild(s),this.matrixFilter=s;var a=this.createMergeNode(i,[n,i+"_tint_1",i+"_tint_2"]);t.appendChild(a)}function SVGFillFilter(t,e,r,i){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),n.setAttribute("result",i),t.appendChild(n),this.matrixFilter=n}function SVGStrokeEffect(t,e,r){this.initialized=!1,this.filterManager=e,this.elem=r,this.paths=[]}function SVGTritoneFilter(t,e,r,i){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","linearRGB"),n.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),t.appendChild(n);var s=createNS("feComponentTransfer");s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("result",i),this.matrixFilter=s;var a=createNS("feFuncR");a.setAttribute("type","table"),s.appendChild(a),this.feFuncR=a;var o=createNS("feFuncG");o.setAttribute("type","table"),s.appendChild(o),this.feFuncG=o;var h=createNS("feFuncB");h.setAttribute("type","table"),s.appendChild(h),this.feFuncB=h,t.appendChild(s)}function SVGProLevelsFilter(t,e,r,i){this.filterManager=e;var n=this.filterManager.effectElements,s=createNS("feComponentTransfer");(n[10].p.k||0!==n[10].p.v||n[11].p.k||1!==n[11].p.v||n[12].p.k||1!==n[12].p.v||n[13].p.k||0!==n[13].p.v||n[14].p.k||1!==n[14].p.v)&&(this.feFuncR=this.createFeFunc("feFuncR",s)),(n[17].p.k||0!==n[17].p.v||n[18].p.k||1!==n[18].p.v||n[19].p.k||1!==n[19].p.v||n[20].p.k||0!==n[20].p.v||n[21].p.k||1!==n[21].p.v)&&(this.feFuncG=this.createFeFunc("feFuncG",s)),(n[24].p.k||0!==n[24].p.v||n[25].p.k||1!==n[25].p.v||n[26].p.k||1!==n[26].p.v||n[27].p.k||0!==n[27].p.v||n[28].p.k||1!==n[28].p.v)&&(this.feFuncB=this.createFeFunc("feFuncB",s)),(n[31].p.k||0!==n[31].p.v||n[32].p.k||1!==n[32].p.v||n[33].p.k||1!==n[33].p.v||n[34].p.k||0!==n[34].p.v||n[35].p.k||1!==n[35].p.v)&&(this.feFuncA=this.createFeFunc("feFuncA",s)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(s.setAttribute("color-interpolation-filters","sRGB"),t.appendChild(s)),(n[3].p.k||0!==n[3].p.v||n[4].p.k||1!==n[4].p.v||n[5].p.k||1!==n[5].p.v||n[6].p.k||0!==n[6].p.v||n[7].p.k||1!==n[7].p.v)&&((s=createNS("feComponentTransfer")).setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("result",i),t.appendChild(s),this.feFuncRComposed=this.createFeFunc("feFuncR",s),this.feFuncGComposed=this.createFeFunc("feFuncG",s),this.feFuncBComposed=this.createFeFunc("feFuncB",s))}function SVGDropShadowEffect(t,e,r,i,n){var s=e.container.globalData.renderConfig.filterSize,a=e.data.fs||s;t.setAttribute("x",a.x||s.x),t.setAttribute("y",a.y||s.y),t.setAttribute("width",a.width||s.width),t.setAttribute("height",a.height||s.height),this.filterManager=e;var o=createNS("feGaussianBlur");o.setAttribute("in","SourceAlpha"),o.setAttribute("result",i+"_drop_shadow_1"),o.setAttribute("stdDeviation","0"),this.feGaussianBlur=o,t.appendChild(o);var h=createNS("feOffset");h.setAttribute("dx","25"),h.setAttribute("dy","0"),h.setAttribute("in",i+"_drop_shadow_1"),h.setAttribute("result",i+"_drop_shadow_2"),this.feOffset=h,t.appendChild(h);var l=createNS("feFlood");l.setAttribute("flood-color","#00ff00"),l.setAttribute("flood-opacity","1"),l.setAttribute("result",i+"_drop_shadow_3"),this.feFlood=l,t.appendChild(l);var p=createNS("feComposite");p.setAttribute("in",i+"_drop_shadow_3"),p.setAttribute("in2",i+"_drop_shadow_2"),p.setAttribute("operator","in"),p.setAttribute("result",i+"_drop_shadow_4"),t.appendChild(p);var c=this.createMergeNode(i,[i+"_drop_shadow_4",n]);t.appendChild(c)}SVGComposableEffect.prototype={createMergeNode:function(t,e){var r,i,n=createNS("feMerge");for(n.setAttribute("result",t),i=0;i<e.length;i+=1)(r=createNS("feMergeNode")).setAttribute("in",e[i]),n.appendChild(r),n.appendChild(r);return n}},extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v/100;this.matrixFilter.setAttribute("values",r[0]-e[0]+" 0 0 0 "+e[0]+" "+(r[1]-e[1])+" 0 0 0 "+e[1]+" "+(r[2]-e[2])+" 0 0 0 "+e[2]+" 0 0 0 "+i+" 0")}},SVGFillFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[2].p.v,r=this.filterManager.effectElements[6].p.v;this.matrixFilter.setAttribute("values","0 0 0 0 "+e[0]+" 0 0 0 0 "+e[1]+" 0 0 0 0 "+e[2]+" 0 0 0 "+r+" 0")}},SVGStrokeEffect.prototype.initialize=function(){var t,e,r,i,n=this.elem.layerElement.children||this.elem.layerElement.childNodes;for(1===this.filterManager.effectElements[1].p.v?(i=this.elem.maskManager.masksProperties.length,r=0):i=(r=this.filterManager.effectElements[0].p.v-1)+1,(e=createNS("g")).setAttribute("fill","none"),e.setAttribute("stroke-linecap","round"),e.setAttribute("stroke-dashoffset",1);r<i;r+=1)t=createNS("path"),e.appendChild(t),this.paths.push({p:t,m:r});if(3===this.filterManager.effectElements[10].p.v){var s=createNS("mask"),a=createElementID();s.setAttribute("id",a),s.setAttribute("mask-type","alpha"),s.appendChild(e),this.elem.globalData.defs.appendChild(s);var o=createNS("g");for(o.setAttribute("mask","url("+getLocationHref()+"#"+a+")");n[0];)o.appendChild(n[0]);this.elem.layerElement.appendChild(o),this.masker=s,e.setAttribute("stroke","#fff")}else if(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v){if(2===this.filterManager.effectElements[10].p.v)for(n=this.elem.layerElement.children||this.elem.layerElement.childNodes;n.length;)this.elem.layerElement.removeChild(n[0]);this.elem.layerElement.appendChild(e),this.elem.layerElement.removeAttribute("mask"),e.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=e},SVGStrokeEffect.prototype.renderFrame=function(t){var e;this.initialized||this.initialize();var r,i,n=this.paths.length;for(e=0;e<n;e+=1)if(-1!==this.paths[e].m&&(r=this.elem.maskManager.viewData[this.paths[e].m],i=this.paths[e].p,(t||this.filterManager._mdf||r.prop._mdf)&&i.setAttribute("d",r.lastPath),t||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||r.prop._mdf)){var s;if(0!==this.filterManager.effectElements[7].p.v||100!==this.filterManager.effectElements[8].p.v){var a=.01*Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),o=.01*Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),h=i.getTotalLength();s="0 0 0 "+h*a+" ";var l,p=h*(o-a),c=1+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01,f=Math.floor(p/c);for(l=0;l<f;l+=1)s+="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01+" ";s+="0 "+10*h+" 0 0"}else s="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01;i.setAttribute("stroke-dasharray",s)}if((t||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",2*this.filterManager.effectElements[4].p.v),(t||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v)&&(t||this.filterManager.effectElements[3].p._mdf)){var u=this.filterManager.effectElements[3].p.v;this.pathMasker.setAttribute("stroke","rgb("+bmFloor(255*u[0])+","+bmFloor(255*u[1])+","+bmFloor(255*u[2])+")")}},SVGTritoneFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v,n=i[0]+" "+r[0]+" "+e[0],s=i[1]+" "+r[1]+" "+e[1],a=i[2]+" "+r[2]+" "+e[2];this.feFuncR.setAttribute("tableValues",n),this.feFuncG.setAttribute("tableValues",s),this.feFuncB.setAttribute("tableValues",a)}},SVGProLevelsFilter.prototype.createFeFunc=function(t,e){var r=createNS(t);return r.setAttribute("type","table"),e.appendChild(r),r},SVGProLevelsFilter.prototype.getTableValue=function(t,e,r,i,n){for(var s,a,o=0,h=Math.min(t,e),l=Math.max(t,e),p=Array.call(null,{length:256}),c=0,f=n-i,u=e-t;o<=256;)a=(s=o/256)<=h?u<0?n:i:s>=l?u<0?i:n:i+f*Math.pow((s-t)/u,1/r),p[c]=a,c+=1,o+=256/255;return p.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e,r=this.filterManager.effectElements;this.feFuncRComposed&&(t||r[3].p._mdf||r[4].p._mdf||r[5].p._mdf||r[6].p._mdf||r[7].p._mdf)&&(e=this.getTableValue(r[3].p.v,r[4].p.v,r[5].p.v,r[6].p.v,r[7].p.v),this.feFuncRComposed.setAttribute("tableValues",e),this.feFuncGComposed.setAttribute("tableValues",e),this.feFuncBComposed.setAttribute("tableValues",e)),this.feFuncR&&(t||r[10].p._mdf||r[11].p._mdf||r[12].p._mdf||r[13].p._mdf||r[14].p._mdf)&&(e=this.getTableValue(r[10].p.v,r[11].p.v,r[12].p.v,r[13].p.v,r[14].p.v),this.feFuncR.setAttribute("tableValues",e)),this.feFuncG&&(t||r[17].p._mdf||r[18].p._mdf||r[19].p._mdf||r[20].p._mdf||r[21].p._mdf)&&(e=this.getTableValue(r[17].p.v,r[18].p.v,r[19].p.v,r[20].p.v,r[21].p.v),this.feFuncG.setAttribute("tableValues",e)),this.feFuncB&&(t||r[24].p._mdf||r[25].p._mdf||r[26].p._mdf||r[27].p._mdf||r[28].p._mdf)&&(e=this.getTableValue(r[24].p.v,r[25].p.v,r[26].p.v,r[27].p.v,r[28].p.v),this.feFuncB.setAttribute("tableValues",e)),this.feFuncA&&(t||r[31].p._mdf||r[32].p._mdf||r[33].p._mdf||r[34].p._mdf||r[35].p._mdf)&&(e=this.getTableValue(r[31].p.v,r[32].p.v,r[33].p.v,r[34].p.v,r[35].p.v),this.feFuncA.setAttribute("tableValues",e))}},extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){if((t||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),t||this.filterManager.effectElements[0].p._mdf){var e=this.filterManager.effectElements[0].p.v;this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(255*e[0]),Math.round(255*e[1]),Math.round(255*e[2])))}if((t||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),t||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf){var r=this.filterManager.effectElements[3].p.v,i=(this.filterManager.effectElements[2].p.v-90)*degToRads,n=r*Math.cos(i),s=r*Math.sin(i);this.feOffset.setAttribute("dx",n),this.feOffset.setAttribute("dy",s)}}};var _svgMatteSymbols=[];function SVGMatte3Effect(t,e,r){this.initialized=!1,this.filterManager=e,this.filterElem=t,this.elem=r,r.matteElement=createNS("g"),r.matteElement.appendChild(r.layerElement),r.matteElement.appendChild(r.transformedElement),r.baseElement=r.matteElement}function SVGGaussianBlurEffect(t,e,r,i){t.setAttribute("x","-100%"),t.setAttribute("y","-100%"),t.setAttribute("width","300%"),t.setAttribute("height","300%"),this.filterManager=e;var n=createNS("feGaussianBlur");n.setAttribute("result",i),t.appendChild(n),this.feGaussianBlur=n}return SVGMatte3Effect.prototype.findSymbol=function(t){for(var e=0,r=_svgMatteSymbols.length;e<r;){if(_svgMatteSymbols[e]===t)return _svgMatteSymbols[e];e+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(t,e){var r=t.layerElement.parentNode;if(r){for(var i,n=r.children,s=0,a=n.length;s<a&&n[s]!==t.layerElement;)s+=1;s<=a-2&&(i=n[s+1]);var o=createNS("use");o.setAttribute("href","#"+e),i?r.insertBefore(o,i):r.appendChild(o)}},SVGMatte3Effect.prototype.setElementAsMask=function(t,e){if(!this.findSymbol(e)){var r=createElementID(),i=createNS("mask");i.setAttribute("id",e.layerId),i.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(e);var n=t.globalData.defs;n.appendChild(i);var s=createNS("symbol");s.setAttribute("id",r),this.replaceInParent(e,r),s.appendChild(e.layerElement),n.appendChild(s);var a=createNS("use");a.setAttribute("href","#"+r),i.appendChild(a),e.data.hd=!1,e.show()}t.setMatte(e.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var t=this.filterManager.effectElements[0].p.v,e=this.elem.comp.elements,r=0,i=e.length;r<i;)e[r]&&e[r].data.ind===t&&this.setElementAsMask(this.elem,e[r]),r+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()},SVGGaussianBlurEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=.3*this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=3==r?0:e,n=2==r?0:e;this.feGaussianBlur.setAttribute("stdDeviation",i+" "+n);var s=1==this.filterManager.effectElements[2].p.v?"wrap":"duplicate";this.feGaussianBlur.setAttribute("edgeMode",s)}},setExpressionsPlugin(Expressions),initialize$1(),initialize(),registerEffect(20,SVGTintFilter,!0),registerEffect(21,SVGFillFilter,!0),registerEffect(22,SVGStrokeEffect,!1),registerEffect(23,SVGTritoneFilter,!0),registerEffect(24,SVGProLevelsFilter,!0),registerEffect(25,SVGDropShadowEffect,!0),registerEffect(28,SVGMatte3Effect,!1),registerEffect(29,SVGGaussianBlurEffect,!0),lottie}))})),jszip=createCommonjsModule((function(t,e){
/*!

  JSZip v3.6.0 - A JavaScript class for generating and reading zip files
  <http://stuartk.com/jszip>

  (c) 2009-2016 Stuart Knightley <stuart [at] stuartk.com>
  Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/master/LICENSE.markdown.

  JSZip uses the library pako released under the MIT license :
  https://github.com/nodeca/pako/blob/master/LICENSE
  */
!function(e){t.exports=e()}((function(){return function t(e,r,i){function n(a,o){if(!r[a]){if(!e[a]){var h="function"==typeof commonjsRequire&&commonjsRequire;if(!o&&h)return h(a,!0);if(s)return s(a,!0);var l=new Error("Cannot find module '"+a+"'");throw l.code="MODULE_NOT_FOUND",l}var p=r[a]={exports:{}};e[a][0].call(p.exports,(function(t){var r=e[a][1][t];return n(r||t)}),p,p.exports,t,e,r,i)}return r[a].exports}for(var s="function"==typeof commonjsRequire&&commonjsRequire,a=0;a<i.length;a++)n(i[a]);return n}({1:[function(t,e,r){(function(i){
/*!

  JSZip v3.5.0 - A JavaScript class for generating and reading zip files
  <http://stuartk.com/jszip>

  (c) 2009-2016 Stuart Knightley <stuart [at] stuartk.com>
  Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/master/LICENSE.markdown.

  JSZip uses the library pako released under the MIT license :
  https://github.com/nodeca/pako/blob/master/LICENSE
  */
!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==i?i:"undefined"!=typeof self?self:this).JSZip=t()}((function(){return function e(r,i,n){function s(o,h){if(!i[o]){if(!r[o]){var l="function"==typeof t&&t;if(!h&&l)return l(o,!0);if(a)return a(o,!0);var p=new Error("Cannot find module '"+o+"'");throw p.code="MODULE_NOT_FOUND",p}var c=i[o]={exports:{}};r[o][0].call(c.exports,(function(t){return s(r[o][1][t]||t)}),c,c.exports,e,r,i,n)}return i[o].exports}for(var a="function"==typeof t&&t,o=0;o<n.length;o++)s(n[o]);return s}({1:[function(t,e,r){(function(i){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==i?i:"undefined"!=typeof self?self:this).JSZip=t()}((function(){return function e(r,i,n){function s(o,h){if(!i[o]){if(!r[o]){var l="function"==typeof t&&t;if(!h&&l)return l(o,!0);if(a)return a(o,!0);var p=new Error("Cannot find module '"+o+"'");throw p.code="MODULE_NOT_FOUND",p}var c=i[o]={exports:{}};r[o][0].call(c.exports,(function(t){return s(r[o][1][t]||t)}),c,c.exports,e,r,i,n)}return i[o].exports}for(var a="function"==typeof t&&t,o=0;o<n.length;o++)s(n[o]);return s}({1:[function(t,e,r){(function(i){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==i?i:"undefined"!=typeof self?self:this).JSZip=t()}((function(){return function e(r,i,n){function s(o,h){if(!i[o]){if(!r[o]){var l="function"==typeof t&&t;if(!h&&l)return l(o,!0);if(a)return a(o,!0);var p=new Error("Cannot find module '"+o+"'");throw p.code="MODULE_NOT_FOUND",p}var c=i[o]={exports:{}};r[o][0].call(c.exports,(function(t){return s(r[o][1][t]||t)}),c,c.exports,e,r,i,n)}return i[o].exports}for(var a="function"==typeof t&&t,o=0;o<n.length;o++)s(n[o]);return s}({1:[function(t,e,r){(function(i){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==i?i:"undefined"!=typeof self?self:this).JSZip=t()}((function(){return function e(r,i,n){function s(o,h){if(!i[o]){if(!r[o]){var l="function"==typeof t&&t;if(!h&&l)return l(o,!0);if(a)return a(o,!0);var p=new Error("Cannot find module '"+o+"'");throw p.code="MODULE_NOT_FOUND",p}var c=i[o]={exports:{}};r[o][0].call(c.exports,(function(t){return s(r[o][1][t]||t)}),c,c.exports,e,r,i,n)}return i[o].exports}for(var a="function"==typeof t&&t,o=0;o<n.length;o++)s(n[o]);return s}({1:[function(t,e,r){(function(i){!function(t){"object"==typeof r&&void 0!==e?e.exports=t():("undefined"!=typeof window?window:void 0!==i?i:"undefined"!=typeof self?self:this).JSZip=t()}((function(){return function e(r,i,n){function s(o,h){if(!i[o]){if(!r[o]){var l="function"==typeof t&&t;if(!h&&l)return l(o,!0);if(a)return a(o,!0);var p=new Error("Cannot find module '"+o+"'");throw p.code="MODULE_NOT_FOUND",p}var c=i[o]={exports:{}};r[o][0].call(c.exports,(function(t){return s(r[o][1][t]||t)}),c,c.exports,e,r,i,n)}return i[o].exports}for(var a="function"==typeof t&&t,o=0;o<n.length;o++)s(n[o]);return s}({1:[function(t,e,r){var i=t("./utils"),n=t("./support"),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(t){for(var e,r,n,a,o,h,l,p=[],c=0,f=t.length,u=f,d="string"!==i.getTypeOf(t);c<t.length;)u=f-c,n=d?(e=t[c++],r=c<f?t[c++]:0,c<f?t[c++]:0):(e=t.charCodeAt(c++),r=c<f?t.charCodeAt(c++):0,c<f?t.charCodeAt(c++):0),a=e>>2,o=(3&e)<<4|r>>4,h=1<u?(15&r)<<2|n>>6:64,l=2<u?63&n:64,p.push(s.charAt(a)+s.charAt(o)+s.charAt(h)+s.charAt(l));return p.join("")},r.decode=function(t){var e,r,i,a,o,h,l=0,p=0;if("data:"===t.substr(0,"data:".length))throw new Error("Invalid base64 input, it looks like a data url.");var c,f=3*(t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"")).length/4;if(t.charAt(t.length-1)===s.charAt(64)&&f--,t.charAt(t.length-2)===s.charAt(64)&&f--,f%1!=0)throw new Error("Invalid base64 input, bad content length.");for(c=n.uint8array?new Uint8Array(0|f):new Array(0|f);l<t.length;)e=s.indexOf(t.charAt(l++))<<2|(a=s.indexOf(t.charAt(l++)))>>4,r=(15&a)<<4|(o=s.indexOf(t.charAt(l++)))>>2,i=(3&o)<<6|(h=s.indexOf(t.charAt(l++))),c[p++]=e,64!==o&&(c[p++]=r),64!==h&&(c[p++]=i);return c}},{"./support":30,"./utils":32}],2:[function(t,e,r){var i=t("./external"),n=t("./stream/DataWorker"),s=t("./stream/Crc32Probe"),a=t("./stream/DataLengthProbe");function o(t,e,r,i,n){this.compressedSize=t,this.uncompressedSize=e,this.crc32=r,this.compression=i,this.compressedContent=n}o.prototype={getContentWorker:function(){var t=new n(i.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new a("data_length")),e=this;return t.on("end",(function(){if(this.streamInfo.data_length!==e.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")})),t},getCompressedWorker:function(){return new n(i.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},o.createWorkerFrom=function(t,e,r){return t.pipe(new s).pipe(new a("uncompressedSize")).pipe(e.compressWorker(r)).pipe(new a("compressedSize")).withStreamInfo("compression",e)},e.exports=o},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(t,e,r){var i=t("./stream/GenericWorker");r.STORE={magic:"\0\0",compressWorker:function(t){return new i("STORE compression")},uncompressWorker:function(){return new i("STORE decompression")}},r.DEFLATE=t("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(t,e,r){var i=t("./utils"),n=function(){for(var t,e=[],r=0;r<256;r++){t=r;for(var i=0;i<8;i++)t=1&t?3988292384^t>>>1:t>>>1;e[r]=t}return e}();e.exports=function(t,e){return void 0!==t&&t.length?"string"!==i.getTypeOf(t)?function(t,e,r){var i=n,s=0+r;t^=-1;for(var a=0;a<s;a++)t=t>>>8^i[255&(t^e[a])];return-1^t}(0|e,t,t.length):function(t,e,r){var i=n,s=0+r;t^=-1;for(var a=0;a<s;a++)t=t>>>8^i[255&(t^e.charCodeAt(a))];return-1^t}(0|e,t,t.length):0}},{"./utils":32}],5:[function(t,e,r){r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(t,e,r){var i;i="undefined"!=typeof Promise?Promise:t("lie"),e.exports={Promise:i}},{lie:37}],7:[function(t,e,r){var i="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,n=t("pako"),s=t("./utils"),a=t("./stream/GenericWorker"),o=i?"uint8array":"array";function h(t,e){a.call(this,"FlateWorker/"+t),this._pako=null,this._pakoAction=t,this._pakoOptions=e,this.meta={}}r.magic="\b\0",s.inherits(h,a),h.prototype.processChunk=function(t){this.meta=t.meta,null===this._pako&&this._createPako(),this._pako.push(s.transformTo(o,t.data),!1)},h.prototype.flush=function(){a.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},h.prototype.cleanUp=function(){a.prototype.cleanUp.call(this),this._pako=null},h.prototype._createPako=function(){this._pako=new n[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var t=this;this._pako.onData=function(e){t.push({data:e,meta:t.meta})}},r.compressWorker=function(t){return new h("Deflate",t)},r.uncompressWorker=function(){return new h("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(t,e,r){function i(t,e){var r,i="";for(r=0;r<e;r++)i+=String.fromCharCode(255&t),t>>>=8;return i}function n(t,e,r,n,a,p){var c,f,u=t.file,d=t.compression,m=p!==o.utf8encode,y=s.transformTo("string",p(u.name)),g=s.transformTo("string",o.utf8encode(u.name)),v=u.comment,_=s.transformTo("string",p(v)),b=s.transformTo("string",o.utf8encode(v)),P=g.length!==u.name.length,S=b.length!==v.length,w="",k="",x="",E=u.dir,A=u.date,C={crc32:0,compressedSize:0,uncompressedSize:0};e&&!r||(C.crc32=t.crc32,C.compressedSize=t.compressedSize,C.uncompressedSize=t.uncompressedSize);var T=0;e&&(T|=8),m||!P&&!S||(T|=2048);var D,I=0,F=0;E&&(I|=16),"UNIX"===a?(F=798,I|=((D=u.unixPermissions)||(D=E?16893:33204),(65535&D)<<16)):(F=20,I|=63&(u.dosPermissions||0)),c=A.getUTCHours(),c<<=6,c|=A.getUTCMinutes(),c<<=5,c|=A.getUTCSeconds()/2,f=A.getUTCFullYear()-1980,f<<=4,f|=A.getUTCMonth()+1,f<<=5,f|=A.getUTCDate(),P&&(w+="up"+i((k=i(1,1)+i(h(y),4)+g).length,2)+k),S&&(w+="uc"+i((x=i(1,1)+i(h(_),4)+b).length,2)+x);var M="";return M+="\n\0",M+=i(T,2),M+=d.magic,M+=i(c,2),M+=i(f,2),M+=i(C.crc32,4),M+=i(C.compressedSize,4),M+=i(C.uncompressedSize,4),M+=i(y.length,2),M+=i(w.length,2),{fileRecord:l.LOCAL_FILE_HEADER+M+y+w,dirRecord:l.CENTRAL_FILE_HEADER+i(F,2)+M+i(_.length,2)+"\0\0\0\0"+i(I,4)+i(n,4)+y+w+_}}var s=t("../utils"),a=t("../stream/GenericWorker"),o=t("../utf8"),h=t("../crc32"),l=t("../signature");function p(t,e,r,i){a.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=e,this.zipPlatform=r,this.encodeFileName=i,this.streamFiles=t,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}s.inherits(p,a),p.prototype.push=function(t){var e=t.meta.percent||0,r=this.entriesCount,i=this._sources.length;this.accumulate?this.contentBuffer.push(t):(this.bytesWritten+=t.data.length,a.prototype.push.call(this,{data:t.data,meta:{currentFile:this.currentFile,percent:r?(e+100*(r-i-1))/r:100}}))},p.prototype.openedSource=function(t){this.currentSourceOffset=this.bytesWritten,this.currentFile=t.file.name;var e=this.streamFiles&&!t.file.dir;if(e){var r=n(t,e,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},p.prototype.closedSource=function(t){this.accumulate=!1;var e,r=this.streamFiles&&!t.file.dir,s=n(t,r,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(s.dirRecord),r)this.push({data:(e=t,l.DATA_DESCRIPTOR+i(e.crc32,4)+i(e.compressedSize,4)+i(e.uncompressedSize,4)),meta:{percent:100}});else for(this.push({data:s.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},p.prototype.flush=function(){for(var t=this.bytesWritten,e=0;e<this.dirRecords.length;e++)this.push({data:this.dirRecords[e],meta:{percent:100}});var r,n,a,o,h,p,c=this.bytesWritten-t,f=(r=this.dirRecords.length,n=c,a=t,o=this.zipComment,h=this.encodeFileName,p=s.transformTo("string",h(o)),l.CENTRAL_DIRECTORY_END+"\0\0\0\0"+i(r,2)+i(r,2)+i(n,4)+i(a,4)+i(p.length,2)+p);this.push({data:f,meta:{percent:100}})},p.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},p.prototype.registerPrevious=function(t){this._sources.push(t);var e=this;return t.on("data",(function(t){e.processChunk(t)})),t.on("end",(function(){e.closedSource(e.previous.streamInfo),e._sources.length?e.prepareNextSource():e.end()})),t.on("error",(function(t){e.error(t)})),this},p.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},p.prototype.error=function(t){var e=this._sources;if(!a.prototype.error.call(this,t))return!1;for(var r=0;r<e.length;r++)try{e[r].error(t)}catch(t){}return!0},p.prototype.lock=function(){a.prototype.lock.call(this);for(var t=this._sources,e=0;e<t.length;e++)t[e].lock()},e.exports=p},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(t,e,r){var i=t("../compressions"),n=t("./ZipFileWorker");r.generateWorker=function(t,e,r){var s=new n(e.streamFiles,r,e.platform,e.encodeFileName),a=0;try{t.forEach((function(t,r){a++;var n=function(t,e){var r=t||e,n=i[r];if(!n)throw new Error(r+" is not a valid compression method !");return n}(r.options.compression,e.compression),o=r.options.compressionOptions||e.compressionOptions||{},h=r.dir,l=r.date;r._compressWorker(n,o).withStreamInfo("file",{name:t,dir:h,date:l,comment:r.comment||"",unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions}).pipe(s)})),s.entriesCount=a}catch(t){s.error(t)}return s}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(t,e,r){function i(){if(!(this instanceof i))return new i;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files={},this.comment=null,this.root="",this.clone=function(){var t=new i;for(var e in this)"function"!=typeof this[e]&&(t[e]=this[e]);return t}}(i.prototype=t("./object")).loadAsync=t("./load"),i.support=t("./support"),i.defaults=t("./defaults"),i.version="3.5.0",i.loadAsync=function(t,e){return(new i).loadAsync(t,e)},i.external=t("./external"),e.exports=i},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(t,e,r){var i=t("./utils"),n=t("./external"),s=t("./utf8"),a=t("./zipEntries"),o=t("./stream/Crc32Probe"),h=t("./nodejsUtils");function l(t){return new n.Promise((function(e,r){var i=t.decompressed.getContentWorker().pipe(new o);i.on("error",(function(t){r(t)})).on("end",(function(){i.streamInfo.crc32!==t.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):e()})).resume()}))}e.exports=function(t,e){var r=this;return e=i.extend(e||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:s.utf8decode}),h.isNode&&h.isStream(t)?n.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):i.prepareContent("the loaded zip file",t,!0,e.optimizedBinaryString,e.base64).then((function(t){var r=new a(e);return r.load(t),r})).then((function(t){var r=[n.Promise.resolve(t)],i=t.files;if(e.checkCRC32)for(var s=0;s<i.length;s++)r.push(l(i[s]));return n.Promise.all(r)})).then((function(t){for(var i=t.shift(),n=i.files,s=0;s<n.length;s++){var a=n[s];r.file(a.fileNameStr,a.decompressed,{binary:!0,optimizedBinaryString:!0,date:a.date,dir:a.dir,comment:a.fileCommentStr.length?a.fileCommentStr:null,unixPermissions:a.unixPermissions,dosPermissions:a.dosPermissions,createFolders:e.createFolders})}return i.zipComment.length&&(r.comment=i.zipComment),r}))}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(t,e,r){var i=t("../utils"),n=t("../stream/GenericWorker");function s(t,e){n.call(this,"Nodejs stream input adapter for "+t),this._upstreamEnded=!1,this._bindStream(e)}i.inherits(s,n),s.prototype._bindStream=function(t){var e=this;(this._stream=t).pause(),t.on("data",(function(t){e.push({data:t,meta:{percent:0}})})).on("error",(function(t){e.isPaused?this.generatedError=t:e.error(t)})).on("end",(function(){e.isPaused?e._upstreamEnded=!0:e.end()}))},s.prototype.pause=function(){return!!n.prototype.pause.call(this)&&(this._stream.pause(),!0)},s.prototype.resume=function(){return!!n.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},e.exports=s},{"../stream/GenericWorker":28,"../utils":32}],13:[function(t,e,r){var i=t("readable-stream").Readable;function n(t,e,r){i.call(this,e),this._helper=t;var n=this;t.on("data",(function(t,e){n.push(t)||n._helper.pause(),r&&r(e)})).on("error",(function(t){n.emit("error",t)})).on("end",(function(){n.push(null)}))}t("../utils").inherits(n,i),n.prototype._read=function(){this._helper.resume()},e.exports=n},{"../utils":32,"readable-stream":16}],14:[function(t,e,r){e.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(t,e){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(t,e);if("number"==typeof t)throw new Error('The "data" argument must not be a number');return new Buffer(t,e)},allocBuffer:function(t){if(Buffer.alloc)return Buffer.alloc(t);var e=new Buffer(t);return e.fill(0),e},isBuffer:function(t){return Buffer.isBuffer(t)},isStream:function(t){return t&&"function"==typeof t.on&&"function"==typeof t.pause&&"function"==typeof t.resume}}},{}],15:[function(t,e,r){function i(t,e,r){var i,s=a.getTypeOf(e),h=a.extend(r||{},l);h.date=h.date||new Date,null!==h.compression&&(h.compression=h.compression.toUpperCase()),"string"==typeof h.unixPermissions&&(h.unixPermissions=parseInt(h.unixPermissions,8)),h.unixPermissions&&16384&h.unixPermissions&&(h.dir=!0),h.dosPermissions&&16&h.dosPermissions&&(h.dir=!0),h.dir&&(t=n(t)),h.createFolders&&(i=function(t){"/"===t.slice(-1)&&(t=t.substring(0,t.length-1));var e=t.lastIndexOf("/");return 0<e?t.substring(0,e):""}(t))&&m.call(this,i,!0);var f,y="string"===s&&!1===h.binary&&!1===h.base64;r&&void 0!==r.binary||(h.binary=!y),(e instanceof p&&0===e.uncompressedSize||h.dir||!e||0===e.length)&&(h.base64=!1,h.binary=!0,e="",h.compression="STORE",s="string"),f=e instanceof p||e instanceof o?e:u.isNode&&u.isStream(e)?new d(t,e):a.prepareContent(t,e,h.binary,h.optimizedBinaryString,h.base64);var g=new c(t,f,h);this.files[t]=g}function n(t){return"/"!==t.slice(-1)&&(t+="/"),t}var s=t("./utf8"),a=t("./utils"),o=t("./stream/GenericWorker"),h=t("./stream/StreamHelper"),l=t("./defaults"),p=t("./compressedObject"),c=t("./zipObject"),f=t("./generate"),u=t("./nodejsUtils"),d=t("./nodejs/NodejsStreamInputAdapter"),m=function(t,e){return e=void 0!==e?e:l.createFolders,t=n(t),this.files[t]||i.call(this,t,null,{dir:!0,createFolders:e}),this.files[t]};function y(t){return"[object RegExp]"===Object.prototype.toString.call(t)}var g={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(t){var e,r,i;for(e in this.files)this.files.hasOwnProperty(e)&&(i=this.files[e],(r=e.slice(this.root.length,e.length))&&e.slice(0,this.root.length)===this.root&&t(r,i))},filter:function(t){var e=[];return this.forEach((function(r,i){t(r,i)&&e.push(i)})),e},file:function(t,e,r){if(1!==arguments.length)return t=this.root+t,i.call(this,t,e,r),this;if(y(t)){var n=t;return this.filter((function(t,e){return!e.dir&&n.test(t)}))}var s=this.files[this.root+t];return s&&!s.dir?s:null},folder:function(t){if(!t)return this;if(y(t))return this.filter((function(e,r){return r.dir&&t.test(e)}));var e=this.root+t,r=m.call(this,e),i=this.clone();return i.root=r.name,i},remove:function(t){t=this.root+t;var e=this.files[t];if(e||("/"!==t.slice(-1)&&(t+="/"),e=this.files[t]),e&&!e.dir)delete this.files[t];else for(var r=this.filter((function(e,r){return r.name.slice(0,t.length)===t})),i=0;i<r.length;i++)delete this.files[r[i].name];return this},generate:function(t){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(t){var e,r={};try{if((r=a.extend(t||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:s.utf8encode})).type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),"binarystring"===r.type&&(r.type="string"),!r.type)throw new Error("No output type specified.");a.checkSupport(r.type),"darwin"!==r.platform&&"freebsd"!==r.platform&&"linux"!==r.platform&&"sunos"!==r.platform||(r.platform="UNIX"),"win32"===r.platform&&(r.platform="DOS");var i=r.comment||this.comment||"";e=f.generateWorker(this,r,i)}catch(t){(e=new o("error")).error(t)}return new h(e,r.type||"string",r.mimeType)},generateAsync:function(t,e){return this.generateInternalStream(t).accumulate(e)},generateNodeStream:function(t,e){return(t=t||{}).type||(t.type="nodebuffer"),this.generateInternalStream(t).toNodejsStream(e)}};e.exports=g},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(t,e,r){e.exports=t("stream")},{stream:void 0}],17:[function(t,e,r){var i=t("./DataReader");function n(t){i.call(this,t);for(var e=0;e<this.data.length;e++)t[e]=255&t[e]}t("../utils").inherits(n,i),n.prototype.byteAt=function(t){return this.data[this.zero+t]},n.prototype.lastIndexOfSignature=function(t){for(var e=t.charCodeAt(0),r=t.charCodeAt(1),i=t.charCodeAt(2),n=t.charCodeAt(3),s=this.length-4;0<=s;--s)if(this.data[s]===e&&this.data[s+1]===r&&this.data[s+2]===i&&this.data[s+3]===n)return s-this.zero;return-1},n.prototype.readAndCheckSignature=function(t){var e=t.charCodeAt(0),r=t.charCodeAt(1),i=t.charCodeAt(2),n=t.charCodeAt(3),s=this.readData(4);return e===s[0]&&r===s[1]&&i===s[2]&&n===s[3]},n.prototype.readData=function(t){if(this.checkOffset(t),0===t)return[];var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./DataReader":18}],18:[function(t,e,r){var i=t("../utils");function n(t){this.data=t,this.length=t.length,this.index=0,this.zero=0}n.prototype={checkOffset:function(t){this.checkIndex(this.index+t)},checkIndex:function(t){if(this.length<this.zero+t||t<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+t+"). Corrupted zip ?")},setIndex:function(t){this.checkIndex(t),this.index=t},skip:function(t){this.setIndex(this.index+t)},byteAt:function(t){},readInt:function(t){var e,r=0;for(this.checkOffset(t),e=this.index+t-1;e>=this.index;e--)r=(r<<8)+this.byteAt(e);return this.index+=t,r},readString:function(t){return i.transformTo("string",this.readData(t))},readData:function(t){},lastIndexOfSignature:function(t){},readAndCheckSignature:function(t){},readDate:function(){var t=this.readInt(4);return new Date(Date.UTC(1980+(t>>25&127),(t>>21&15)-1,t>>16&31,t>>11&31,t>>5&63,(31&t)<<1))}},e.exports=n},{"../utils":32}],19:[function(t,e,r){var i=t("./Uint8ArrayReader");function n(t){i.call(this,t)}t("../utils").inherits(n,i),n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(t,e,r){var i=t("./DataReader");function n(t){i.call(this,t)}t("../utils").inherits(n,i),n.prototype.byteAt=function(t){return this.data.charCodeAt(this.zero+t)},n.prototype.lastIndexOfSignature=function(t){return this.data.lastIndexOf(t)-this.zero},n.prototype.readAndCheckSignature=function(t){return t===this.readData(4)},n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./DataReader":18}],21:[function(t,e,r){var i=t("./ArrayReader");function n(t){i.call(this,t)}t("../utils").inherits(n,i),n.prototype.readData=function(t){if(this.checkOffset(t),0===t)return new Uint8Array(0);var e=this.data.subarray(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./ArrayReader":17}],22:[function(t,e,r){var i=t("../utils"),n=t("../support"),s=t("./ArrayReader"),a=t("./StringReader"),o=t("./NodeBufferReader"),h=t("./Uint8ArrayReader");e.exports=function(t){var e=i.getTypeOf(t);return i.checkSupport(e),"string"!==e||n.uint8array?"nodebuffer"===e?new o(t):n.uint8array?new h(i.transformTo("uint8array",t)):new s(i.transformTo("array",t)):new a(t)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(t,e,r){r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],24:[function(t,e,r){var i=t("./GenericWorker"),n=t("../utils");function s(t){i.call(this,"ConvertWorker to "+t),this.destType=t}n.inherits(s,i),s.prototype.processChunk=function(t){this.push({data:n.transformTo(this.destType,t.data),meta:t.meta})},e.exports=s},{"../utils":32,"./GenericWorker":28}],25:[function(t,e,r){var i=t("./GenericWorker"),n=t("../crc32");function s(){i.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}t("../utils").inherits(s,i),s.prototype.processChunk=function(t){this.streamInfo.crc32=n(t.data,this.streamInfo.crc32||0),this.push(t)},e.exports=s},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(t,e,r){var i=t("../utils"),n=t("./GenericWorker");function s(t){n.call(this,"DataLengthProbe for "+t),this.propName=t,this.withStreamInfo(t,0)}i.inherits(s,n),s.prototype.processChunk=function(t){if(t){var e=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=e+t.data.length}n.prototype.processChunk.call(this,t)},e.exports=s},{"../utils":32,"./GenericWorker":28}],27:[function(t,e,r){var i=t("../utils"),n=t("./GenericWorker");function s(t){n.call(this,"DataWorker");var e=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,t.then((function(t){e.dataIsReady=!0,e.data=t,e.max=t&&t.length||0,e.type=i.getTypeOf(t),e.isPaused||e._tickAndRepeat()}),(function(t){e.error(t)}))}i.inherits(s,n),s.prototype.cleanUp=function(){n.prototype.cleanUp.call(this),this.data=null},s.prototype.resume=function(){return!!n.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,i.delay(this._tickAndRepeat,[],this)),!0)},s.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(i.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},s.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var t=null,e=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":t=this.data.substring(this.index,e);break;case"uint8array":t=this.data.subarray(this.index,e);break;case"array":case"nodebuffer":t=this.data.slice(this.index,e)}return this.index=e,this.push({data:t,meta:{percent:this.max?this.index/this.max*100:0}})},e.exports=s},{"../utils":32,"./GenericWorker":28}],28:[function(t,e,r){function i(t){this.name=t||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}i.prototype={push:function(t){this.emit("data",t)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(t){this.emit("error",t)}return!0},error:function(t){return!this.isFinished&&(this.isPaused?this.generatedError=t:(this.isFinished=!0,this.emit("error",t),this.previous&&this.previous.error(t),this.cleanUp()),!0)},on:function(t,e){return this._listeners[t].push(e),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(t,e){if(this._listeners[t])for(var r=0;r<this._listeners[t].length;r++)this._listeners[t][r].call(this,e)},pipe:function(t){return t.registerPrevious(this)},registerPrevious:function(t){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=t.streamInfo,this.mergeStreamInfo(),this.previous=t;var e=this;return t.on("data",(function(t){e.processChunk(t)})),t.on("end",(function(){e.end()})),t.on("error",(function(t){e.error(t)})),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var t=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),t=!0),this.previous&&this.previous.resume(),!t},flush:function(){},processChunk:function(t){this.push(t)},withStreamInfo:function(t,e){return this.extraStreamInfo[t]=e,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var t in this.extraStreamInfo)this.extraStreamInfo.hasOwnProperty(t)&&(this.streamInfo[t]=this.extraStreamInfo[t])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var t="Worker "+this.name;return this.previous?this.previous+" -> "+t:t}},e.exports=i},{}],29:[function(t,e,r){var i=t("../utils"),n=t("./ConvertWorker"),s=t("./GenericWorker"),a=t("../base64"),o=t("../support"),h=t("../external"),l=null;if(o.nodestream)try{l=t("../nodejs/NodejsStreamOutputAdapter")}catch(t){}function p(t,e,r){var a=e;switch(e){case"blob":case"arraybuffer":a="uint8array";break;case"base64":a="string"}try{this._internalType=a,this._outputType=e,this._mimeType=r,i.checkSupport(a),this._worker=t.pipe(new n(a)),t.lock()}catch(t){this._worker=new s("error"),this._worker.error(t)}}p.prototype={accumulate:function(t){return e=this,r=t,new h.Promise((function(t,n){var s=[],o=e._internalType,h=e._outputType,l=e._mimeType;e.on("data",(function(t,e){s.push(t),r&&r(e)})).on("error",(function(t){s=[],n(t)})).on("end",(function(){try{var e=function(t,e,r){switch(t){case"blob":return i.newBlob(i.transformTo("arraybuffer",e),r);case"base64":return a.encode(e);default:return i.transformTo(t,e)}}(h,function(t,e){var r,i=0,n=null,s=0;for(r=0;r<e.length;r++)s+=e[r].length;switch(t){case"string":return e.join("");case"array":return Array.prototype.concat.apply([],e);case"uint8array":for(n=new Uint8Array(s),r=0;r<e.length;r++)n.set(e[r],i),i+=e[r].length;return n;case"nodebuffer":return Buffer.concat(e);default:throw new Error("concat : unsupported type '"+t+"'")}}(o,s),l);t(e)}catch(e){n(e)}s=[]})).resume()}));var e,r},on:function(t,e){var r=this;return"data"===t?this._worker.on(t,(function(t){e.call(r,t.data,t.meta)})):this._worker.on(t,(function(){i.delay(e,arguments,r)})),this},resume:function(){return i.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(t){if(i.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new l(this,{objectMode:"nodebuffer"!==this._outputType},t)}},e.exports=p},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(t,e,r){if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,r.nodebuffer="undefined"!=typeof Buffer,r.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)r.blob=!1;else{var i=new ArrayBuffer(0);try{r.blob=0===new Blob([i],{type:"application/zip"}).size}catch(t){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);n.append(i),r.blob=0===n.getBlob("application/zip").size}catch(t){r.blob=!1}}}try{r.nodestream=!!t("readable-stream").Readable}catch(t){r.nodestream=!1}},{"readable-stream":16}],31:[function(t,e,r){for(var i=t("./utils"),n=t("./support"),s=t("./nodejsUtils"),a=t("./stream/GenericWorker"),o=new Array(256),h=0;h<256;h++)o[h]=252<=h?6:248<=h?5:240<=h?4:224<=h?3:192<=h?2:1;function l(){a.call(this,"utf-8 decode"),this.leftOver=null}function p(){a.call(this,"utf-8 encode")}o[254]=o[254]=1,r.utf8encode=function(t){return n.nodebuffer?s.newBufferFrom(t,"utf-8"):function(t){var e,r,i,s,a,o=t.length,h=0;for(s=0;s<o;s++)55296==(64512&(r=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(i=t.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(i-56320),s++),h+=r<128?1:r<2048?2:r<65536?3:4;for(e=n.uint8array?new Uint8Array(h):new Array(h),s=a=0;a<h;s++)55296==(64512&(r=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(i=t.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(i-56320),s++),r<128?e[a++]=r:(r<2048?e[a++]=192|r>>>6:(r<65536?e[a++]=224|r>>>12:(e[a++]=240|r>>>18,e[a++]=128|r>>>12&63),e[a++]=128|r>>>6&63),e[a++]=128|63&r);return e}(t)},r.utf8decode=function(t){return n.nodebuffer?i.transformTo("nodebuffer",t).toString("utf-8"):function(t){var e,r,n,s,a=t.length,h=new Array(2*a);for(e=r=0;e<a;)if((n=t[e++])<128)h[r++]=n;else if(4<(s=o[n]))h[r++]=65533,e+=s-1;else{for(n&=2===s?31:3===s?15:7;1<s&&e<a;)n=n<<6|63&t[e++],s--;1<s?h[r++]=65533:n<65536?h[r++]=n:(n-=65536,h[r++]=55296|n>>10&1023,h[r++]=56320|1023&n)}return h.length!==r&&(h.subarray?h=h.subarray(0,r):h.length=r),i.applyFromCharCode(h)}(t=i.transformTo(n.uint8array?"uint8array":"array",t))},i.inherits(l,a),l.prototype.processChunk=function(t){var e=i.transformTo(n.uint8array?"uint8array":"array",t.data);if(this.leftOver&&this.leftOver.length){if(n.uint8array){var s=e;(e=new Uint8Array(s.length+this.leftOver.length)).set(this.leftOver,0),e.set(s,this.leftOver.length)}else e=this.leftOver.concat(e);this.leftOver=null}var a=function(t,e){var r;for((e=e||t.length)>t.length&&(e=t.length),r=e-1;0<=r&&128==(192&t[r]);)r--;return r<0||0===r?e:r+o[t[r]]>e?r:e}(e),h=e;a!==e.length&&(n.uint8array?(h=e.subarray(0,a),this.leftOver=e.subarray(a,e.length)):(h=e.slice(0,a),this.leftOver=e.slice(a,e.length))),this.push({data:r.utf8decode(h),meta:t.meta})},l.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:r.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},r.Utf8DecodeWorker=l,i.inherits(p,a),p.prototype.processChunk=function(t){this.push({data:r.utf8encode(t.data),meta:t.meta})},r.Utf8EncodeWorker=p},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(t,e,r){var i=t("./support"),n=t("./base64"),s=t("./nodejsUtils"),a=t("set-immediate-shim"),o=t("./external");function h(t){return t}function l(t,e){for(var r=0;r<t.length;++r)e[r]=255&t.charCodeAt(r);return e}r.newBlob=function(t,e){r.checkSupport("blob");try{return new Blob([t],{type:e})}catch(r){try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return i.append(t),i.getBlob(e)}catch(t){throw new Error("Bug : can't construct the Blob.")}}};var p={stringifyByChunk:function(t,e,r){var i=[],n=0,s=t.length;if(s<=r)return String.fromCharCode.apply(null,t);for(;n<s;)"array"===e||"nodebuffer"===e?i.push(String.fromCharCode.apply(null,t.slice(n,Math.min(n+r,s)))):i.push(String.fromCharCode.apply(null,t.subarray(n,Math.min(n+r,s)))),n+=r;return i.join("")},stringifyByChar:function(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},applyCanBeUsed:{uint8array:function(){try{return i.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(t){return!1}}(),nodebuffer:function(){try{return i.nodebuffer&&1===String.fromCharCode.apply(null,s.allocBuffer(1)).length}catch(t){return!1}}()}};function c(t){var e=65536,i=r.getTypeOf(t),n=!0;if("uint8array"===i?n=p.applyCanBeUsed.uint8array:"nodebuffer"===i&&(n=p.applyCanBeUsed.nodebuffer),n)for(;1<e;)try{return p.stringifyByChunk(t,i,e)}catch(t){e=Math.floor(e/2)}return p.stringifyByChar(t)}function f(t,e){for(var r=0;r<t.length;r++)e[r]=t[r];return e}r.applyFromCharCode=c;var u={};u.string={string:h,array:function(t){return l(t,new Array(t.length))},arraybuffer:function(t){return u.string.uint8array(t).buffer},uint8array:function(t){return l(t,new Uint8Array(t.length))},nodebuffer:function(t){return l(t,s.allocBuffer(t.length))}},u.array={string:c,array:h,arraybuffer:function(t){return new Uint8Array(t).buffer},uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s.newBufferFrom(t)}},u.arraybuffer={string:function(t){return c(new Uint8Array(t))},array:function(t){return f(new Uint8Array(t),new Array(t.byteLength))},arraybuffer:h,uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s.newBufferFrom(new Uint8Array(t))}},u.uint8array={string:c,array:function(t){return f(t,new Array(t.length))},arraybuffer:function(t){return t.buffer},uint8array:h,nodebuffer:function(t){return s.newBufferFrom(t)}},u.nodebuffer={string:c,array:function(t){return f(t,new Array(t.length))},arraybuffer:function(t){return u.nodebuffer.uint8array(t).buffer},uint8array:function(t){return f(t,new Uint8Array(t.length))},nodebuffer:h},r.transformTo=function(t,e){if(e=e||"",!t)return e;r.checkSupport(t);var i=r.getTypeOf(e);return u[i][t](e)},r.getTypeOf=function(t){return"string"==typeof t?"string":"[object Array]"===Object.prototype.toString.call(t)?"array":i.nodebuffer&&s.isBuffer(t)?"nodebuffer":i.uint8array&&t instanceof Uint8Array?"uint8array":i.arraybuffer&&t instanceof ArrayBuffer?"arraybuffer":void 0},r.checkSupport=function(t){if(!i[t.toLowerCase()])throw new Error(t+" is not supported by this platform")},r.MAX_VALUE_16BITS=65535,r.MAX_VALUE_32BITS=-1,r.pretty=function(t){var e,r,i="";for(r=0;r<(t||"").length;r++)i+="\\x"+((e=t.charCodeAt(r))<16?"0":"")+e.toString(16).toUpperCase();return i},r.delay=function(t,e,r){a((function(){t.apply(r||null,e||[])}))},r.inherits=function(t,e){function r(){}r.prototype=e.prototype,t.prototype=new r},r.extend=function(){var t,e,r={};for(t=0;t<arguments.length;t++)for(e in arguments[t])arguments[t].hasOwnProperty(e)&&void 0===r[e]&&(r[e]=arguments[t][e]);return r},r.prepareContent=function(t,e,s,a,h){return o.Promise.resolve(e).then((function(t){return i.blob&&(t instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(t)))&&"undefined"!=typeof FileReader?new o.Promise((function(e,r){var i=new FileReader;i.onload=function(t){e(t.target.result)},i.onerror=function(t){r(t.target.error)},i.readAsArrayBuffer(t)})):t})).then((function(e){var p,c=r.getTypeOf(e);return c?("arraybuffer"===c?e=r.transformTo("uint8array",e):"string"===c&&(h?e=n.decode(e):s&&!0!==a&&(e=l(p=e,i.uint8array?new Uint8Array(p.length):new Array(p.length)))),e):o.Promise.reject(new Error("Can't read the data of '"+t+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))}))}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,"set-immediate-shim":54}],33:[function(t,e,r){var i=t("./reader/readerFor"),n=t("./utils"),s=t("./signature"),a=t("./zipEntry"),o=(t("./utf8"),t("./support"));function h(t){this.files=[],this.loadOptions=t}h.prototype={checkSignature:function(t){if(!this.reader.readAndCheckSignature(t)){this.reader.index-=4;var e=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+n.pretty(e)+", expected "+n.pretty(t)+")")}},isSignature:function(t,e){var r=this.reader.index;this.reader.setIndex(t);var i=this.reader.readString(4)===e;return this.reader.setIndex(r),i},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var t=this.reader.readData(this.zipCommentLength),e=o.uint8array?"uint8array":"array",r=n.transformTo(e,t);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var t,e,r,i=this.zip64EndOfCentralSize-44;0<i;)t=this.reader.readInt(2),e=this.reader.readInt(4),r=this.reader.readData(e),this.zip64ExtensibleData[t]={id:t,length:e,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var t,e;for(t=0;t<this.files.length;t++)e=this.files[t],this.reader.setIndex(e.localHeaderOffset),this.checkSignature(s.LOCAL_FILE_HEADER),e.readLocalPart(this.reader),e.handleUTF8(),e.processAttributes()},readCentralDir:function(){var t;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);)(t=new a({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(t);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var t=this.reader.lastIndexOfSignature(s.CENTRAL_DIRECTORY_END);if(t<0)throw this.isSignature(0,s.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(t);var e=t;if(this.checkSignature(s.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===n.MAX_VALUE_16BITS||this.diskWithCentralDirStart===n.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===n.MAX_VALUE_16BITS||this.centralDirRecords===n.MAX_VALUE_16BITS||this.centralDirSize===n.MAX_VALUE_32BITS||this.centralDirOffset===n.MAX_VALUE_32BITS){if(this.zip64=!0,(t=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(t),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,s.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var r=this.centralDirOffset+this.centralDirSize;this.zip64&&(r+=20,r+=12+this.zip64EndOfCentralSize);var i=e-r;if(0<i)this.isSignature(e,s.CENTRAL_FILE_HEADER)||(this.reader.zero=i);else if(i<0)throw new Error("Corrupted zip: missing "+Math.abs(i)+" bytes.")},prepareReader:function(t){this.reader=i(t)},load:function(t){this.prepareReader(t),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},e.exports=h},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utf8":31,"./utils":32,"./zipEntry":34}],34:[function(t,e,r){var i=t("./reader/readerFor"),n=t("./utils"),s=t("./compressedObject"),a=t("./crc32"),o=t("./utf8"),h=t("./compressions"),l=t("./support");function p(t,e){this.options=t,this.loadOptions=e}p.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(t){var e,r;if(t.skip(22),this.fileNameLength=t.readInt(2),r=t.readInt(2),this.fileName=t.readData(this.fileNameLength),t.skip(r),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(e=function(t){for(var e in h)if(h.hasOwnProperty(e)&&h[e].magic===t)return h[e];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+n.pretty(this.compressionMethod)+" unknown (inner file : "+n.transformTo("string",this.fileName)+")");this.decompressed=new s(this.compressedSize,this.uncompressedSize,this.crc32,e,t.readData(this.compressedSize))},readCentralPart:function(t){this.versionMadeBy=t.readInt(2),t.skip(2),this.bitFlag=t.readInt(2),this.compressionMethod=t.readString(2),this.date=t.readDate(),this.crc32=t.readInt(4),this.compressedSize=t.readInt(4),this.uncompressedSize=t.readInt(4);var e=t.readInt(2);if(this.extraFieldsLength=t.readInt(2),this.fileCommentLength=t.readInt(2),this.diskNumberStart=t.readInt(2),this.internalFileAttributes=t.readInt(2),this.externalFileAttributes=t.readInt(4),this.localHeaderOffset=t.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");t.skip(e),this.readExtraFields(t),this.parseZIP64ExtraField(t),this.fileComment=t.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var t=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==t&&(this.dosPermissions=63&this.externalFileAttributes),3==t&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(t){if(this.extraFields[1]){var e=i(this.extraFields[1].value);this.uncompressedSize===n.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===n.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===n.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===n.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(t){var e,r,i,n=t.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});t.index+4<n;)e=t.readInt(2),r=t.readInt(2),i=t.readData(r),this.extraFields[e]={id:e,length:r,value:i};t.setIndex(n)},handleUTF8:function(){var t=l.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=o.utf8decode(this.fileName),this.fileCommentStr=o.utf8decode(this.fileComment);else{var e=this.findExtraFieldUnicodePath();if(null!==e)this.fileNameStr=e;else{var r=n.transformTo(t,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var i=this.findExtraFieldUnicodeComment();if(null!==i)this.fileCommentStr=i;else{var s=n.transformTo(t,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(s)}}},findExtraFieldUnicodePath:function(){var t=this.extraFields[28789];if(t){var e=i(t.value);return 1!==e.readInt(1)||a(this.fileName)!==e.readInt(4)?null:o.utf8decode(e.readData(t.length-5))}return null},findExtraFieldUnicodeComment:function(){var t=this.extraFields[25461];if(t){var e=i(t.value);return 1!==e.readInt(1)||a(this.fileComment)!==e.readInt(4)?null:o.utf8decode(e.readData(t.length-5))}return null}},e.exports=p},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(t,e,r){function i(t,e,r){this.name=t,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=e,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}}var n=t("./stream/StreamHelper"),s=t("./stream/DataWorker"),a=t("./utf8"),o=t("./compressedObject"),h=t("./stream/GenericWorker");i.prototype={internalStream:function(t){var e=null,r="string";try{if(!t)throw new Error("No output type specified.");var i="string"===(r=t.toLowerCase())||"text"===r;"binarystring"!==r&&"text"!==r||(r="string"),e=this._decompressWorker();var s=!this._dataBinary;s&&!i&&(e=e.pipe(new a.Utf8EncodeWorker)),!s&&i&&(e=e.pipe(new a.Utf8DecodeWorker))}catch(t){(e=new h("error")).error(t)}return new n(e,r,"")},async:function(t,e){return this.internalStream(t).accumulate(e)},nodeStream:function(t,e){return this.internalStream(t||"nodebuffer").toNodejsStream(e)},_compressWorker:function(t,e){if(this._data instanceof o&&this._data.compression.magic===t.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new a.Utf8EncodeWorker)),o.createWorkerFrom(r,t,e)},_decompressWorker:function(){return this._data instanceof o?this._data.getContentWorker():this._data instanceof h?this._data:new s(this._data)}};for(var l=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],p=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},c=0;c<l.length;c++)i.prototype[l[c]]=p;e.exports=i},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(t,e,r){(function(t){var r,i,n=t.MutationObserver||t.WebKitMutationObserver;if(n){var s=0,a=new n(p),o=t.document.createTextNode("");a.observe(o,{characterData:!0}),r=function(){o.data=s=++s%2}}else if(t.setImmediate||void 0===t.MessageChannel)r="document"in t&&"onreadystatechange"in t.document.createElement("script")?function(){var e=t.document.createElement("script");e.onreadystatechange=function(){p(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},t.document.documentElement.appendChild(e)}:function(){setTimeout(p,0)};else{var h=new t.MessageChannel;h.port1.onmessage=p,r=function(){h.port2.postMessage(0)}}var l=[];function p(){var t,e;i=!0;for(var r=l.length;r;){for(e=l,l=[],t=-1;++t<r;)e[t]();r=l.length}i=!1}e.exports=function(t){1!==l.push(t)||i||r()}}).call(this,void 0!==i?i:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(t,e,r){var i=t("immediate");function n(){}var s={},a=["REJECTED"],o=["FULFILLED"],h=["PENDING"];function l(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=h,this.queue=[],this.outcome=void 0,t!==n&&u(this,t)}function p(t,e,r){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function c(t,e,r){i((function(){var i;try{i=e(r)}catch(i){return s.reject(t,i)}i===t?s.reject(t,new TypeError("Cannot resolve promise with itself")):s.resolve(t,i)}))}function f(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function u(t,e){var r=!1;function i(e){r||(r=!0,s.reject(t,e))}function n(e){r||(r=!0,s.resolve(t,e))}var a=d((function(){e(n,i)}));"error"===a.status&&i(a.value)}function d(t,e){var r={};try{r.value=t(e),r.status="success"}catch(t){r.status="error",r.value=t}return r}(e.exports=l).prototype.finally=function(t){if("function"!=typeof t)return this;var e=this.constructor;return this.then((function(r){return e.resolve(t()).then((function(){return r}))}),(function(r){return e.resolve(t()).then((function(){throw r}))}))},l.prototype.catch=function(t){return this.then(null,t)},l.prototype.then=function(t,e){if("function"!=typeof t&&this.state===o||"function"!=typeof e&&this.state===a)return this;var r=new this.constructor(n);return this.state!==h?c(r,this.state===o?t:e,this.outcome):this.queue.push(new p(r,t,e)),r},p.prototype.callFulfilled=function(t){s.resolve(this.promise,t)},p.prototype.otherCallFulfilled=function(t){c(this.promise,this.onFulfilled,t)},p.prototype.callRejected=function(t){s.reject(this.promise,t)},p.prototype.otherCallRejected=function(t){c(this.promise,this.onRejected,t)},s.resolve=function(t,e){var r=d(f,e);if("error"===r.status)return s.reject(t,r.value);var i=r.value;if(i)u(t,i);else{t.state=o,t.outcome=e;for(var n=-1,a=t.queue.length;++n<a;)t.queue[n].callFulfilled(e)}return t},s.reject=function(t,e){t.state=a,t.outcome=e;for(var r=-1,i=t.queue.length;++r<i;)t.queue[r].callRejected(e);return t},l.resolve=function(t){return t instanceof this?t:s.resolve(new this(n),t)},l.reject=function(t){var e=new this(n);return s.reject(e,t)},l.all=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var r=t.length,i=!1;if(!r)return this.resolve([]);for(var a=new Array(r),o=0,h=-1,l=new this(n);++h<r;)p(t[h],h);return l;function p(t,n){e.resolve(t).then((function(t){a[n]=t,++o!==r||i||(i=!0,s.resolve(l,a))}),(function(t){i||(i=!0,s.reject(l,t))}))}},l.race=function(t){if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var e=t.length,r=!1;if(!e)return this.resolve([]);for(var i,a=-1,o=new this(n);++a<e;)i=t[a],this.resolve(i).then((function(t){r||(r=!0,s.resolve(o,t))}),(function(t){r||(r=!0,s.reject(o,t))}));return o}},{immediate:36}],38:[function(t,e,r){var i={};(0,t("./lib/utils/common").assign)(i,t("./lib/deflate"),t("./lib/inflate"),t("./lib/zlib/constants")),e.exports=i},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(t,e,r){var i=t("./zlib/deflate"),n=t("./utils/common"),s=t("./utils/strings"),a=t("./zlib/messages"),o=t("./zlib/zstream"),h=Object.prototype.toString;function l(t){if(!(this instanceof l))return new l(t);this.options=n.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});var e=this.options;e.raw&&0<e.windowBits?e.windowBits=-e.windowBits:e.gzip&&0<e.windowBits&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var r=i.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(0!==r)throw new Error(a[r]);if(e.header&&i.deflateSetHeader(this.strm,e.header),e.dictionary){var p;if(p="string"==typeof e.dictionary?s.string2buf(e.dictionary):"[object ArrayBuffer]"===h.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,0!==(r=i.deflateSetDictionary(this.strm,p)))throw new Error(a[r]);this._dict_set=!0}}function p(t,e){var r=new l(e);if(r.push(t,!0),r.err)throw r.msg||a[r.err];return r.result}l.prototype.push=function(t,e){var r,a,o=this.strm,l=this.options.chunkSize;if(this.ended)return!1;a=e===~~e?e:!0===e?4:0,"string"==typeof t?o.input=s.string2buf(t):"[object ArrayBuffer]"===h.call(t)?o.input=new Uint8Array(t):o.input=t,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new n.Buf8(l),o.next_out=0,o.avail_out=l),1!==(r=i.deflate(o,a))&&0!==r)return this.onEnd(r),!(this.ended=!0);0!==o.avail_out&&(0!==o.avail_in||4!==a&&2!==a)||("string"===this.options.to?this.onData(s.buf2binstring(n.shrinkBuf(o.output,o.next_out))):this.onData(n.shrinkBuf(o.output,o.next_out)))}while((0<o.avail_in||0===o.avail_out)&&1!==r);return 4===a?(r=i.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,0===r):2!==a||(this.onEnd(0),!(o.avail_out=0))},l.prototype.onData=function(t){this.chunks.push(t)},l.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Deflate=l,r.deflate=p,r.deflateRaw=function(t,e){return(e=e||{}).raw=!0,p(t,e)},r.gzip=function(t,e){return(e=e||{}).gzip=!0,p(t,e)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(t,e,r){var i=t("./zlib/inflate"),n=t("./utils/common"),s=t("./utils/strings"),a=t("./zlib/constants"),o=t("./zlib/messages"),h=t("./zlib/zstream"),l=t("./zlib/gzheader"),p=Object.prototype.toString;function c(t){if(!(this instanceof c))return new c(t);this.options=n.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options;e.raw&&0<=e.windowBits&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(0<=e.windowBits&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),15<e.windowBits&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new h,this.strm.avail_out=0;var r=i.inflateInit2(this.strm,e.windowBits);if(r!==a.Z_OK)throw new Error(o[r]);this.header=new l,i.inflateGetHeader(this.strm,this.header)}function f(t,e){var r=new c(e);if(r.push(t,!0),r.err)throw r.msg||o[r.err];return r.result}c.prototype.push=function(t,e){var r,o,h,l,c,f,u=this.strm,d=this.options.chunkSize,m=this.options.dictionary,y=!1;if(this.ended)return!1;o=e===~~e?e:!0===e?a.Z_FINISH:a.Z_NO_FLUSH,"string"==typeof t?u.input=s.binstring2buf(t):"[object ArrayBuffer]"===p.call(t)?u.input=new Uint8Array(t):u.input=t,u.next_in=0,u.avail_in=u.input.length;do{if(0===u.avail_out&&(u.output=new n.Buf8(d),u.next_out=0,u.avail_out=d),(r=i.inflate(u,a.Z_NO_FLUSH))===a.Z_NEED_DICT&&m&&(f="string"==typeof m?s.string2buf(m):"[object ArrayBuffer]"===p.call(m)?new Uint8Array(m):m,r=i.inflateSetDictionary(this.strm,f)),r===a.Z_BUF_ERROR&&!0===y&&(r=a.Z_OK,y=!1),r!==a.Z_STREAM_END&&r!==a.Z_OK)return this.onEnd(r),!(this.ended=!0);u.next_out&&(0!==u.avail_out&&r!==a.Z_STREAM_END&&(0!==u.avail_in||o!==a.Z_FINISH&&o!==a.Z_SYNC_FLUSH)||("string"===this.options.to?(h=s.utf8border(u.output,u.next_out),l=u.next_out-h,c=s.buf2string(u.output,h),u.next_out=l,u.avail_out=d-l,l&&n.arraySet(u.output,u.output,h,l,0),this.onData(c)):this.onData(n.shrinkBuf(u.output,u.next_out)))),0===u.avail_in&&0===u.avail_out&&(y=!0)}while((0<u.avail_in||0===u.avail_out)&&r!==a.Z_STREAM_END);return r===a.Z_STREAM_END&&(o=a.Z_FINISH),o===a.Z_FINISH?(r=i.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===a.Z_OK):o!==a.Z_SYNC_FLUSH||(this.onEnd(a.Z_OK),!(u.avail_out=0))},c.prototype.onData=function(t){this.chunks.push(t)},c.prototype.onEnd=function(t){t===a.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Inflate=c,r.inflate=f,r.inflateRaw=function(t,e){return(e=e||{}).raw=!0,f(t,e)},r.ungzip=f},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(t,e,r){var i="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var r=e.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var i in r)r.hasOwnProperty(i)&&(t[i]=r[i])}}return t},r.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var n={arraySet:function(t,e,r,i,n){if(e.subarray&&t.subarray)t.set(e.subarray(r,r+i),n);else for(var s=0;s<i;s++)t[n+s]=e[r+s]},flattenChunks:function(t){var e,r,i,n,s,a;for(e=i=0,r=t.length;e<r;e++)i+=t[e].length;for(a=new Uint8Array(i),e=n=0,r=t.length;e<r;e++)s=t[e],a.set(s,n),n+=s.length;return a}},s={arraySet:function(t,e,r,i,n){for(var s=0;s<i;s++)t[n+s]=e[r+s]},flattenChunks:function(t){return[].concat.apply([],t)}};r.setTyped=function(t){t?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,n)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,s))},r.setTyped(i)},{}],42:[function(t,e,r){var i=t("./common"),n=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(t){n=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){s=!1}for(var a=new i.Buf8(256),o=0;o<256;o++)a[o]=252<=o?6:248<=o?5:240<=o?4:224<=o?3:192<=o?2:1;function h(t,e){if(e<65537&&(t.subarray&&s||!t.subarray&&n))return String.fromCharCode.apply(null,i.shrinkBuf(t,e));for(var r="",a=0;a<e;a++)r+=String.fromCharCode(t[a]);return r}a[254]=a[254]=1,r.string2buf=function(t){var e,r,n,s,a,o=t.length,h=0;for(s=0;s<o;s++)55296==(64512&(r=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=t.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(n-56320),s++),h+=r<128?1:r<2048?2:r<65536?3:4;for(e=new i.Buf8(h),s=a=0;a<h;s++)55296==(64512&(r=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=t.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(n-56320),s++),r<128?e[a++]=r:(r<2048?e[a++]=192|r>>>6:(r<65536?e[a++]=224|r>>>12:(e[a++]=240|r>>>18,e[a++]=128|r>>>12&63),e[a++]=128|r>>>6&63),e[a++]=128|63&r);return e},r.buf2binstring=function(t){return h(t,t.length)},r.binstring2buf=function(t){for(var e=new i.Buf8(t.length),r=0,n=e.length;r<n;r++)e[r]=t.charCodeAt(r);return e},r.buf2string=function(t,e){var r,i,n,s,o=e||t.length,l=new Array(2*o);for(r=i=0;r<o;)if((n=t[r++])<128)l[i++]=n;else if(4<(s=a[n]))l[i++]=65533,r+=s-1;else{for(n&=2===s?31:3===s?15:7;1<s&&r<o;)n=n<<6|63&t[r++],s--;1<s?l[i++]=65533:n<65536?l[i++]=n:(n-=65536,l[i++]=55296|n>>10&1023,l[i++]=56320|1023&n)}return h(l,i)},r.utf8border=function(t,e){var r;for((e=e||t.length)>t.length&&(e=t.length),r=e-1;0<=r&&128==(192&t[r]);)r--;return r<0||0===r?e:r+a[t[r]]>e?r:e}},{"./common":41}],43:[function(t,e,r){e.exports=function(t,e,r,i){for(var n=65535&t|0,s=t>>>16&65535|0,a=0;0!==r;){for(r-=a=2e3<r?2e3:r;s=s+(n=n+e[i++]|0)|0,--a;);n%=65521,s%=65521}return n|s<<16|0}},{}],44:[function(t,e,r){e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(t,e,r){var i=function(){for(var t,e=[],r=0;r<256;r++){t=r;for(var i=0;i<8;i++)t=1&t?3988292384^t>>>1:t>>>1;e[r]=t}return e}();e.exports=function(t,e,r,n){var s=i,a=n+r;t^=-1;for(var o=n;o<a;o++)t=t>>>8^s[255&(t^e[o])];return-1^t}},{}],46:[function(t,e,r){var i,n=t("../utils/common"),s=t("./trees"),a=t("./adler32"),o=t("./crc32"),h=t("./messages"),l=-2,p=258,c=262,f=113;function u(t,e){return t.msg=h[e],e}function d(t){return(t<<1)-(4<t?9:0)}function m(t){for(var e=t.length;0<=--e;)t[e]=0}function y(t){var e=t.state,r=e.pending;r>t.avail_out&&(r=t.avail_out),0!==r&&(n.arraySet(t.output,e.pending_buf,e.pending_out,r,t.next_out),t.next_out+=r,e.pending_out+=r,t.total_out+=r,t.avail_out-=r,e.pending-=r,0===e.pending&&(e.pending_out=0))}function g(t,e){s._tr_flush_block(t,0<=t.block_start?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,y(t.strm)}function v(t,e){t.pending_buf[t.pending++]=e}function _(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function b(t,e){var r,i,n=t.max_chain_length,s=t.strstart,a=t.prev_length,o=t.nice_match,h=t.strstart>t.w_size-c?t.strstart-(t.w_size-c):0,l=t.window,f=t.w_mask,u=t.prev,d=t.strstart+p,m=l[s+a-1],y=l[s+a];t.prev_length>=t.good_match&&(n>>=2),o>t.lookahead&&(o=t.lookahead);do{if(l[(r=e)+a]===y&&l[r+a-1]===m&&l[r]===l[s]&&l[++r]===l[s+1]){s+=2,r++;do{}while(l[++s]===l[++r]&&l[++s]===l[++r]&&l[++s]===l[++r]&&l[++s]===l[++r]&&l[++s]===l[++r]&&l[++s]===l[++r]&&l[++s]===l[++r]&&l[++s]===l[++r]&&s<d);if(i=p-(d-s),s=d-p,a<i){if(t.match_start=e,o<=(a=i))break;m=l[s+a-1],y=l[s+a]}}}while((e=u[e&f])>h&&0!=--n);return a<=t.lookahead?a:t.lookahead}function P(t){var e,r,i,s,h,l,p,f,u,d,m=t.w_size;do{if(s=t.window_size-t.lookahead-t.strstart,t.strstart>=m+(m-c)){for(n.arraySet(t.window,t.window,m,m,0),t.match_start-=m,t.strstart-=m,t.block_start-=m,e=r=t.hash_size;i=t.head[--e],t.head[e]=m<=i?i-m:0,--r;);for(e=r=m;i=t.prev[--e],t.prev[e]=m<=i?i-m:0,--r;);s+=m}if(0===t.strm.avail_in)break;if(l=t.strm,p=t.window,f=t.strstart+t.lookahead,d=void 0,(u=s)<(d=l.avail_in)&&(d=u),r=0===d?0:(l.avail_in-=d,n.arraySet(p,l.input,l.next_in,d,f),1===l.state.wrap?l.adler=a(l.adler,p,d,f):2===l.state.wrap&&(l.adler=o(l.adler,p,d,f)),l.next_in+=d,l.total_in+=d,d),t.lookahead+=r,t.lookahead+t.insert>=3)for(h=t.strstart-t.insert,t.ins_h=t.window[h],t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+3-1])&t.hash_mask,t.prev[h&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=h,h++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<c&&0!==t.strm.avail_in)}function S(t,e){for(var r,i;;){if(t.lookahead<c){if(P(t),t.lookahead<c&&0===e)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==r&&t.strstart-r<=t.w_size-c&&(t.match_length=b(t,r)),t.match_length>=3)if(i=s._tr_tally(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){for(t.match_length--;t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart,0!=--t.match_length;);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else i=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(i&&(g(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,4===e?(g(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(g(t,!1),0===t.strm.avail_out)?1:2}function w(t,e){for(var r,i,n;;){if(t.lookahead<c){if(P(t),t.lookahead<c&&0===e)return 1;if(0===t.lookahead)break}if(r=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==r&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-c&&(t.match_length=b(t,r),t.match_length<=5&&(1===t.strategy||3===t.match_length&&4096<t.strstart-t.match_start)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){for(n=t.strstart+t.lookahead-3,i=s._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;++t.strstart<=n&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!=--t.prev_length;);if(t.match_available=0,t.match_length=2,t.strstart++,i&&(g(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((i=s._tr_tally(t,0,t.window[t.strstart-1]))&&g(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(i=s._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e?(g(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(g(t,!1),0===t.strm.avail_out)?1:2}function k(t,e,r,i,n){this.good_length=t,this.max_lazy=e,this.nice_length=r,this.max_chain=i,this.func=n}function x(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new n.Buf16(1146),this.dyn_dtree=new n.Buf16(122),this.bl_tree=new n.Buf16(78),m(this.dyn_ltree),m(this.dyn_dtree),m(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new n.Buf16(16),this.heap=new n.Buf16(573),m(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new n.Buf16(573),m(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function E(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=2,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:f,t.adler=2===e.wrap?0:1,e.last_flush=0,s._tr_init(e),0):u(t,l)}function A(t){var e,r=E(t);return 0===r&&((e=t.state).window_size=2*e.w_size,m(e.head),e.max_lazy_match=i[e.level].max_lazy,e.good_match=i[e.level].good_length,e.nice_match=i[e.level].nice_length,e.max_chain_length=i[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),r}function C(t,e,r,i,s,a){if(!t)return l;var o=1;if(-1===e&&(e=6),i<0?(o=0,i=-i):15<i&&(o=2,i-=16),s<1||9<s||8!==r||i<8||15<i||e<0||9<e||a<0||4<a)return u(t,l);8===i&&(i=9);var h=new x;return(t.state=h).strm=t,h.wrap=o,h.gzhead=null,h.w_bits=i,h.w_size=1<<h.w_bits,h.w_mask=h.w_size-1,h.hash_bits=s+7,h.hash_size=1<<h.hash_bits,h.hash_mask=h.hash_size-1,h.hash_shift=~~((h.hash_bits+3-1)/3),h.window=new n.Buf8(2*h.w_size),h.head=new n.Buf16(h.hash_size),h.prev=new n.Buf16(h.w_size),h.lit_bufsize=1<<s+6,h.pending_buf_size=4*h.lit_bufsize,h.pending_buf=new n.Buf8(h.pending_buf_size),h.d_buf=1*h.lit_bufsize,h.l_buf=3*h.lit_bufsize,h.level=e,h.strategy=a,h.method=r,A(t)}i=[new k(0,0,0,0,(function(t,e){var r=65535;for(r>t.pending_buf_size-5&&(r=t.pending_buf_size-5);;){if(t.lookahead<=1){if(P(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var i=t.block_start+r;if((0===t.strstart||t.strstart>=i)&&(t.lookahead=t.strstart-i,t.strstart=i,g(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-c&&(g(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(g(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(g(t,!1),t.strm.avail_out),1)})),new k(4,4,8,4,S),new k(4,5,16,8,S),new k(4,6,32,32,S),new k(4,4,16,16,w),new k(8,16,32,32,w),new k(8,16,128,128,w),new k(8,32,128,256,w),new k(32,128,258,1024,w),new k(32,258,258,4096,w)],r.deflateInit=function(t,e){return C(t,e,8,15,8,0)},r.deflateInit2=C,r.deflateReset=A,r.deflateResetKeep=E,r.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?l:(t.state.gzhead=e,0):l},r.deflate=function(t,e){var r,n,a,h;if(!t||!t.state||5<e||e<0)return t?u(t,l):l;if(n=t.state,!t.output||!t.input&&0!==t.avail_in||666===n.status&&4!==e)return u(t,0===t.avail_out?-5:l);if(n.strm=t,r=n.last_flush,n.last_flush=e,42===n.status)if(2===n.wrap)t.adler=0,v(n,31),v(n,139),v(n,8),n.gzhead?(v(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),v(n,255&n.gzhead.time),v(n,n.gzhead.time>>8&255),v(n,n.gzhead.time>>16&255),v(n,n.gzhead.time>>24&255),v(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),v(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(v(n,255&n.gzhead.extra.length),v(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(t.adler=o(t.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(v(n,0),v(n,0),v(n,0),v(n,0),v(n,0),v(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),v(n,3),n.status=f);else{var c=8+(n.w_bits-8<<4)<<8;c|=(2<=n.strategy||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(c|=32),c+=31-c%31,n.status=f,_(n,c),0!==n.strstart&&(_(n,t.adler>>>16),_(n,65535&t.adler)),t.adler=1}if(69===n.status)if(n.gzhead.extra){for(a=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),y(t),a=n.pending,n.pending!==n.pending_buf_size));)v(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){a=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),y(t),a=n.pending,n.pending===n.pending_buf_size)){h=1;break}h=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,v(n,h)}while(0!==h);n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),0===h&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){a=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),y(t),a=n.pending,n.pending===n.pending_buf_size)){h=1;break}h=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,v(n,h)}while(0!==h);n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),0===h&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&y(t),n.pending+2<=n.pending_buf_size&&(v(n,255&t.adler),v(n,t.adler>>8&255),t.adler=0,n.status=f)):n.status=f),0!==n.pending){if(y(t),0===t.avail_out)return n.last_flush=-1,0}else if(0===t.avail_in&&d(e)<=d(r)&&4!==e)return u(t,-5);if(666===n.status&&0!==t.avail_in)return u(t,-5);if(0!==t.avail_in||0!==n.lookahead||0!==e&&666!==n.status){var b=2===n.strategy?function(t,e){for(var r;;){if(0===t.lookahead&&(P(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,r=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(g(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(g(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(g(t,!1),0===t.strm.avail_out)?1:2}(n,e):3===n.strategy?function(t,e){for(var r,i,n,a,o=t.window;;){if(t.lookahead<=p){if(P(t),t.lookahead<=p&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&0<t.strstart&&(i=o[n=t.strstart-1])===o[++n]&&i===o[++n]&&i===o[++n]){a=t.strstart+p;do{}while(i===o[++n]&&i===o[++n]&&i===o[++n]&&i===o[++n]&&i===o[++n]&&i===o[++n]&&i===o[++n]&&i===o[++n]&&n<a);t.match_length=p-(a-n),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(r=s._tr_tally(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(g(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(g(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(g(t,!1),0===t.strm.avail_out)?1:2}(n,e):i[n.level].func(n,e);if(3!==b&&4!==b||(n.status=666),1===b||3===b)return 0===t.avail_out&&(n.last_flush=-1),0;if(2===b&&(1===e?s._tr_align(n):5!==e&&(s._tr_stored_block(n,0,0,!1),3===e&&(m(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),y(t),0===t.avail_out))return n.last_flush=-1,0}return 4!==e?0:n.wrap<=0?1:(2===n.wrap?(v(n,255&t.adler),v(n,t.adler>>8&255),v(n,t.adler>>16&255),v(n,t.adler>>24&255),v(n,255&t.total_in),v(n,t.total_in>>8&255),v(n,t.total_in>>16&255),v(n,t.total_in>>24&255)):(_(n,t.adler>>>16),_(n,65535&t.adler)),y(t),0<n.wrap&&(n.wrap=-n.wrap),0!==n.pending?0:1)},r.deflateEnd=function(t){var e;return t&&t.state?42!==(e=t.state.status)&&69!==e&&73!==e&&91!==e&&103!==e&&e!==f&&666!==e?u(t,l):(t.state=null,e===f?u(t,-3):0):l},r.deflateSetDictionary=function(t,e){var r,i,s,o,h,p,c,f,u=e.length;if(!t||!t.state)return l;if(2===(o=(r=t.state).wrap)||1===o&&42!==r.status||r.lookahead)return l;for(1===o&&(t.adler=a(t.adler,e,u,0)),r.wrap=0,u>=r.w_size&&(0===o&&(m(r.head),r.strstart=0,r.block_start=0,r.insert=0),f=new n.Buf8(r.w_size),n.arraySet(f,e,u-r.w_size,r.w_size,0),e=f,u=r.w_size),h=t.avail_in,p=t.next_in,c=t.input,t.avail_in=u,t.next_in=0,t.input=e,P(r);r.lookahead>=3;){for(i=r.strstart,s=r.lookahead-2;r.ins_h=(r.ins_h<<r.hash_shift^r.window[i+3-1])&r.hash_mask,r.prev[i&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=i,i++,--s;);r.strstart=i,r.lookahead=2,P(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,t.next_in=p,t.input=c,t.avail_in=h,r.wrap=o,0},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(t,e,r){e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(t,e,r){e.exports=function(t,e){var r,i,n,s,a,o,h,l,p,c,f,u,d,m,y,g,v,_,b,P,S,w,k,x,E;r=t.state,i=t.next_in,x=t.input,n=i+(t.avail_in-5),s=t.next_out,E=t.output,a=s-(e-t.avail_out),o=s+(t.avail_out-257),h=r.dmax,l=r.wsize,p=r.whave,c=r.wnext,f=r.window,u=r.hold,d=r.bits,m=r.lencode,y=r.distcode,g=(1<<r.lenbits)-1,v=(1<<r.distbits)-1;t:do{d<15&&(u+=x[i++]<<d,d+=8,u+=x[i++]<<d,d+=8),_=m[u&g];e:for(;;){if(u>>>=b=_>>>24,d-=b,0==(b=_>>>16&255))E[s++]=65535&_;else{if(!(16&b)){if(0==(64&b)){_=m[(65535&_)+(u&(1<<b)-1)];continue e}if(32&b){r.mode=12;break t}t.msg="invalid literal/length code",r.mode=30;break t}P=65535&_,(b&=15)&&(d<b&&(u+=x[i++]<<d,d+=8),P+=u&(1<<b)-1,u>>>=b,d-=b),d<15&&(u+=x[i++]<<d,d+=8,u+=x[i++]<<d,d+=8),_=y[u&v];r:for(;;){if(u>>>=b=_>>>24,d-=b,!(16&(b=_>>>16&255))){if(0==(64&b)){_=y[(65535&_)+(u&(1<<b)-1)];continue r}t.msg="invalid distance code",r.mode=30;break t}if(S=65535&_,d<(b&=15)&&(u+=x[i++]<<d,(d+=8)<b&&(u+=x[i++]<<d,d+=8)),h<(S+=u&(1<<b)-1)){t.msg="invalid distance too far back",r.mode=30;break t}if(u>>>=b,d-=b,(b=s-a)<S){if(p<(b=S-b)&&r.sane){t.msg="invalid distance too far back",r.mode=30;break t}if(k=f,(w=0)===c){if(w+=l-b,b<P){for(P-=b;E[s++]=f[w++],--b;);w=s-S,k=E}}else if(c<b){if(w+=l+c-b,(b-=c)<P){for(P-=b;E[s++]=f[w++],--b;);if(w=0,c<P){for(P-=b=c;E[s++]=f[w++],--b;);w=s-S,k=E}}}else if(w+=c-b,b<P){for(P-=b;E[s++]=f[w++],--b;);w=s-S,k=E}for(;2<P;)E[s++]=k[w++],E[s++]=k[w++],E[s++]=k[w++],P-=3;P&&(E[s++]=k[w++],1<P&&(E[s++]=k[w++]))}else{for(w=s-S;E[s++]=E[w++],E[s++]=E[w++],E[s++]=E[w++],2<(P-=3););P&&(E[s++]=E[w++],1<P&&(E[s++]=E[w++]))}break}}break}}while(i<n&&s<o);i-=P=d>>3,u&=(1<<(d-=P<<3))-1,t.next_in=i,t.next_out=s,t.avail_in=i<n?n-i+5:5-(i-n),t.avail_out=s<o?o-s+257:257-(s-o),r.hold=u,r.bits=d}},{}],49:[function(t,e,r){var i=t("../utils/common"),n=t("./adler32"),s=t("./crc32"),a=t("./inffast"),o=t("./inftrees"),h=-2;function l(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function p(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new i.Buf16(320),this.work=new i.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function c(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=1,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new i.Buf32(852),e.distcode=e.distdyn=new i.Buf32(592),e.sane=1,e.back=-1,0):h}function f(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,c(t)):h}function u(t,e){var r,i;return t&&t.state?(i=t.state,e<0?(r=0,e=-e):(r=1+(e>>4),e<48&&(e&=15)),e&&(e<8||15<e)?h:(null!==i.window&&i.wbits!==e&&(i.window=null),i.wrap=r,i.wbits=e,f(t))):h}function d(t,e){var r,i;return t?(i=new p,(t.state=i).window=null,0!==(r=u(t,e))&&(t.state=null),r):h}var m,y,g=!0;function v(t){if(g){var e;for(m=new i.Buf32(512),y=new i.Buf32(32),e=0;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(o(1,t.lens,0,288,m,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;o(2,t.lens,0,32,y,0,t.work,{bits:5}),g=!1}t.lencode=m,t.lenbits=9,t.distcode=y,t.distbits=5}function _(t,e,r,n){var s,a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new i.Buf8(a.wsize)),n>=a.wsize?(i.arraySet(a.window,e,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):(n<(s=a.wsize-a.wnext)&&(s=n),i.arraySet(a.window,e,r-n,s,a.wnext),(n-=s)?(i.arraySet(a.window,e,r-n,n,0),a.wnext=n,a.whave=a.wsize):(a.wnext+=s,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=s))),0}r.inflateReset=f,r.inflateReset2=u,r.inflateResetKeep=c,r.inflateInit=function(t){return d(t,15)},r.inflateInit2=d,r.inflate=function(t,e){var r,p,c,f,u,d,m,y,g,b,P,S,w,k,x,E,A,C,T,D,I,F,M,O,R=0,z=new i.Buf8(4),L=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return h;12===(r=t.state).mode&&(r.mode=13),u=t.next_out,c=t.output,m=t.avail_out,f=t.next_in,p=t.input,d=t.avail_in,y=r.hold,g=r.bits,b=d,P=m,F=0;t:for(;;)switch(r.mode){case 1:if(0===r.wrap){r.mode=13;break}for(;g<16;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(2&r.wrap&&35615===y){z[r.check=0]=255&y,z[1]=y>>>8&255,r.check=s(r.check,z,2,0),g=y=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&y)<<8)+(y>>8))%31){t.msg="incorrect header check",r.mode=30;break}if(8!=(15&y)){t.msg="unknown compression method",r.mode=30;break}if(g-=4,I=8+(15&(y>>>=4)),0===r.wbits)r.wbits=I;else if(I>r.wbits){t.msg="invalid window size",r.mode=30;break}r.dmax=1<<I,t.adler=r.check=1,r.mode=512&y?10:12,g=y=0;break;case 2:for(;g<16;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(r.flags=y,8!=(255&r.flags)){t.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){t.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=y>>8&1),512&r.flags&&(z[0]=255&y,z[1]=y>>>8&255,r.check=s(r.check,z,2,0)),g=y=0,r.mode=3;case 3:for(;g<32;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}r.head&&(r.head.time=y),512&r.flags&&(z[0]=255&y,z[1]=y>>>8&255,z[2]=y>>>16&255,z[3]=y>>>24&255,r.check=s(r.check,z,4,0)),g=y=0,r.mode=4;case 4:for(;g<16;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}r.head&&(r.head.xflags=255&y,r.head.os=y>>8),512&r.flags&&(z[0]=255&y,z[1]=y>>>8&255,r.check=s(r.check,z,2,0)),g=y=0,r.mode=5;case 5:if(1024&r.flags){for(;g<16;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}r.length=y,r.head&&(r.head.extra_len=y),512&r.flags&&(z[0]=255&y,z[1]=y>>>8&255,r.check=s(r.check,z,2,0)),g=y=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&(d<(S=r.length)&&(S=d),S&&(r.head&&(I=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),i.arraySet(r.head.extra,p,f,S,I)),512&r.flags&&(r.check=s(r.check,p,S,f)),d-=S,f+=S,r.length-=S),r.length))break t;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===d)break t;for(S=0;I=p[f+S++],r.head&&I&&r.length<65536&&(r.head.name+=String.fromCharCode(I)),I&&S<d;);if(512&r.flags&&(r.check=s(r.check,p,S,f)),d-=S,f+=S,I)break t}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===d)break t;for(S=0;I=p[f+S++],r.head&&I&&r.length<65536&&(r.head.comment+=String.fromCharCode(I)),I&&S<d;);if(512&r.flags&&(r.check=s(r.check,p,S,f)),d-=S,f+=S,I)break t}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;g<16;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(y!==(65535&r.check)){t.msg="header crc mismatch",r.mode=30;break}g=y=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),t.adler=r.check=0,r.mode=12;break;case 10:for(;g<32;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}t.adler=r.check=l(y),g=y=0,r.mode=11;case 11:if(0===r.havedict)return t.next_out=u,t.avail_out=m,t.next_in=f,t.avail_in=d,r.hold=y,r.bits=g,2;t.adler=r.check=1,r.mode=12;case 12:if(5===e||6===e)break t;case 13:if(r.last){y>>>=7&g,g-=7&g,r.mode=27;break}for(;g<3;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}switch(r.last=1&y,g-=1,3&(y>>>=1)){case 0:r.mode=14;break;case 1:if(v(r),r.mode=20,6!==e)break;y>>>=2,g-=2;break t;case 2:r.mode=17;break;case 3:t.msg="invalid block type",r.mode=30}y>>>=2,g-=2;break;case 14:for(y>>>=7&g,g-=7&g;g<32;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if((65535&y)!=(y>>>16^65535)){t.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&y,g=y=0,r.mode=15,6===e)break t;case 15:r.mode=16;case 16:if(S=r.length){if(d<S&&(S=d),m<S&&(S=m),0===S)break t;i.arraySet(c,p,f,S,u),d-=S,f+=S,m-=S,u+=S,r.length-=S;break}r.mode=12;break;case 17:for(;g<14;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(r.nlen=257+(31&y),y>>>=5,g-=5,r.ndist=1+(31&y),y>>>=5,g-=5,r.ncode=4+(15&y),y>>>=4,g-=4,286<r.nlen||30<r.ndist){t.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;g<3;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}r.lens[L[r.have++]]=7&y,y>>>=3,g-=3}for(;r.have<19;)r.lens[L[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,M={bits:r.lenbits},F=o(0,r.lens,0,19,r.lencode,0,r.work,M),r.lenbits=M.bits,F){t.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;E=(R=r.lencode[y&(1<<r.lenbits)-1])>>>16&255,A=65535&R,!((x=R>>>24)<=g);){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(A<16)y>>>=x,g-=x,r.lens[r.have++]=A;else{if(16===A){for(O=x+2;g<O;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(y>>>=x,g-=x,0===r.have){t.msg="invalid bit length repeat",r.mode=30;break}I=r.lens[r.have-1],S=3+(3&y),y>>>=2,g-=2}else if(17===A){for(O=x+3;g<O;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}g-=x,I=0,S=3+(7&(y>>>=x)),y>>>=3,g-=3}else{for(O=x+7;g<O;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}g-=x,I=0,S=11+(127&(y>>>=x)),y>>>=7,g-=7}if(r.have+S>r.nlen+r.ndist){t.msg="invalid bit length repeat",r.mode=30;break}for(;S--;)r.lens[r.have++]=I}}if(30===r.mode)break;if(0===r.lens[256]){t.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,M={bits:r.lenbits},F=o(1,r.lens,0,r.nlen,r.lencode,0,r.work,M),r.lenbits=M.bits,F){t.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,M={bits:r.distbits},F=o(2,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,M),r.distbits=M.bits,F){t.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===e)break t;case 20:r.mode=21;case 21:if(6<=d&&258<=m){t.next_out=u,t.avail_out=m,t.next_in=f,t.avail_in=d,r.hold=y,r.bits=g,a(t,P),u=t.next_out,c=t.output,m=t.avail_out,f=t.next_in,p=t.input,d=t.avail_in,y=r.hold,g=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;E=(R=r.lencode[y&(1<<r.lenbits)-1])>>>16&255,A=65535&R,!((x=R>>>24)<=g);){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(E&&0==(240&E)){for(C=x,T=E,D=A;E=(R=r.lencode[D+((y&(1<<C+T)-1)>>C)])>>>16&255,A=65535&R,!(C+(x=R>>>24)<=g);){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}y>>>=C,g-=C,r.back+=C}if(y>>>=x,g-=x,r.back+=x,r.length=A,0===E){r.mode=26;break}if(32&E){r.back=-1,r.mode=12;break}if(64&E){t.msg="invalid literal/length code",r.mode=30;break}r.extra=15&E,r.mode=22;case 22:if(r.extra){for(O=r.extra;g<O;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}r.length+=y&(1<<r.extra)-1,y>>>=r.extra,g-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;E=(R=r.distcode[y&(1<<r.distbits)-1])>>>16&255,A=65535&R,!((x=R>>>24)<=g);){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(0==(240&E)){for(C=x,T=E,D=A;E=(R=r.distcode[D+((y&(1<<C+T)-1)>>C)])>>>16&255,A=65535&R,!(C+(x=R>>>24)<=g);){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}y>>>=C,g-=C,r.back+=C}if(y>>>=x,g-=x,r.back+=x,64&E){t.msg="invalid distance code",r.mode=30;break}r.offset=A,r.extra=15&E,r.mode=24;case 24:if(r.extra){for(O=r.extra;g<O;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}r.offset+=y&(1<<r.extra)-1,y>>>=r.extra,g-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){t.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===m)break t;if(S=P-m,r.offset>S){if((S=r.offset-S)>r.whave&&r.sane){t.msg="invalid distance too far back",r.mode=30;break}w=S>r.wnext?(S-=r.wnext,r.wsize-S):r.wnext-S,S>r.length&&(S=r.length),k=r.window}else k=c,w=u-r.offset,S=r.length;for(m<S&&(S=m),m-=S,r.length-=S;c[u++]=k[w++],--S;);0===r.length&&(r.mode=21);break;case 26:if(0===m)break t;c[u++]=r.length,m--,r.mode=21;break;case 27:if(r.wrap){for(;g<32;){if(0===d)break t;d--,y|=p[f++]<<g,g+=8}if(P-=m,t.total_out+=P,r.total+=P,P&&(t.adler=r.check=r.flags?s(r.check,c,P,u-P):n(r.check,c,P,u-P)),P=m,(r.flags?y:l(y))!==r.check){t.msg="incorrect data check",r.mode=30;break}g=y=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;g<32;){if(0===d)break t;d--,y+=p[f++]<<g,g+=8}if(y!==(4294967295&r.total)){t.msg="incorrect length check",r.mode=30;break}g=y=0}r.mode=29;case 29:F=1;break t;case 30:F=-3;break t;case 31:return-4;case 32:default:return h}return t.next_out=u,t.avail_out=m,t.next_in=f,t.avail_in=d,r.hold=y,r.bits=g,(r.wsize||P!==t.avail_out&&r.mode<30&&(r.mode<27||4!==e))&&_(t,t.output,t.next_out,P-t.avail_out)?(r.mode=31,-4):(b-=t.avail_in,P-=t.avail_out,t.total_in+=b,t.total_out+=P,r.total+=P,r.wrap&&P&&(t.adler=r.check=r.flags?s(r.check,c,P,t.next_out-P):n(r.check,c,P,t.next_out-P)),t.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),(0==b&&0===P||4===e)&&0===F&&(F=-5),F)},r.inflateEnd=function(t){if(!t||!t.state)return h;var e=t.state;return e.window&&(e.window=null),t.state=null,0},r.inflateGetHeader=function(t,e){var r;return t&&t.state?0==(2&(r=t.state).wrap)?h:((r.head=e).done=!1,0):h},r.inflateSetDictionary=function(t,e){var r,i=e.length;return t&&t.state?0!==(r=t.state).wrap&&11!==r.mode?h:11===r.mode&&n(1,e,i,0)!==r.check?-3:_(t,e,i,i)?(r.mode=31,-4):(r.havedict=1,0):h},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(t,e,r){var i=t("../utils/common"),n=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],s=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(t,e,r,h,l,p,c,f){var u,d,m,y,g,v,_,b,P,S=f.bits,w=0,k=0,x=0,E=0,A=0,C=0,T=0,D=0,I=0,F=0,M=null,O=0,R=new i.Buf16(16),z=new i.Buf16(16),L=null,B=0;for(w=0;w<=15;w++)R[w]=0;for(k=0;k<h;k++)R[e[r+k]]++;for(A=S,E=15;1<=E&&0===R[E];E--);if(E<A&&(A=E),0===E)return l[p++]=20971520,l[p++]=20971520,f.bits=1,0;for(x=1;x<E&&0===R[x];x++);for(A<x&&(A=x),w=D=1;w<=15;w++)if(D<<=1,(D-=R[w])<0)return-1;if(0<D&&(0===t||1!==E))return-1;for(z[1]=0,w=1;w<15;w++)z[w+1]=z[w]+R[w];for(k=0;k<h;k++)0!==e[r+k]&&(c[z[e[r+k]]++]=k);if(v=0===t?(M=L=c,19):1===t?(M=n,O-=257,L=s,B-=257,256):(M=a,L=o,-1),w=x,g=p,T=k=F=0,m=-1,y=(I=1<<(C=A))-1,1===t&&852<I||2===t&&592<I)return 1;for(;;){for(_=w-T,P=c[k]<v?(b=0,c[k]):c[k]>v?(b=L[B+c[k]],M[O+c[k]]):(b=96,0),u=1<<w-T,x=d=1<<C;l[g+(F>>T)+(d-=u)]=_<<24|b<<16|P|0,0!==d;);for(u=1<<w-1;F&u;)u>>=1;if(0!==u?(F&=u-1,F+=u):F=0,k++,0==--R[w]){if(w===E)break;w=e[r+c[k]]}if(A<w&&(F&y)!==m){for(0===T&&(T=A),g+=x,D=1<<(C=w-T);C+T<E&&!((D-=R[C+T])<=0);)C++,D<<=1;if(I+=1<<C,1===t&&852<I||2===t&&592<I)return 1;l[m=F&y]=A<<24|C<<16|g-p|0}}return 0!==F&&(l[g+F]=w-T<<24|64<<16|0),f.bits=A,0}},{"../utils/common":41}],51:[function(t,e,r){e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(t,e,r){var i=t("../utils/common");function n(t){for(var e=t.length;0<=--e;)t[e]=0}var s=15,a=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],o=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],l=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],p=new Array(576);n(p);var c=new Array(60);n(c);var f=new Array(512);n(f);var u=new Array(256);n(u);var d=new Array(29);n(d);var m,y,g,v=new Array(30);function _(t,e,r,i,n){this.static_tree=t,this.extra_bits=e,this.extra_base=r,this.elems=i,this.max_length=n,this.has_stree=t&&t.length}function b(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function P(t){return t<256?f[t]:f[256+(t>>>7)]}function S(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function w(t,e,r){t.bi_valid>16-r?(t.bi_buf|=e<<t.bi_valid&65535,S(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=r-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=r)}function k(t,e,r){w(t,r[2*e],r[2*e+1])}function x(t,e){for(var r=0;r|=1&t,t>>>=1,r<<=1,0<--e;);return r>>>1}function E(t,e,r){var i,n,a=new Array(16),o=0;for(i=1;i<=s;i++)a[i]=o=o+r[i-1]<<1;for(n=0;n<=e;n++){var h=t[2*n+1];0!==h&&(t[2*n]=x(a[h]++,h))}}function A(t){var e;for(e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function C(t){8<t.bi_valid?S(t,t.bi_buf):0<t.bi_valid&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function T(t,e,r,i){var n=2*e,s=2*r;return t[n]<t[s]||t[n]===t[s]&&i[e]<=i[r]}function D(t,e,r){for(var i=t.heap[r],n=r<<1;n<=t.heap_len&&(n<t.heap_len&&T(e,t.heap[n+1],t.heap[n],t.depth)&&n++,!T(e,i,t.heap[n],t.depth));)t.heap[r]=t.heap[n],r=n,n<<=1;t.heap[r]=i}function I(t,e,r){var i,n,s,h,l=0;if(0!==t.last_lit)for(;i=t.pending_buf[t.d_buf+2*l]<<8|t.pending_buf[t.d_buf+2*l+1],n=t.pending_buf[t.l_buf+l],l++,0===i?k(t,n,e):(k(t,(s=u[n])+256+1,e),0!==(h=a[s])&&w(t,n-=d[s],h),k(t,s=P(--i),r),0!==(h=o[s])&&w(t,i-=v[s],h)),l<t.last_lit;);k(t,256,e)}function F(t,e){var r,i,n,a=e.dyn_tree,o=e.stat_desc.static_tree,h=e.stat_desc.has_stree,l=e.stat_desc.elems,p=-1;for(t.heap_len=0,t.heap_max=573,r=0;r<l;r++)0!==a[2*r]?(t.heap[++t.heap_len]=p=r,t.depth[r]=0):a[2*r+1]=0;for(;t.heap_len<2;)a[2*(n=t.heap[++t.heap_len]=p<2?++p:0)]=1,t.depth[n]=0,t.opt_len--,h&&(t.static_len-=o[2*n+1]);for(e.max_code=p,r=t.heap_len>>1;1<=r;r--)D(t,a,r);for(n=l;r=t.heap[1],t.heap[1]=t.heap[t.heap_len--],D(t,a,1),i=t.heap[1],t.heap[--t.heap_max]=r,t.heap[--t.heap_max]=i,a[2*n]=a[2*r]+a[2*i],t.depth[n]=(t.depth[r]>=t.depth[i]?t.depth[r]:t.depth[i])+1,a[2*r+1]=a[2*i+1]=n,t.heap[1]=n++,D(t,a,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1],function(t,e){var r,i,n,a,o,h,l=e.dyn_tree,p=e.max_code,c=e.stat_desc.static_tree,f=e.stat_desc.has_stree,u=e.stat_desc.extra_bits,d=e.stat_desc.extra_base,m=e.stat_desc.max_length,y=0;for(a=0;a<=s;a++)t.bl_count[a]=0;for(l[2*t.heap[t.heap_max]+1]=0,r=t.heap_max+1;r<573;r++)m<(a=l[2*l[2*(i=t.heap[r])+1]+1]+1)&&(a=m,y++),l[2*i+1]=a,p<i||(t.bl_count[a]++,o=0,d<=i&&(o=u[i-d]),h=l[2*i],t.opt_len+=h*(a+o),f&&(t.static_len+=h*(c[2*i+1]+o)));if(0!==y){do{for(a=m-1;0===t.bl_count[a];)a--;t.bl_count[a]--,t.bl_count[a+1]+=2,t.bl_count[m]--,y-=2}while(0<y);for(a=m;0!==a;a--)for(i=t.bl_count[a];0!==i;)p<(n=t.heap[--r])||(l[2*n+1]!==a&&(t.opt_len+=(a-l[2*n+1])*l[2*n],l[2*n+1]=a),i--)}}(t,e),E(a,p,t.bl_count)}function M(t,e,r){var i,n,s=-1,a=e[1],o=0,h=7,l=4;for(0===a&&(h=138,l=3),e[2*(r+1)+1]=65535,i=0;i<=r;i++)n=a,a=e[2*(i+1)+1],++o<h&&n===a||(o<l?t.bl_tree[2*n]+=o:0!==n?(n!==s&&t.bl_tree[2*n]++,t.bl_tree[32]++):o<=10?t.bl_tree[34]++:t.bl_tree[36]++,s=n,l=(o=0)===a?(h=138,3):n===a?(h=6,3):(h=7,4))}function O(t,e,r){var i,n,s=-1,a=e[1],o=0,h=7,l=4;for(0===a&&(h=138,l=3),i=0;i<=r;i++)if(n=a,a=e[2*(i+1)+1],!(++o<h&&n===a)){if(o<l)for(;k(t,n,t.bl_tree),0!=--o;);else 0!==n?(n!==s&&(k(t,n,t.bl_tree),o--),k(t,16,t.bl_tree),w(t,o-3,2)):o<=10?(k(t,17,t.bl_tree),w(t,o-3,3)):(k(t,18,t.bl_tree),w(t,o-11,7));s=n,l=(o=0)===a?(h=138,3):n===a?(h=6,3):(h=7,4)}}n(v);var R=!1;function z(t,e,r,n){var s,a,o;w(t,0+(n?1:0),3),a=e,o=r,C(s=t),S(s,o),S(s,~o),i.arraySet(s.pending_buf,s.window,a,o,s.pending),s.pending+=o}r._tr_init=function(t){R||(function(){var t,e,r,i,n,l=new Array(16);for(i=r=0;i<28;i++)for(d[i]=r,t=0;t<1<<a[i];t++)u[r++]=i;for(u[r-1]=i,i=n=0;i<16;i++)for(v[i]=n,t=0;t<1<<o[i];t++)f[n++]=i;for(n>>=7;i<30;i++)for(v[i]=n<<7,t=0;t<1<<o[i]-7;t++)f[256+n++]=i;for(e=0;e<=s;e++)l[e]=0;for(t=0;t<=143;)p[2*t+1]=8,t++,l[8]++;for(;t<=255;)p[2*t+1]=9,t++,l[9]++;for(;t<=279;)p[2*t+1]=7,t++,l[7]++;for(;t<=287;)p[2*t+1]=8,t++,l[8]++;for(E(p,287,l),t=0;t<30;t++)c[2*t+1]=5,c[2*t]=x(t,5);m=new _(p,a,257,286,s),y=new _(c,o,0,30,s),g=new _(new Array(0),h,0,19,7)}(),R=!0),t.l_desc=new b(t.dyn_ltree,m),t.d_desc=new b(t.dyn_dtree,y),t.bl_desc=new b(t.bl_tree,g),t.bi_buf=0,t.bi_valid=0,A(t)},r._tr_stored_block=z,r._tr_flush_block=function(t,e,r,i){var n,s,a=0;0<t.level?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,r=4093624447;for(e=0;e<=31;e++,r>>>=1)if(1&r&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<256;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),F(t,t.l_desc),F(t,t.d_desc),a=function(t){var e;for(M(t,t.dyn_ltree,t.l_desc.max_code),M(t,t.dyn_dtree,t.d_desc.max_code),F(t,t.bl_desc),e=18;3<=e&&0===t.bl_tree[2*l[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),n=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=n&&(n=s)):n=s=r+5,r+4<=n&&-1!==e?z(t,e,r,i):4===t.strategy||s===n?(w(t,2+(i?1:0),3),I(t,p,c)):(w(t,4+(i?1:0),3),function(t,e,r,i){var n;for(w(t,e-257,5),w(t,r-1,5),w(t,i-4,4),n=0;n<i;n++)w(t,t.bl_tree[2*l[n]+1],3);O(t,t.dyn_ltree,e-1),O(t,t.dyn_dtree,r-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,a+1),I(t,t.dyn_ltree,t.dyn_dtree)),A(t),i&&C(t)},r._tr_tally=function(t,e,r){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&r,t.last_lit++,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[2*(u[r]+256+1)]++,t.dyn_dtree[2*P(e)]++),t.last_lit===t.lit_bufsize-1},r._tr_align=function(t){var e;w(t,2,3),k(t,256,p),16===(e=t).bi_valid?(S(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}},{"../utils/common":41}],53:[function(t,e,r){e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(t,e,r){e.exports="function"==typeof setImmediate?setImmediate:function(){var t=[].slice.apply(arguments);t.splice(1,0,0),setTimeout.apply(null,t)}},{}]},{},[10])(10)}))}).call(this,void 0!==i?i:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)}))}).call(this,void 0!==i?i:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)}))}).call(this,void 0!==i?i:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)}))}).call(this,void 0!==i?i:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)}))}).call(this,void 0!==commonjsGlobal?commonjsGlobal:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)}))})),_templateObject$1,styles=i$3(_templateObject$1||(_templateObject$1=_taggedTemplateLiteral(["\n  * {\n    box-sizing: border-box;\n  }\n\n  :host {\n    --lottie-player-toolbar-height: 35px;\n    --lottie-player-toolbar-background-color: transparent;\n    --lottie-player-toolbar-icon-color: #999;\n    --lottie-player-toolbar-icon-hover-color: #222;\n    --lottie-player-toolbar-icon-active-color: #555;\n    --lottie-player-seeker-track-color: #ccc;\n    --lottie-player-seeker-thumb-color: rgba(0, 107, 120, 0.8);\n    --lottie-player-seeker-display: block;\n\n    display: block;\n    width: 100%;\n    height: 100%;\n  }\n\n  .main {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    width: 100%;\n  }\n\n  .animation {\n    width: 100%;\n    height: 100%;\n    display: flex;\n  }\n  .animation.controls {\n    height: calc(100% - 35px);\n  }\n\n  .toolbar {\n    display: flex;\n    align-items: center;\n    justify-items: center;\n    background-color: var(--lottie-player-toolbar-background-color);\n    margin: 0 5px;\n    height: 35px;\n  }\n\n  .toolbar button {\n    cursor: pointer;\n    fill: var(--lottie-player-toolbar-icon-color);\n    display: flex;\n    background: none;\n    border: 0;\n    padding: 0;\n    outline: none;\n    height: 100%;\n  }\n\n  .toolbar button:hover {\n    fill: var(--lottie-player-toolbar-icon-hover-color);\n  }\n\n  .toolbar button.active {\n    fill: var(--lottie-player-toolbar-icon-active-color);\n  }\n\n  .toolbar button.active:hover {\n    fill: var(--lottie-player-toolbar-icon-hover-color);\n  }\n\n  .toolbar button:focus {\n    outline: 1px dotted var(--lottie-player-toolbar-icon-active-color);\n  }\n\n  .toolbar button svg {\n  }\n\n  .toolbar button.disabled svg {\n    display: none;\n  }\n\n  .seeker {\n    -webkit-appearance: none;\n    width: 95%;\n    outline: none;\n    background-color: var(--lottie-player-toolbar-background-color);\n    display: var(--lottie-player-seeker-display);\n  }\n\n  .seeker::-webkit-slider-runnable-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-webkit-slider-thumb {\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n    -webkit-appearance: none;\n    margin-top: -5px;\n  }\n  .seeker:focus::-webkit-slider-runnable-track {\n    background: #999;\n  }\n  .seeker::-moz-range-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-moz-range-thumb {\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n  }\n  .seeker::-ms-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: transparent;\n    border-color: transparent;\n    color: transparent;\n  }\n  .seeker::-ms-fill-lower {\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-ms-fill-upper {\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-ms-thumb {\n    border: 0;\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n  }\n  .seeker:focus::-ms-fill-lower {\n    background: var(--lottie-player-seeker-track-color);\n  }\n  .seeker:focus::-ms-fill-upper {\n    background: var(--lottie-player-seeker-track-color);\n  }\n\n  .error {\n    display: flex;\n    margin: auto;\n    justify-content: center;\n    height: 100%;\n    align-items: center;\n  }\n"]))),_templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,PlayerState,PlayMode,PlayerEvents;function fetchPath(t){var e;const r=null===(e=t.split(".").pop())||void 0===e?void 0:e.toLowerCase();let i=!1;return"json"===r&&(i=!0),new Promise((e,r)=>{const n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType=i?"json":"arraybuffer",n.send(),n.onreadystatechange=function(){4==n.readyState&&200==n.status&&(i&&e(n.response),jszip.loadAsync(n.response).then(t=>{t.file("manifest.json").async("string").then(r=>{const i=JSON.parse(r);if(!("animations"in i))throw new Error("Manifest not found");if(0===i.animations.length)throw new Error("No animations listed in the manifest");const n=i.animations[0];t.file("animations/".concat(n.id,".json")).async("string").then(r=>{const i=JSON.parse(r);"assets"in i&&Promise.all(i.assets.map(e=>{if(e.p&&null!=t.file("images/".concat(e.p)))return new Promise(r=>{const i=e.p.split(".").pop();t.file("images/".concat(e.p)).async("base64").then(t=>{e.p="svg"===i||"svg+xml"===i?"data:image/svg+xml;base64,"+t:"data:;base64,"+t,e.e=1,r()})})})).then(()=>{e(i)})})})}).catch(t=>{r(t)}))}})}exports.PlayerState=void 0,PlayerState=exports.PlayerState||(exports.PlayerState={}),PlayerState.Loading="loading",PlayerState.Playing="playing",PlayerState.Paused="paused",PlayerState.Stopped="stopped",PlayerState.Frozen="frozen",PlayerState.Error="error",exports.PlayMode=void 0,PlayMode=exports.PlayMode||(exports.PlayMode={}),PlayMode.Normal="normal",PlayMode.Bounce="bounce",exports.PlayerEvents=void 0,PlayerEvents=exports.PlayerEvents||(exports.PlayerEvents={}),PlayerEvents.Load="load",PlayerEvents.Error="error",PlayerEvents.Ready="ready",PlayerEvents.Play="play",PlayerEvents.Pause="pause",PlayerEvents.Stop="stop",PlayerEvents.Freeze="freeze",PlayerEvents.Loop="loop",PlayerEvents.Complete="complete",PlayerEvents.Frame="frame",exports.DotLottiePlayer=class extends s{constructor(){super(...arguments),this.mode=exports.PlayMode.Normal,this.autoplay=!1,this.background="transparent",this.controls=!1,this.direction=1,this.hover=!1,this.loop=!1,this.renderer="svg",this.speed=1,this.currentState=exports.PlayerState.Loading,this.intermission=1,this._counter=0}_onVisibilityChange(){document.hidden&&this.currentState===exports.PlayerState.Playing?this.freeze():this.currentState===exports.PlayerState.Frozen&&this.play()}_handleSeekChange(t){if(!this._lottie||isNaN(t.target.value))return;const e=t.target.value/100*this._lottie.totalFrames;this.seek(e)}isLottie(t){return["v","ip","op","layers","fr","w","h"].every(e=>Object.prototype.hasOwnProperty.call(t,e))}parseSrc(t){if("object"==typeof t)return t;try{return JSON.parse(t)}catch(e){return new URL(t,window.location.href).toString()}}async load(t,e){if(!this.shadowRoot)return;const r={container:this.container,loop:!1,autoplay:!1,renderer:this.renderer,rendererSettings:e||{scaleMode:"noScale",clearCanvas:!1,progressiveLoad:!0,hideOnTransparent:!0}};try{let e=this.parseSrc(t);if("string"==typeof e&&(e=await fetchPath(e),void 0===e))throw new Error("[dotLottie] No animation to load!");if(!this.isLottie(e))throw new Error("[dotLottie] Load method failing. Object is not a valid Lottie.");this._lottie&&this._lottie.destroy(),this._lottie=lottie_svg.loadAnimation(Object.assign(Object.assign({},r),{animationData:e}))}catch(t){return this.currentState=exports.PlayerState.Error,void this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Error))}this._lottie&&(this._lottie.addEventListener("enterFrame",()=>{this.seeker=this._lottie.currentFrame/this._lottie.totalFrames*100,this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Frame,{detail:{frame:this._lottie.currentFrame,seeker:this.seeker}}))}),this._lottie.addEventListener("complete",()=>{this.currentState===exports.PlayerState.Playing?!this.loop||this.count&&this._counter>=this.count?this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Complete)):this.mode===exports.PlayMode.Bounce?(this.count&&(this._counter+=.5),setTimeout(()=>{this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Loop)),this.currentState===exports.PlayerState.Playing&&(this._lottie.setDirection(-1*this._lottie.playDirection),this._lottie.play())},this.intermission)):(this.count&&(this._counter+=1),window.setTimeout(()=>{this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Loop)),this.currentState===exports.PlayerState.Playing&&(this._lottie.stop(),this._lottie.play())},this.intermission)):this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Complete))}),this._lottie.addEventListener("DOMLoaded",()=>{this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Ready))}),this._lottie.addEventListener("data_ready",()=>{this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Load))}),this._lottie.addEventListener("data_failed",()=>{this.currentState=exports.PlayerState.Error,this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Error))}),this.container.addEventListener("mouseenter",()=>{this.hover&&this.currentState!==exports.PlayerState.Playing&&this.play()}),this.container.addEventListener("mouseleave",()=>{this.hover&&this.currentState===exports.PlayerState.Playing&&this.stop()}),this.setSpeed(this.speed),this.setDirection(this.direction),this.autoplay&&this.play())}getLottie(){return this._lottie}play(){this._lottie&&(this._lottie.play(),this.currentState=exports.PlayerState.Playing,this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Play)))}pause(){this._lottie&&(this._lottie.pause(),this.currentState=exports.PlayerState.Paused,this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Pause)))}stop(){this._lottie&&(this._counter=0,this._lottie.stop(),this.currentState=exports.PlayerState.Stopped,this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Stop)))}seek(t){if(!this._lottie)return;"number"==typeof t&&(t=Math.round(t));const e=t.toString().match(/^([0-9]+)(%?)$/);if(!e)return;const r="%"===e[2]?this._lottie.totalFrames*Number(e[1])/100:e[1];this.seeker=r,this.currentState===exports.PlayerState.Playing?this._lottie.goToAndPlay(r,!0):(this._lottie.goToAndStop(r,!0),this._lottie.pause())}snapshot(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(!this.shadowRoot)return;const e=this.shadowRoot.querySelector(".animation svg"),r=(new XMLSerializer).serializeToString(e);if(t){const t=document.createElement("a");t.href="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(r),t.download="download_"+this.seeker+".svg",document.body.appendChild(t),t.click(),document.body.removeChild(t)}return r}freeze(){this._lottie&&(this._lottie.pause(),this.currentState=exports.PlayerState.Frozen,this.dispatchEvent(new CustomEvent(exports.PlayerEvents.Freeze)))}setSpeed(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this._lottie&&this._lottie.setSpeed(t)}setDirection(t){this._lottie&&this._lottie.setDirection(t)}setLooping(t){this._lottie&&(this.loop=t,this._lottie.loop=t)}togglePlay(){return this.currentState===exports.PlayerState.Playing?this.pause():this.play()}toggleLooping(){this.setLooping(!this.loop)}static get styles(){return styles}async firstUpdated(){"IntersectionObserver"in window&&(this._io=new IntersectionObserver(t=>{t[0].isIntersecting?this.currentState===exports.PlayerState.Frozen&&this.play():this.currentState===exports.PlayerState.Playing&&this.freeze()}),this._io.observe(this.container)),void 0!==document.hidden&&document.addEventListener("visibilitychange",()=>this._onVisibilityChange()),this.src&&await this.load(this.src)}disconnectedCallback(){this._io&&(this._io.disconnect(),this._io=void 0),this._lottie&&this._lottie.destroy(),document.removeEventListener("visibilitychange",()=>this._onVisibilityChange())}renderControls(){const t=this.currentState===exports.PlayerState.Playing,e=this.currentState===exports.PlayerState.Paused,r=this.currentState===exports.PlayerState.Stopped;return y(_templateObject||(_templateObject=_taggedTemplateLiteral(['\n      <div id="lottie-controls" aria-label="lottie-animation-controls" class="toolbar">\n        <button\n          id="lottie-play-button"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="play-pause"\n        >\n          ','\n        </button>\n        <button\n          id="lottie-stop-button"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="stop"\n        >\n          <svg width="24" height="24" aria-hidden="true" focusable="false">\n            <path d="M6 6h12v12H6V6z" />\n          </svg>\n        </button>\n        <input\n          id="lottie-seeker-input"\n          class="seeker"\n          type="range"\n          min="0"\n          step="1"\n          max="100"\n          .value=',"\n          @input=","\n          @mousedown=","\n          @mouseup=",'\n          aria-valuemin="1"\n          aria-valuemax="100"\n          role="slider"\n          aria-valuenow=','\n          tabindex="0"\n          aria-label="lottie-seek-input"\n        />\n        <button\n          id="lottie-loop-toggle"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="loop-toggle"\n        >\n          <svg width="24" height="24" aria-hidden="true" focusable="false">\n            <path\n              d="M17.016 17.016v-4.031h1.969v6h-12v3l-3.984-3.984 3.984-3.984v3h10.031zM6.984 6.984v4.031H5.015v-6h12v-3l3.984 3.984-3.984 3.984v-3H6.984z"\n            />\n          </svg>\n        </button>\n      </div>\n    '])),this.togglePlay,t||e?"active":"",y(t?_templateObject2||(_templateObject2=_taggedTemplateLiteral(['\n                <svg width="24" height="24" aria-hidden="true" focusable="false">\n                  <path d="M14.016 5.016H18v13.969h-3.984V5.016zM6 18.984V5.015h3.984v13.969H6z" />\n                </svg>\n              '])):_templateObject3||(_templateObject3=_taggedTemplateLiteral(['\n                <svg width="24" height="24" aria-hidden="true" focusable="false">\n                  <path d="M8.016 5.016L18.985 12 8.016 18.984V5.015z" />\n                </svg>\n              ']))),this.stop,r?"active":"",this.seeker,this._handleSeekChange,()=>{this._prevState=this.currentState,this.freeze()},()=>{this._prevState===exports.PlayerState.Playing&&this.play(),this.seek(this._lottie.currentFrame)},this.seeker,this.toggleLooping,this.loop?"active":"")}render(){const t=this.controls?"main controls":"main",e=this.controls?"animation controls":"animation";return y(_templateObject4||(_templateObject4=_taggedTemplateLiteral(['\n      <div id="animation-container" class=',' lang="en" role="img">\n        <div id="animation" class=',' style="background:',';">\n          ',"\n        </div>\n        ","\n      </div>\n    "])),t,e,this.background,this.currentState===exports.PlayerState.Error?y(_templateObject5||(_templateObject5=_taggedTemplateLiteral(['\n                <div class="error">⚠️</div>\n              ']))):void 0,this.controls?this.renderControls():void 0)}},__decorate([i(".animation")],exports.DotLottiePlayer.prototype,"container",void 0),__decorate([e$5()],exports.DotLottiePlayer.prototype,"mode",void 0),__decorate([e$5({type:Boolean})],exports.DotLottiePlayer.prototype,"autoplay",void 0),__decorate([e$5({type:String,reflect:!0})],exports.DotLottiePlayer.prototype,"background",void 0),__decorate([e$5({type:Boolean})],exports.DotLottiePlayer.prototype,"controls",void 0),__decorate([e$5({type:Number})],exports.DotLottiePlayer.prototype,"count",void 0),__decorate([e$5({type:Number})],exports.DotLottiePlayer.prototype,"direction",void 0),__decorate([e$5({type:Boolean})],exports.DotLottiePlayer.prototype,"hover",void 0),__decorate([e$5({type:Boolean,reflect:!0})],exports.DotLottiePlayer.prototype,"loop",void 0),__decorate([e$5({type:String})],exports.DotLottiePlayer.prototype,"renderer",void 0),__decorate([e$5({type:Number})],exports.DotLottiePlayer.prototype,"speed",void 0),__decorate([e$5({type:String})],exports.DotLottiePlayer.prototype,"src",void 0),__decorate([e$5({type:String})],exports.DotLottiePlayer.prototype,"currentState",void 0),__decorate([e$5()],exports.DotLottiePlayer.prototype,"seeker",void 0),__decorate([e$5()],exports.DotLottiePlayer.prototype,"intermission",void 0),exports.DotLottiePlayer=__decorate([e$6("dotlottie-player")],exports.DotLottiePlayer),exports.fetchPath=fetchPath,Object.defineProperty(exports,"__esModule",{value:!0})}));
//# sourceMappingURL=dotlottie-player.js.map