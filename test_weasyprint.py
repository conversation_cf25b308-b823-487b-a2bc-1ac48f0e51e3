import os

# Ajouter manuellement le chemin vers les DLL GTK avant tout import
os.environ['PATH'] = r"C:\Program Files\GTK3-Runtime-Win64\bin;" + os.environ.get('PATH', '')

# Importation de WeasyPrint
from weasyprint import HTML

# Exemple de contenu HTML
html_content = """
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test WeasyPrint</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Bienvenue sur WeasyPrint</h1>
    <p>Ceci est un test pour générer un PDF.</p>
</body>
</html>
"""

# Générer le PDF
HTML(string=html_content).write_pdf("test.pdf")

print("✅ PDF généré avec succès : test.pdf")
