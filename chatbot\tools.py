from langchain.tools import tool
from pages.models import Mission, Bion, StockHasEquipement, Systeme, EquipementSystemeInstance, Bion, Cie, Sion,Servant, Technicien
from django.db.models import Q

@tool
def get_missions_by_battalion(bion_name: str) -> str:
    """Retourne les missions associées à un bataillon (bion) donné par son nom."""
    try:
        bion = Bion.objects.get(nom__iexact=bion_name)
        missions = Mission.objects.filter(bion_mission=bion)
        if not missions.exists():
            return f"Aucune mission trouvée pour le bataillon {bion_name}."
        return "\n".join([f"{m.objet} ({m.reference_mission})" for m in missions])
    except Bion.DoesNotExist:
        return f"Bataillon '{bion_name}' introuvable."


@tool
def check_minimum_stock(nom_equipement: str) -> str:
    """Vérifie si un équipement est en dessous de son stock minimum."""
    stocks = StockHasEquipement.objects.filter(equipement__nom__iexact=nom_equipement)
    if not stocks.exists():
        return f"Aucun stock trouvé pour l'équipement {nom_equipement}."
    alerts = []
    for stock in stocks:
        if stock.quantite is not None and stock.quantite_critique is not None:
            if stock.quantite <= stock.quantite_critique:
                alerts.append(f"{stock.stock.unite}: stock CRITIQUE ({stock.quantite} ≤ {stock.quantite_critique})")
    return "✅ Stock OK" if not alerts else "⚠️ Stock critique détecté :\n" + "\n".join(alerts)


@tool
def get_installed_equipment_by_system(systeme_nom: str) -> str:
    """Retourne tous les équipements installés dans un système donné."""
    systemes = Systeme.objects.filter(nom__iexact=systeme_nom)
    if not systemes.exists():
        return f"Aucun système nommé '{systeme_nom}' trouvé."
    systeme = systemes.first()
    instances = EquipementSystemeInstance.objects.filter(sous_systeme_has_instance__systeme=systeme)

    if not instances.exists():
        return f"Aucun équipement installé dans le système '{systeme_nom}'."

    lines = [f"- {i.sous_systeme_has_instance.equipement.nom} (SN: {i.numero_serie})"
             for i in instances]
    return f"Équipements installés dans '{systeme_nom}' :\n" + "\n".join(lines)

@tool
def lister_unites_organisationnelles(input: str) -> str:
    """
    Liste les noms des unités organisationnelles disponibles (Bion, Cie, Sion) selon l'entrée.
    Utilise le mot-clé contenu dans 'input' pour déterminer si on liste les Bion, les Cie ou les Sion.
    """

    if "bion" in input.lower():
        noms = Bion.objects.values_list("nom", flat=True)
        liste = " ".join(noms)
        return f"📋 Noms des bataillons (Bion) : {liste}"

    elif "cie" in input.lower():
        noms = Cie.objects.values_list("nom", flat=True)
        liste = " ".join(noms)
        return f"📋 Noms des compagnies (Cie) : {liste}"

    elif "sion" in input.lower():
        noms = Sion.objects.values_list("nom", flat=True)
        liste = " ".join(noms)
        return f"📋 Noms des sections (Sion) : {liste}"

    else:
        return "Je n’ai pas compris si vous souhaitez les noms des Bion, Cie ou Sion."


@tool

def lister_personnel(role: str) -> str:
    """
    Retourne un tableau avec les informations des techniciens ou servants :
    nom, prénom, matricule, date d'affectation.
    Le paramètre 'role' doit être 'technicien' ou 'servant'.
    """
    role = role.strip().lower()

    if role == "technicien":
        personnels = Technicien.objects.all()
        if not personnels:
            return "Aucun technicien trouvé."

        header = "| Nom | Prénom | Matricule | Date d'affectation |\n"
        header += "|-----|--------|-----------|---------------------|\n"
        lignes = [
            f"| {t.nom} | {t.prenom} | {t.matricule} | {t.date_affectation.strftime('%Y-%m-%d') if t.date_affectation else 'N/A'} |"
            for t in personnels
        ]
        return f"👷 Techniciens :\n\n{header}" + "\n".join(lignes)

    elif role == "servant":
        personnels = Servant.objects.all()
        if not personnels:
            return "Aucun servant trouvé."

        header = "| Nom | Prénom | Matricule | Date d'affectation |\n"
        header += "|-----|--------|-----------|---------------------|\n"
        lignes = [
            f"| {s.nom} | {s.prenom} | {s.matricule} | {s.date_affectation.strftime('%Y-%m-%d') if s.date_affectation else 'N/A'} |"
            for s in personnels
        ]
        return f"🧑‍🚒 Servants :\n\n{header}" + "\n".join(lignes)

    else:
        return "❌ Rôle non reconnu. Veuillez préciser 'technicien' ou 'servant'."
