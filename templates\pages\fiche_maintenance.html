{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
.image-equipement {
  width: 100%;
  height: 200px;
  object-fit: contain;
  border: 1px solid #ccc;
  border-radius: 8px;
  cursor: pointer;
  transition: 0.3s ease-in-out;
}

#lightbox {
  display: none;
  position: fixed;
  z-index: 1000;
  padding-top: 80px;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.9);
}

#lightbox img {
  margin: auto;
  display: block;
  max-width: 90%;
  max-height: 80%;
}

#lightbox-close {
  position: absolute;
  top: 20px;
  right: 40px;
  font-size: 40px;
  color: white;
  text-decoration: none;
}

.card {
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.card-header {
  background-color: #007bff;
  color: #ffffff;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  font-weight: bold;
}

.text-primary {
  color: #007bff !important;
}

.badge-success {
  background-color: #28a745;
}

.fiche-container {
  padding: 20px;
}

hr {
  border-top: 2px solid #007bff;
  margin: 20px 0;
}
</style>

<div class="card mb-3" style="max-width: 900px; margin: 20px auto; padding: 20px;">
  <div class="card-header text-center">
    {% if maintenance.type_maintenance == 'PANNE' %} FICHE DE PANNE {% else %} FICHE DE MAINTENANCE {% endif %}
  </div>

  <div class="fiche-container">
    <div class="row mb-3">
      <div class="col-md-6">
        <h5 class="text-primary">Informations générales :</h5>
        <p><strong>Date de la maintenance :</strong> {{ maintenance.date_maintenance }}</p>
        <p id="id_type_maintenance" data-type="{{ maintenance.type_maintenance }}">
          <strong>Type :</strong> {{ maintenance.type_maintenance }}
        </p>
        <p><strong>Compagnie :</strong> {{ maintenance.cie.nom }}</p>
        <p><strong>Section :</strong> {{ maintenance.sion.nom }}</p>
        <p><strong>Technicien :</strong> {{ maintenance.technicien_nom }} {{ maintenance.technicien_prenom }}</p>
        <p><strong>Système :</strong> {{ maintenance.systeme.nom }}</p>
      </div>
      <div class="col-md-6 text-center">
        {% if equipement_media.media %}
        <img src="{{ equipement_media.media.url }}" alt="Image de l'équipement" class="img-fluid rounded image-equipement img-thumbnail">
        {% else %}
        <p>Aucune image disponible</p>
        {% endif %}
      </div>
    </div>

    <hr>

    <div class="row mb-3">
      <div class="col-md-12">
        <h5 class="text-primary">Équipement :</h5>
      </div>
      <div class="col-md-4"><p><strong>Nom :</strong> {{ maintenance.equipement.nom }}</p></div>
      <div class="col-md-4"><p><strong>N° Série :</strong> {{ numero_serie }}</p></div>
      <div class="col-md-4" id="nouveau-equipement-div"><p><strong>Nouveau équipement :</strong> {{ maintenance.equipement_de_rechange }}</p></div>
      <div class="col-md-4"><p><strong>Catégorie :</strong> {{ maintenance.sous_systeme.nom }}</p></div>
      <div class="col-md-4">
        <p><strong>Statut Système :</strong>
          <span class="badge {% if maintenance.statut_systeme == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
            {% if maintenance.statut_systeme == 'EN PANNE' %}En panne{% else %}Opérationnel{% endif %}
          </span>
        </p>
      </div>
      <div class="col-md-4">
        <p><strong>Statut Equipement :</strong>
          <span class="badge {% if maintenance.statut_equipement == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
            {% if maintenance.statut_equipement == 'EN PANNE' %}En panne{% else %}Opérationnel{% endif %}
          </span>
        </p>
      </div>
    </div>

    <hr>

    <h5 class="text-primary">Images associées :</h5>
    <div class="row">
      {% if images %}
        {% for img in images %}
          <div class="col-md-4 mb-3 text-center">
            <img src="{{ img.image.url }}" class="img-fluid rounded img-thumbnail image-equipement" alt="Image liée à la maintenance">
            {% if img.description %}
              <p class="mt-2 text-muted">{{ img.description }}</p>
            {% endif %}
          </div>
        {% endfor %}
      {% else %}
        <p class="text-muted">Aucune image n’est associée à cette maintenance.</p>
      {% endif %}
    </div>

    <hr>

    <h5 class="text-primary">Description de l'Intervention :</h5>
    <p>{{ maintenance.description }}</p>
  </div>
</div>

<!-- Lightbox modal -->
<div id="lightbox">
  <a href="#" id="lightbox-close">&times;</a>
  <img id="lightbox-img" src="" alt="Image Agrandie">
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
  const typeMaintenance = document.getElementById('id_type_maintenance');
  const divNouveauEquipement = document.getElementById('nouveau-equipement-div');
  const typeMaintenanceValue = typeMaintenance.getAttribute('data-type');

  if (typeMaintenanceValue === 'PANNE' || typeMaintenanceValue === 'PREVENTIVE') {
    divNouveauEquipement.style.display = 'none';
  } else {
    divNouveauEquipement.style.display = 'block';
  }

  const images = document.querySelectorAll('.image-equipement');
  const lightbox = document.getElementById('lightbox');
  const lightboxImg = document.getElementById('lightbox-img');
  const closeBtn = document.getElementById('lightbox-close');

  images.forEach(img => {
    img.addEventListener('click', function () {
      lightboxImg.src = this.src;
      lightbox.style.display = 'block';
    });
  });

  closeBtn.addEventListener('click', function (e) {
    e.preventDefault();
    lightbox.style.display = 'none';
  });

  lightbox.addEventListener('click', function (e) {
    if (e.target !== lightboxImg) {
      lightbox.style.display = 'none';
    }
  });
});
</script>

{% endblock %}
