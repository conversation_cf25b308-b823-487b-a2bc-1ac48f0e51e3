from django import forms
from .models import Maintenance, Technicien,EquipementStockInstance,EquipementSystemeInstance
from .models import MaintenanceHistory

class MaintenanceForm(forms.ModelForm):
    STATUT_CHOICES = [
        ('OPS', 'Opérationnel'),
        ('EN PANNE', 'En panne'),
    ]
    TYPE_MAINTENANCE = [
        ('CORRECTIVE', 'CORRECTIVE'),
        ('PREVENTIVE', 'PREVENTIVE'),
        ('PANNE', 'PANNE'),
    ]

    equipementsystemeInstance = forms.ModelChoiceField(
        queryset=EquipementSystemeInstance.objects.filter(date_remplacement__isnull=True),
        required=False,
        label="Équipement en service",
         widget=forms.Select(attrs={'class': 'form-control'})
    )
    equipement_de_rechange = forms.ModelChoiceField(
        queryset=EquipementStockInstance.objects.filter(date_sortie__isnull=True),
        required=False,
        label="Équipement en stock",
         widget=forms.Select(attrs={'class': 'form-control'})
    )
 
 
   
    statut_systeme = forms.ChoiceField(choices=STATUT_CHOICES, widget=forms.Select(attrs={'class': 'form-control'}))
    statut_sous_systeme = forms.ChoiceField(
    choices=STATUT_CHOICES,
    widget=forms.Select(attrs={'class': 'form-control'}),
    required=False  # Ajout de required=False
)
    statut_equipement = forms.ChoiceField(choices=STATUT_CHOICES, widget=forms.Select(attrs={'class': 'form-control'}))
    type_maintenance=forms.ChoiceField(choices=TYPE_MAINTENANCE, required=False, widget=forms.Select(attrs={'class': 'form-control' }))
     
    class Meta:
        model = Maintenance
        fields = [
            'type_maintenance',
            'statut_sous_systeme',
            'statut_systeme',
            'statut_equipement',
            'date_maintenance',
            'duree',
            'description',
            'equipement',
            'equipementsystemeInstance',
            'equipement_de_rechange',
            'sous_systeme',
            'systeme',
            'bion',
            'cie',
            'sion',
            'technicien',
            'reference_entree',
            'reference_sortie',
            'reference_declaration_panne',
            'date_sortie_stock',
            'destination',
        ]
        widgets = {
            'date_maintenance': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'date_sortie_stock': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}  ),
            'reference_entree': forms.TextInput(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'TO N°'}),
            'reference_sortie': forms.TextInput(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'TO N°'}),
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Décrivez la maintenance'}),
            'destination': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Destination'}),
            'type_maintenance': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Type de maintenance'}),
            'duree': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Durée en minutes'}),
            'equipement': forms.Select(attrs={'class': 'form-control'}),
           
            'sous_systeme': forms.Select(attrs={'class': 'form-control'}),
            'systeme': forms.Select(attrs={'class': 'form-control'}),
            'bion': forms.Select(attrs={'class': 'form-control' }),
            'cie': forms.Select(attrs={'class': 'form-control' }),
            'sion': forms.Select(attrs={'class': 'form-control' }),
            
            'technicien': forms.Select(attrs={'class': 'form-control'}), 
            'reference_declaration_panne':forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Référence'}),
        }


from django import forms
from .models import MaintenanceHistory,Formation

class MaintenanceHistoryForm(forms.ModelForm):
    class Meta:
        model = MaintenanceHistory
        fields = [
            'date_entree_atelier',
            'description_entree_atelier',
            'lieu_reparation',
            'date_envoi_reparation_externe',
            'description_envoi_reparation_externe',
            'lieu_reparation_externe',
            'date_reparation',
            'description_reparation',
            'statut'
        ]
        widgets = {
            'date_entree_atelier': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description_entree_atelier': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Description entrée atelier'}),
            'lieu_reparation': forms.TextInput(attrs={'class': 'form-control'}),
            'date_envoi_reparation_externe': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description_envoi_reparation_externe': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Description envoi réparation externe'}),
            'lieu_reparation_externe': forms.TextInput(attrs={'class': 'form-control'}),
            'date_reparation': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description_reparation': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Description de la réparation'}),
             'statut': forms.Select(attrs={'class': 'form-control'}, choices=MaintenanceHistory.STATUS_CHOICES),
        }

    def save(self, commit=True):
        instance = super().save(commit=False)
        if commit:
            instance.save()
        return instance
    
from django import forms
from .models import MaintenanceHistory

class AtelierForm(forms.ModelForm):
    class Meta:
        model = MaintenanceHistory
        fields = [
            'date_entree_atelier',
            'description_entree_atelier',
            'lieu_reparation',
            'statut_atelier'
        ]
        widgets = {
            'date_entree_atelier': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description_entree_atelier': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Description entrée atelier'}),
            'lieu_reparation': forms.TextInput(attrs={'class': 'form-control'}),
            'statut_atelier': forms.Select(attrs={'class': 'form-control'}, choices=MaintenanceHistory.STATUS_CHOICES),
        }
class ReparationExterneForm(forms.ModelForm):
    class Meta:
        model = MaintenanceHistory
        fields = [
            'date_envoi_reparation_externe',
            'description_envoi_reparation_externe',
            'lieu_reparation_externe',
            'statut_externe'
        ]
        widgets = {
            'date_envoi_reparation_externe': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description_envoi_reparation_externe': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Description envoi réparation externe'}),
            'lieu_reparation_externe': forms.TextInput(attrs={'class': 'form-control'}),
            'statut_externe': forms.Select(attrs={'class': 'form-control'}, choices=MaintenanceHistory.STATUS_CHOICES),
        }

class DetailsReparationForm(forms.ModelForm):
    class Meta:
        model = MaintenanceHistory
        fields = [
            'date_reparation',
            'description_reparation',
            'statut_reparation'
        ]
        widgets = {
            'date_reparation': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'description_reparation': forms.Textarea(attrs={'rows': 4, 'class': 'form-control', 'placeholder': 'Description de la réparation'}),
            'statut_reparation': forms.Select(attrs={'class': 'form-control'}, choices=MaintenanceHistory.STATUS_CHOICES),
        }









class FormationForm(forms.ModelForm):
    class Meta:
        model = Formation
        fields = ['intitule', 'reference', 'description', 'badge']
        widgets = {
            'intitule': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Entrez l\'intitulé'}),
            'reference': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Entrez la référence'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Entrez une description'}),
            'badge': forms.FileInput(attrs={'class': 'form-control'}),
        }