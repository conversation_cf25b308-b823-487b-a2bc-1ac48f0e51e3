{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-5">
  <form method="POST" enctype="multipart/form-data">
    {% csrf_token %}
    <input type="hidden" name="current_step" id="current_step">

    <!-- 🛠 Équipement -->
    <div class="card mb-4 shadow-sm">
      <div class="card-header bg-primary text-white">Détails de l'Équipement</div>
      <div class="card-body">
        <div class="row mb-2">
          <div class="col-md-4"><strong>Système :</strong> {{ maintenance_instance.systeme.nom }}</div>
          <div class="col-md-4"><strong>Catégorie :</strong> {{ maintenance_instance.equipementsystemeInstance.sous_systeme_has_instance.sous_systeme.nom }}</div>
          <div class="col-md-4"><strong>Équipement :</strong> <span class="badge bg-info">{{ maintenance_instance.equipement.nom }}</span></div>
        </div>
        <div class="row">
          <div class="col-md-12"><strong>Description de la panne :</strong> <span class="badge bg-warning text-dark">{{ maintenance_instance.description_panne }}</span></div>
        </div>
      </div>
    </div>

    <!-- 🔧 Entrée Atelier -->
    <div class="card mb-4">
      <div class="card-header bg-secondary text-white">Entrée en Atelier</div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-3"><label>Date :</label>
            {% if is_atelier_filled and edit_step != 'atelier' %}
              <p>{{ maintenance_instance.date_entree_atelier }}</p>
            {% else %}
              {{ atelier_form.date_entree_atelier }}
            {% endif %}
          </div>
          <div class="col-md-3"><label>Lieu :</label>
            {% if is_atelier_filled and edit_step != 'atelier' %}
              <p>{{ maintenance_instance.lieu_reparation }}</p>
            {% else %}
              {{ atelier_form.lieu_reparation }}
            {% endif %}
          </div>
          <div class="col-md-3"><label>Description :</label>
            {% if is_atelier_filled and edit_step != 'atelier' %}
              <p>{{ maintenance_instance.description_entree_atelier }}</p>
            {% else %}
              {{ atelier_form.description_entree_atelier }}
            {% endif %}
          </div>
          <div class="col-md-3"><label>Statut :</label>
            {% if is_atelier_filled and edit_step != 'atelier' %}
              <p>{{ maintenance_instance.get_statut_atelier_display }}</p>
            {% else %}
              {{ atelier_form.statut_atelier }}
            {% endif %}
          </div>
        </div>
        <div class="text-end">
          {% if is_atelier_filled %}
            <a href="?edit_step=atelier" class="btn btn-outline-dark btn-sm"><i class="bi bi-pencil"></i> Modifier</a>
          {% endif %}
          {% if not is_atelier_filled or edit_step == 'atelier' %}
            <button type="submit" class="btn btn-success" onclick="setCurrentStep('atelier')">Enregistrer</button>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- 🛠 Réparation Externe -->
    <div class="card mb-4">
      <div class="card-header bg-dark text-white">Réparation Externe</div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-3"><label>Date :</label>
            {% if is_externe_filled and edit_step != 'externe' %}
              <p>{{ maintenance_instance.date_envoi_reparation_externe }}</p>
            {% else %}
              {{ repar_externe_form.date_envoi_reparation_externe }}
            {% endif %}
          </div>
          <div class="col-md-3"><label>Lieu :</label>
            {% if is_externe_filled and edit_step != 'externe' %}
              <p>{{ maintenance_instance.lieu_reparation_externe }}</p>
            {% else %}
              {{ repar_externe_form.lieu_reparation_externe }}
            {% endif %}
          </div>
          <div class="col-md-3"><label>Description :</label>
            {% if is_externe_filled and edit_step != 'externe' %}
              <p>{{ maintenance_instance.description_envoi_reparation_externe }}</p>
            {% else %}
              {{ repar_externe_form.description_envoi_reparation_externe }}
            {% endif %}
          </div>
          <div class="col-md-3"><label>Statut :</label>
            {% if is_externe_filled and edit_step != 'externe' %}
              <p>{{ maintenance_instance.get_statut_externe_display }}</p>
            {% else %}
              {{ repar_externe_form.statut_externe }}
            {% endif %}
          </div>
        </div>
        <div class="text-end">
          {% if is_externe_filled %}
            <a href="?edit_step=externe" class="btn btn-outline-dark btn-sm"><i class="bi bi-pencil"></i> Modifier</a>
          {% endif %}
          {% if not is_externe_filled or edit_step == 'externe' %}
            <button type="submit" class="btn btn-success" onclick="setCurrentStep('externe')">Enregistrer</button>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- 🔧 Détails Réparation -->
    <div class="card mb-4">
      <div class="card-header bg-success text-white">Détails de la Réparation</div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-4"><label>Date :</label>
            {% if is_reparation_filled and edit_step != 'reparation' %}
              <p>{{ maintenance_instance.date_reparation }}</p>
            {% else %}
              {{ details_reparation_form.date_reparation }}
            {% endif %}
          </div>
          <div class="col-md-4"><label>Description :</label>
            {% if is_reparation_filled and edit_step != 'reparation' %}
              <p>{{ maintenance_instance.description_reparation }}</p>
            {% else %}
              {{ details_reparation_form.description_reparation }}
            {% endif %}
          </div>
          <div class="col-md-4"><label>Statut :</label>
            {% if is_reparation_filled and edit_step != 'reparation' %}
              <p>{{ maintenance_instance.get_statut_reparation_display }}</p>
            {% else %}
              {{ details_reparation_form.statut_reparation }}
            {% endif %}
          </div>
        </div>
        <div class="text-end">
          {% if is_reparation_filled %}
            <a href="?edit_step=reparation" class="btn btn-outline-dark btn-sm"><i class="bi bi-pencil"></i> Modifier</a>
          {% endif %}
          {% if not is_reparation_filled or edit_step == 'reparation' %}
            <button type="submit" class="btn btn-success" onclick="setCurrentStep('reparation')">Enregistrer</button>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- ✔ Bouton final -->
    <div class="text-center mt-4">
      <button type="submit" class="btn btn-primary btn-lg" onclick="setCurrentStep('final')">
        <i class="bi bi-check-circle"></i> Valider
      </button>
    </div>
  </form>
</div>

<script>
  function setCurrentStep(step) {
    document.getElementById('current_step').value = step;
  }
</script>
{% endblock %}
