 

{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
#datatablesSimple td,
#datatablesSimple th,
#systemes-table td,
#systemes-table th {
    text-align: center !important; /* Centre horizontalement */
    vertical-align: middle !important; /* Centre verticalement */
}
</style>
<div class="container-fluid px-4" >
     <ol class="breadcrumb mb-4" style="margin-top: 10px;">
        <li class="breadcrumb-item">Maintenance</li>
        <li class="breadcrumb-item active"><a href="{% url 'panne' %}">Pannes</a></li>
    </ol>
 
    <div class="card mb-4" style="margin-top: 10px;">
        <div class="card-header" >
            <i class="fas fa-table me-1"></i>
            Historique des pannes
       
            
        </div>
        <div class="card-body">
            <div class="table-responsive"></div>
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>id</th>
                        <th>Équipement</th>
                        <th>N° Serie</th>
                        <th>Statut</th>                                    
                        <th>Date de panne</th>
                        <th>Date d'entée en atelier</th>
                         
                        <th>Position actuelle</th>
                        <th>Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for history in histories %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ history.equipement.nom|default:"Non spécifié" }}</td>
                        <td> {{ history.equipementsystemeInstance.numero_serie }}</td>
                        <td>
                            <span class="badge {% if history.statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                                {{ history.statut|default:"Non spécifié" }}    
                            </span>    
                        </td>
                        <td>
                            {{ history.date_panne|default:"Non spécifié" }}
                        </td>
                        <td> {{ history.date_entree_atelier|default:"Non spécifié" }}</td>
                     
                        <td>{{ history.lieu_reparation|default:"Non spécifié" }}</td>
                        <td>{{ history.date_reparation|default:"Non spécifié" }}</td>
                      
                        <!-- <td>{{ maintenance.description |default:"Non spécifié" }}</td> -->
                        <td class="d-flex justify-content-center gap-2" style=" text-align: center;">

                            <a href="{% url 'fiche_suivie_reparation' history.id %}" class="btn btn-success btn-sm">
                                <i class="fa-solid fa-eye fa-sm"></i>
                            </a>
                            {% if history.statut != "Terminé" %}
                                <a href="{% url 'create_maintenance_history' history.id %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-wrench fa-sm"></i>
                                </a>
                                {% elif history.statut == "Terminé"  %}
                                 <a href="{% url 'create_maintenance_history' history.id %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-wrench fa-sm"></i>
                                </a>
                                {% endif %}
                               
                           
                        </td>
                       
                    </tr>
                    {% endfor %}
                </tbody>
               
            </table>
        </div>
    </div>
</div>




    <script>
        const addModal = document.getElementById('addData');
        addModal.addEventListener('show.bs.modal', function () {
            document.getElementById('addForm').reset(); // Réinitialiser le formulaire
        });
        
    </script>
{% endblock %}

 
 