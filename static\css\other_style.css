#selections-demo {
    border: 1px solid var(--im-border-color);
    margin-top: 1rem;
    padding: 1rem;
}
 

.center-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    vertical-align: middle;
}

.center-buttons .btn {
    margin-right: 10px;
}

.center-buttons .btn:last-child {
    margin-right: 0;
}
.image-equipement{

    width: 120px;  /* <PERSON>lle fixe */
        height: 120px; /* <PERSON>lle fixe */
        max-width: 150px;  /* Taille maximale */
        max-height: 150px; /* Taille maximale */
        border: 1px solid #ccc;
        border-radius: 10px;
        padding: 5px;
        background: #fff;
        object-fit: contain; /* S'assure que l'image ne soit pas déformée */
}

 

#datatablesSimple td,
#datatablesSimple th,
#systemes-table td,
#systemes-table th {
    text-align: center !important; /* Centre horizontalement */
    vertical-align: middle !important; /* Centre verticalement */
}
 