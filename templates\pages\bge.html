{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>


    #datatablesSimple td,
#datatablesSimple th,
#systemes-table td,
#systemes-table th {
    text-align: center !important; /* Centre horizontalement */
    vertical-align: middle !important; /* Centre verticalement */
}
</style>
<div class="container-fluid px-5">
    <ol class="breadcrumb mb-4" style="margin-top: 10px;">
        <li class="breadcrumb-item">Stock</a></li>
        <li class="breadcrumb-item active"><a href="{% url 'bge' %}">BGE</a></li>
    </ol>
 
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Stock BGE
            <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addData">
                <i class="fa-solid fa-plus"></i>
            </a>
          
        </div>
        <div class="card-body">
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>Équipement</th>
                        <th>Sous-système</th>
                        <th>N° Serie</th>
                        <th>Date Entrée</th>
                        <th>Date Sortie</th>
                        <th>Action</th>
                    </tr>
                </thead>
            
                <tbody>
                    {% for equipement_info in equipements_info %}
                    {% for instance in equipement_info.instances %}
                    {% if instance.date_sortie is None or instance.date_sortie == "" %}
                        <tr>
                            <td>{{ equipement_info.equipement.nom }}</td>
                            <td>{{ equipement_info.sous_systeme.nom }}</td>
                            <td>{{ instance.numero_serie }}</td>
                            <td>{{ instance.date_entree }}</td>
                            <td>{{ instance.date_sortie }}</td>
                            <td class="d-flex justify-content-center gap-2" >
                                <!-- Button to open modal and pass data -->
                                <a 
                                class="btn btn-primary btn-sm" 
                                data-bs-toggle="modal" 
                                data-bs-target="#readData" 
                                        data-equipement="{{ equipement_info.equipement.nom }}"
                                        data-sous_systeme="{{ equipement_info.sous_systeme.nom }}"
                                        data-stock="{{ equipement_info.stock }}" 
                                        data-quantite="{{ equipement_info.quantite }}" 
                                        style="margin-right: 10px;">
                                    <i class="fa-solid fa-eye"></i>
                            </a>
                                <a 
                                class="btn btn-secondary btn-sm" 
                                data-bs-toggle="modal" 
                                data-bs-target="#editData" 
                                        data-id="{{ instance.id }}"
                                        data-numero_serie="{{ instance.numero_serie }}"
                                        data-date_entree="{{ instance.date_entree }}"
                                        data-date_sortie="{{ instance.date_sortie }}"
                                        style="margin-right: 10px;">
                                    <i class="fa-solid fa-pencil"></i>
                        </a>
                                <a 
                                class="btn btn-danger btn-sm" 
                                data-bs-toggle="modal" 
                                data-bs-target="#deleteData"
                                data-id="{{ instance.id }}">
                            <i class="fa-solid fa-trash"></i>
                    </a>
                            </td>
                        </tr> 
                   
                        {% endif %}
                        {% endfor %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="modal fade" id="readData" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLabel">Titre du Modal</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
    
           
           
    
              <!-- Contenu dynamique du modal sera ajouté ici -->
              <p>Sous-systèmes: <span id="modal-sous_systeme"></span></p>
              <p>Stock: <span id="modal-stock"></span></p>
              <p>Quantité: <span id="modal-quantite"></span></p>
              <p>Imgae: <span id="modal-image"></span></p>
              <img src="{% static 'img/far.png' %}" alt="Image descriptive" class="img-fluid"/>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
          </div>
        </div>
      </div>
      




    <!-- Modal to Edit Data -->
    <div class="modal fade" id="editData" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">Modifier l'équipement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Form to edit equipment -->
                    <form id="editForm" method="POST" action="{% url 'edit_equipment' %}">
                        {% csrf_token %}
                        <input type="hidden" id="instance-id" name="instance_id">
                        <div class="mb-3">
                            <label for="numero_serie" class="form-label">Numéro de série</label>
                            <input type="text" class="form-control" id="numero_serie" name="numero_serie">
                        </div>
                        <div class="mb-3">
                            <label for="date_entree" class="form-label">Date d'entrée</label>
                            <input type="date" class="form-control" id="date_entree" name="date_entree">
                        </div>
                        <div class="mb-3">
                            <label for="date_sortie" class="form-label">Date de sortie</label>
                            <input type="date" class="form-control" id="date_sortie" name="date_sortie">
                        </div>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteData" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Voulez-vous vraiment supprimer cet équipement ? Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <form id="deleteForm" method="POST" action="">
                    {% csrf_token %}
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'ajout d'un équipement -->
<div class="modal fade" id="addData" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addModalLabel">Ajouter un équipement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <div class="modal-body text-center">
                <div class="d-flex justify-content-center gap-2">
                    <a href="{% url 'ajouter_equipement' %}" class="btn btn-primary">Nouvel équipement</a>
                    <a href="{% url 'ajouter_quantite_stock' %}" class="btn btn-primary">Ajouter quantité</a>
                </div>
            </div>
        </div>
    </div>
</div>




 
    <script>
        // Script pour peupler le modal avec les données de l'équipement
        const modal = document.getElementById('readData');
        modal.addEventListener('show.bs.modal', function (event) {
            // Récupérer les données de l'équipement depuis les attributs data-* du bouton
            const button = event.relatedTarget; // Le bouton qui a déclenché le modal
            const equipement = button.getAttribute('data-equipement');
            const sous_systeme = button.getAttribute('data-sous_systeme');
            const stock = button.getAttribute('data-stock');
            const quantite = button.getAttribute('data-quantite');

            // Peupler le modal avec ces données
            const modalTitle = modal.querySelector('.modal-title');
            const modalBody = modal.querySelector('.modal-body');

            modalTitle.textContent = equipement;
            modalBody.innerHTML = `
                <p>Sous-système: ${sous_systeme}</p>
                <p>Stock: ${stock}</p>
                <p>Quantité: ${quantite}</p>
            `;
        });


        function formatDate(date) {
            const d = new Date(date);
            const day = String(d.getDate()).padStart(2, '0');
            const month = String(d.getMonth() + 1).padStart(2, '0'); // Les mois commencent à 0
            const year = d.getFullYear();
            return `${year}-${month}-${day}`;
        }
        const editModal = document.getElementById('editData');
        editModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const instanceId = button.getAttribute('data-id');
            const numeroSerie = button.getAttribute('data-numero_serie');
            const dateEntree = button.getAttribute('data-date_entree');
            const dateSortie = button.getAttribute('data-date_sortie');
           // Formater la date au format yyyy-mm-dd
            const dateEntreeFormatted = formatDate(dateEntree);
            const dateSortieFormatted = formatDate(dateSortie);
        
            // Peupler les champs du formulaire avec les données actuelles
            document.getElementById('instance-id').value = instanceId;
            document.getElementById('numero_serie').value = numeroSerie;
            document.getElementById('date_entree').value = dateEntreeFormatted;
            document.getElementById('date_sortie').value = dateSortieFormatted;
        });

        const deleteModal = document.getElementById('deleteData');
        deleteModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const instanceId = button.getAttribute('data-id');
            
            // Modifier l'action du formulaire avec l'ID de l'équipement
            const deleteForm = document.getElementById('deleteForm');
            deleteForm.action = `/delete_equipment/${instanceId}/`;
        });
       
        const addModal = document.getElementById('addData');
        addModal.addEventListener('show.bs.modal', function () {
            document.getElementById('addForm').reset(); // Réinitialiser le formulaire
        });

    </script>
    
</div>

{% endblock %}
