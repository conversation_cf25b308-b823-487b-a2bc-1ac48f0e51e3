{% extends 'base.html' %}
{% load static %}
{% block content %}
  
<div class="container" style="margin-top: 10px;">
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item">Système</li>
        <li class="breadcrumb-item active">{{systeme_id}}</li>
    </ol>


    <div class="card mb-4 custom-card-background">
    <div class="card-header text-primary btn-primary" >
        <i class="fas fa-info me-1"></i>
        Détails:
    </div>
    <div class="card-body">
        <div class="accordion" id="accordionExample">
            <!-- Boucle pour générer un accordéon pour chaque sous-système -->
            {% for sous_systeme, equipements in equipements_par_sous_systeme.items %}
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                    <button 
                        class="accordion-button {% if not forloop.first %}collapsed{% endif %}" 
                        type="button" 
                        data-bs-toggle="collapse" 
                        data-bs-target="#collapse{{ forloop.counter }}" 
                        aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}" 
                        aria-controls="collapse{{ forloop.counter }}">
                        {{ sous_systeme.nom }} <!-- Nom du sous-système -->
                    </button>
                </h2>
                <div 
                    id="collapse{{ forloop.counter }}" 
                    class="accordion-collapse collapse {% if forloop.first %}show{% endif %}" 
                    aria-labelledby="heading{{ forloop.counter }}" 
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <div class="card mb-4">
                            <div class="card-body">
                                <table id="datatablesSimple" class="table table-bordered">
                                    <thead class="text-center">
                                        <tr class="text-center">
                                            <th class="text-center">N°</th>
                                            <th class="text-center">Désignation</th>
                                            <th class="text-center">Quantité</th>
                                            <th class="text-center">Numéro de produit</th>
                                            <th class="text-center">Numéro de série</th>
                                             
                                            <th class="text-center">Marque</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Boucle pour afficher les équipements du sous-système -->
                                        {% for equipement in equipements %}
                                        <tr>
                                            <td class="text-center">{{ forloop.counter }}</td>
                                            <td class="text-center">{{ equipement.designation }}</td>
                                            <td class="text-center">{{ equipement.quantite }}</td>
                                            <td class="text-center">{{ equipement.product_number }}</td>
                                            <td class="text-center">
                                                
                                                    {% for numero_serie, instance_id in equipement.serial_numbers_with_instances %}
                                                        {% if numero_serie != "Non spécifié" and instance_id %}
                                                            <a href="{% url 'fiche_equipement' equipement.equi_id instance_id %}">{{ numero_serie }}</a><br>
                                                        {% else %}
                                                            Non spécifié<br>
                                                        {% endif %}
                                                    {% endfor %}
                                                
                                            </td>
                                         
                                            <td class="text-center">
                                                {{ equipement.marque }}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
</div>
{% endblock %}