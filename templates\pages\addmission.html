{% extends 'base.html' %}
{% load static %}
{% block content %}

<div class="container py-3">
    <div class="card shadow-lg border-0 rounded-3 mb-4" style="margin-top: 0px; max-width: 95%; margin-left: auto; margin-right: auto;">
        <div class="card-header bg-primary text-white py-2">
            <h4 class="mb-0 text-center">Création d'une nouvelle mission</h4>
        </div>
        <div class="card-body p-4">
            <form action="" method="POST" enctype="multipart/form-data">
                {% csrf_token %}

                <!-- Sélection des unités -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Unités</h5>
                    </div>
                    <div class="card-body">
                        <!-- Bataillon -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Bataillon :</h6>
                            <div class="row row-cols-2 row-cols-md-4 g-3">
                                {% for bataillon in bataillons %}
                                <div class="col">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="bataillon" id="bataillon_{{ bataillon.id }}" value="{{ bataillon.id }}">
                                        <label class="form-check-label" for="bataillon_{{ bataillon.id }}">
                                            {{ bataillon.nom }}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="mt-3">
                                <button type="button" id="reset-bataillon" class="btn btn-outline-secondary btn-sm">
                                    <i class="fa-solid fa-rotate"></i> Réinitialiser
                                </button>
                            </div>
                        </div>

                        <hr class="my-3">

                        <!-- Compagnies -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Compagnies :</h6>
                            <div class="row row-cols-2 row-cols-md-4 g-3">
                                {% for compagnie in compagnies %}
                                <div class="col">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="compagnie_{{ compagnie.id }}" name="compagnies[]" value="{{ compagnie.id }}">
                                        <label class="form-check-label" for="compagnie_{{ compagnie.id }}">{{ compagnie.nom }}</label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <hr class="my-3">

                        <!-- Systèmes -->
                        <div>
                            <h6 class="fw-bold mb-3">Systèmes :</h6>
                            <div id="systemes-container" class="row row-cols-2 row-cols-md-4 g-3">
                                {% for systeme in systemes %}
                                <div class="col">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="systeme_{{ systeme.id }}" name="systemes[]" value="{{ systeme.id }}">
                                        <label class="form-check-label" for="systeme_{{ systeme.id }}">{{ systeme.nom }}</label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations générales -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Informations générales</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="objet" class="form-label fw-bold">Objet</label>
                                <input type="text" class="form-control" id="objet" name="objet" required>
                            </div>
                            <div class="col-md-6">
                                <label for="reference_mission" class="form-label fw-bold">Référence</label>
                                <input type="text" class="form-control" id="reference_mission" name="reference_mission" required>
                            </div>
                            <div class="col-md-6">
                                <label for="date_debut_mission" class="form-label fw-bold">Date début</label>
                                <input type="date" class="form-control" id="date_debut_mission" name="date_debut_mission" required>
                            </div>
                            <div class="col-md-6">
                                <label for="date_fin_mission" class="form-label fw-bold">Date fin</label>
                                <input type="date" class="form-control" id="date_fin_mission" name="date_fin_mission" required>
                            </div>
                            <div class="col-md-12">
                                <label for="lieu" class="form-label fw-bold">Lieu</label>
                                <input type="text" class="form-control" id="lieu" name="lieu" required>
                            </div>
                            <div class="col-md-6">
                                <label for="id_latitude_sex" class="form-label fw-bold">Latitude</label>
                                <input type="text" class="form-control" id="id_latitude_sex" name="latitude_sex" placeholder='Ex : N 32°22&#39;22"' required>
                                <div class="form-text">Format : N ou S suivi de degrés, minutes et secondes</div>
                            </div>
                            <div class="col-md-6">
                                <label for="id_longitude_sex" class="form-label fw-bold">Longitude</label>
                                <input type="text" class="form-control" id="id_longitude_sex" name="longitude_sex" placeholder='Ex : E 06°51&#39;43"' required>
                                <div class="form-text">Format : E ou W suivi de degrés, minutes et secondes</div>
                            </div>
                            <div class="col-md-12">
                                <label for="description" class="form-label fw-bold">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Participants -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Participants</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-3">
                                <h6 class="fw-bold mb-3">Techniciens :</h6>
                                <div class="mb-3">
                                    <select class="form-select" id="selected_techniciens" multiple>
                                        {% for technicien in techniciens %}
                                            <option value="{{ technicien.id }}">{{ technicien.nom }} {{ technicien.prenom }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">Maintenez Ctrl pour sélectionner plusieurs techniciens</div>
                                </div>
                                <div id="techniciens-container" class="mb-3"></div>
                                <button type="button" class="btn btn-outline-success w-100" id="add-technicien-btn">
                                    <i class="fa-solid fa-plus"></i> Ajouter technicien
                                </button>
                            </div>
                            <div class="col-md-3">
                                <h6 class="fw-bold mb-3">Servants :</h6>
                                <div class="mb-3">
                                    <select class="form-select" id="selected_servants" multiple>
                                        {% for servant in servants %}
                                            <option value="{{ servant.id }}">{{ servant.nom }} {{ servant.prenom }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">Maintenez Ctrl pour sélectionner plusieurs servants</div>
                                </div>
                                <div id="servants-container" class="mb-3"></div>
                                <button type="button" class="btn btn-outline-success w-100" id="add-servant-btn">
                                    <i class="fa-solid fa-plus"></i> Ajouter servant
                                </button>
                            </div>
                            <div class="col-md-3">
                                <h6 class="fw-bold mb-3">Conducteurs :</h6>
                                <div class="mb-3">
                                    <select class="form-select" id="selected_conducteurs" multiple>
                                        {% for conducteur in conducteurs %}
                                            <option value="{{ conducteur.id }}">{{ conducteur.nom }} {{ conducteur.prenom }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">Maintenez Ctrl pour sélectionner plusieurs conducteurs</div>
                                </div>
                                <div id="conducteurs-container" class="mb-3"></div>
                                <button type="button" class="btn btn-outline-success w-100" id="add-conducteur-btn">
                                    <i class="fa-solid fa-plus"></i> Ajouter conducteur
                                </button>
                            </div>
                            <div class="col-md-3">
                                <h6 class="fw-bold mb-3">Autres participants :</h6>
                                <div id="autres-container" class="mb-3"></div>
                                <button type="button" class="btn btn-outline-success w-100" data-bs-toggle="modal" data-bs-target="#autreParticipantModal">
                                    <i class="fa-solid fa-plus"></i> Ajouter autre participant
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Soumission -->
                <div class="d-flex justify-content-center">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="fa-solid fa-check me-2"></i>Valider la mission
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal pour Autre Participant -->
<div class="modal fade" id="autreParticipantModal" tabindex="-1" aria-labelledby="autreParticipantModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="autreParticipantModalLabel">Ajouter un autre participant</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="autreParticipantForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="autre_nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="autre_nom" required>
                        </div>
                        <div class="col-md-6">
                            <label for="autre_prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="autre_prenom" required>
                        </div>
                        <div class="col-md-6">
                            <label for="autre_matricule" class="form-label">Matricule</label>
                            <input type="text" class="form-control" id="autre_matricule">
                        </div>
                        <div class="col-md-6">
                            <label for="autre_unite" class="form-label">Unité</label>
                            <input type="text" class="form-control" id="autre_unite">
                        </div>
                        <div class="col-md-6">
                            <label for="autre_date_arrivee" class="form-label">Date d'arrivée</label>
                            <input type="date" class="form-control" id="autre_date_arrivee">
                        </div>
                        <div class="col-md-6">
                            <label for="autre_date_depart" class="form-label">Date de départ</label>
                            <input type="date" class="form-control" id="autre_date_depart">
                        </div>
                        <div class="col-md-6">
                            <label for="autre_specialite" class="form-label">Spécialité</label>
                            <input type="text" class="form-control" id="autre_specialite">
                        </div>
                        <div class="col-md-6">
                            <label for="autre_role" class="form-label">Rôle dans la mission</label>
                            <input type="text" class="form-control" id="autre_role" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="saveAutreParticipant">Ajouter</button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function () {
    // === Chargement dynamique des systèmes en fonction des compagnies cochées ===
    const compagnieCheckboxes = document.querySelectorAll('input[name="compagnies[]"]');
    const systemesContainer = document.getElementById('systemes-container');

    function updateSystemes() {
        const selectedCies = Array.from(compagnieCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        const url = new URL('/get-systemes-by-cies/', window.location.origin);
        selectedCies.forEach(id => url.searchParams.append('cies[]', id));

        // Sauvegarder les systèmes actuellement sélectionnés
        const selectedSystemes = Array.from(document.querySelectorAll('input[name="systemes[]"]:checked'))
            .map(cb => cb.value);

        fetch(url)
            .then(response => response.json())
            .then(data => {
                systemesContainer.innerHTML = '';
                if (data.systemes.length === 0) {
                    systemesContainer.innerHTML = "<p class='text-muted'>Aucun système disponible</p>";
                } else {
                    // Créer un Set pour éviter les doublons
                    const uniqueSystemes = new Map();
                    
                    // Ajouter tous les systèmes au Map (pour éviter les doublons)
                    data.systemes.forEach(sys => {
                        uniqueSystemes.set(sys.id, sys);
                    });
                    
                    // Créer les éléments HTML pour chaque système unique
                    uniqueSystemes.forEach(sys => {
                        const col = document.createElement('div');
                        col.className = 'col';
                        
                        const div = document.createElement('div');
                        div.className = 'form-check';
                        div.innerHTML = `
                            <input class="form-check-input" type="checkbox" id="systeme_${sys.id}" name="systemes[]" value="${sys.id}" ${selectedSystemes.includes(sys.id.toString()) ? 'checked' : ''}>
                            <label class="form-check-label" for="systeme_${sys.id}">${sys.nom}</label>
                        `;
                        
                        col.appendChild(div);
                        systemesContainer.appendChild(col);
                    });
                }
            });
    }

    compagnieCheckboxes.forEach(cb => cb.addEventListener('change', updateSystemes));

    // === Ajout dynamique de techniciens, servants et conducteurs avec rôle ===
    let technicienCount = 0;
    let servantCount = 0;
    let conducteurCount = 0;

    const addTechnicienBtn = document.getElementById('add-technicien-btn');
    const techniciensContainer = document.getElementById('techniciens-container');
    const selectedTechniciens = document.getElementById('selected_techniciens');

    addTechnicienBtn.addEventListener('click', function () {
        const selectedOptions = Array.from(selectedTechniciens.selectedOptions);
        if (selectedOptions.length === 0) {
            alert("Veuillez sélectionner au moins un technicien.");
            return;
        }

        selectedOptions.forEach(option => {
            const newGroup = document.createElement('div');
            newGroup.classList.add('participant-group', 'card', 'mb-3', 'shadow-sm');
            newGroup.setAttribute('data-participant-id', option.value);

            newGroup.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <strong>${option.text}</strong>
                    <button type="button" class="btn btn-sm btn-danger remove-participant-btn">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <input type="hidden" name="technicien_id[]" value="${option.value}">
                    <input type="hidden" name="technicien[]" value="${option.text}">
                    <div class="mb-2">
                        <label class="form-label small">Rôle :</label>
                        <input type="text" class="form-control" name="role_technicien[]" maxlength="100" required>
                    </div>
                </div>
            `;
            techniciensContainer.appendChild(newGroup);
            technicienCount++;
        });
        selectedTechniciens.selectedIndex = -1;
    });

    const addServantBtn = document.getElementById('add-servant-btn');
    const servantsContainer = document.getElementById('servants-container');
    const selectedServants = document.getElementById('selected_servants');

    addServantBtn.addEventListener('click', function () {
        const selectedOptions = Array.from(selectedServants.selectedOptions);
        if (selectedOptions.length === 0) {
            alert("Veuillez sélectionner au moins un servant.");
            return;
        }

        selectedOptions.forEach(option => {
            const newGroup = document.createElement('div');
            newGroup.classList.add('participant-group', 'card', 'mb-3', 'shadow-sm');
            newGroup.setAttribute('data-participant-id', option.value);

            newGroup.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <strong>${option.text}</strong>
                    <button type="button" class="btn btn-sm btn-danger remove-participant-btn">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <input type="hidden" name="servant_id[]" value="${option.value}">
                    <input type="hidden" name="servant[]" value="${option.text}">
                    <div class="mb-2">
                        <label class="form-label small">Rôle :</label>
                        <input type="text" class="form-control" name="role_servant[]" maxlength="100" required>
                    </div>
                </div>
            `;
            servantsContainer.appendChild(newGroup);
            servantCount++;
        });
        selectedServants.selectedIndex = -1;
    });

    // Ajout des conducteurs
    const addConducteurBtn = document.getElementById('add-conducteur-btn');
    const conducteursContainer = document.getElementById('conducteurs-container');
    const selectedConducteurs = document.getElementById('selected_conducteurs');

    addConducteurBtn.addEventListener('click', function () {
        const selectedOptions = Array.from(selectedConducteurs.selectedOptions);
        if (selectedOptions.length === 0) {
            alert("Veuillez sélectionner au moins un conducteur.");
            return;
        }

        selectedOptions.forEach(option => {
            const newGroup = document.createElement('div');
            newGroup.classList.add('participant-group', 'card', 'mb-3', 'shadow-sm');
            newGroup.setAttribute('data-participant-id', option.value);

            newGroup.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <strong>${option.text}</strong>
                    <button type="button" class="btn btn-sm btn-danger remove-participant-btn">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <input type="hidden" name="conducteur_id[]" value="${option.value}">
                    <input type="hidden" name="conducteur[]" value="${option.text}">
                    <div class="mb-2">
                        <label class="form-label small">Rôle :</label>
                        <input type="text" class="form-control" name="role_conducteur[]" maxlength="100" required>
                    </div>
                </div>
            `;
            conducteursContainer.appendChild(newGroup);
            conducteurCount++;
        });
        selectedConducteurs.selectedIndex = -1;
    });

    // === Suppression dynamique des blocs participants ===
    document.addEventListener('click', function (e) {
        if (e.target.closest('.remove-participant-btn')) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce participant ?')) {
                e.target.closest('.participant-group').remove();
                showNotification('Participant supprimé', 'info');
            }
        }

        // === Modification des autres participants ===
        if (e.target.closest('.edit-participant-btn')) {
            const participantGroup = e.target.closest('.participant-group');
            editAutreParticipant(participantGroup);
        }
    });

    // Réinitialisation du bataillon
    const resetBataillonBtn = document.getElementById('reset-bataillon');
    resetBataillonBtn.addEventListener('click', function() {
        // Récupérer tous les boutons radio de bataillon
        const bataillonRadios = document.querySelectorAll('input[name="bataillon"]');

        // Décocher tous les boutons radio
        bataillonRadios.forEach(radio => {
            radio.checked = false;
        });
    });

    // === Gestion des autres participants ===
    let autreParticipantCount = 0;
    const autresContainer = document.getElementById('autres-container');
    const saveAutreParticipantBtn = document.getElementById('saveAutreParticipant');
    const autreParticipantForm = document.getElementById('autreParticipantForm');
    const autreParticipantModal = new bootstrap.Modal(document.getElementById('autreParticipantModal'));

    saveAutreParticipantBtn.addEventListener('click', function() {
        // Récupérer les valeurs du formulaire
        const nom = document.getElementById('autre_nom').value.trim();
        const prenom = document.getElementById('autre_prenom').value.trim();
        const matricule = document.getElementById('autre_matricule').value.trim();
        const unite = document.getElementById('autre_unite').value.trim();
        const dateArrivee = document.getElementById('autre_date_arrivee').value;
        const dateDepart = document.getElementById('autre_date_depart').value;
        const specialite = document.getElementById('autre_specialite').value.trim();
        const role = document.getElementById('autre_role').value.trim();

        // Validation des champs obligatoires
        if (!nom || !prenom || !role) {
            alert('Veuillez remplir au minimum les champs Nom, Prénom et Rôle.');
            return;
        }

        // Vérifier si on est en mode modification
        const editingParticipantId = this.getAttribute('data-editing-participant');

        if (editingParticipantId) {
            // Mode modification
            const existingParticipant = document.querySelector(`[data-participant-id="${editingParticipantId}"]`);
            if (existingParticipant) {
                updateParticipantDisplay(existingParticipant, nom, prenom, matricule, unite, dateArrivee, dateDepart, specialite, role);
                showNotification('Participant modifié avec succès !', 'warning');
            }

            // Réinitialiser le mode modification
            resetModalToAddMode();
        } else {
            // Mode ajout
            // Créer un identifiant unique pour ce participant
            autreParticipantCount++;
            const participantId = 'autre_' + autreParticipantCount;
            createNewParticipant(participantId, nom, prenom, matricule, unite, dateArrivee, dateDepart, specialite, role);
            showNotification('Participant ajouté avec succès !', 'success');
        }

        // Réinitialiser le formulaire et fermer le modal
        autreParticipantForm.reset();
        autreParticipantModal.hide();
    });

    // Fonction pour créer un nouveau participant
    function createNewParticipant(participantId, nom, prenom, matricule, unite, dateArrivee, dateDepart, specialite, role) {

        // Créer l'élément HTML pour afficher le participant
        const participantElement = document.createElement('div');
        participantElement.classList.add('participant-group', 'card', 'mb-3', 'shadow-sm');
        participantElement.setAttribute('data-participant-id', participantId);

        participantElement.innerHTML = generateParticipantHTML(nom, prenom, matricule, unite, dateArrivee, dateDepart, specialite, role);

        // Ajouter le participant à la liste
        autresContainer.appendChild(participantElement);
    }

    // Fonction pour mettre à jour l'affichage d'un participant existant
    function updateParticipantDisplay(participantElement, nom, prenom, matricule, unite, dateArrivee, dateDepart, specialite, role) {
        participantElement.innerHTML = generateParticipantHTML(nom, prenom, matricule, unite, dateArrivee, dateDepart, specialite, role);
    }

    // Fonction pour générer le HTML d'un participant
    function generateParticipantHTML(nom, prenom, matricule, unite, dateArrivee, dateDepart, specialite, role) {
        return `
            <div class="card-header d-flex justify-content-between align-items-center bg-info text-white">
                <strong>${prenom} ${nom}</strong>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-warning edit-participant-btn" title="Modifier">
                        <i class="fa-solid fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-danger remove-participant-btn" title="Supprimer">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Champs cachés pour l'envoi du formulaire -->
                <input type="hidden" name="autre_participant_nom[]" value="${nom}">
                <input type="hidden" name="autre_participant_prenom[]" value="${prenom}">
                <input type="hidden" name="autre_participant_matricule[]" value="${matricule}">
                <input type="hidden" name="autre_participant_unite[]" value="${unite}">
                <input type="hidden" name="autre_participant_date_arrivee[]" value="${dateArrivee}">
                <input type="hidden" name="autre_participant_date_depart[]" value="${dateDepart}">
                <input type="hidden" name="autre_participant_specialite[]" value="${specialite}">
                <input type="hidden" name="autre_participant_role[]" value="${role}">

                <!-- Affichage des informations -->
                <div class="row g-2">
                    ${matricule ? `<div class="col-md-6"><small class="text-muted">Matricule:</small><br><span class="fw-medium">${matricule}</span></div>` : ''}
                    ${unite ? `<div class="col-md-6"><small class="text-muted">Unité:</small><br><span class="fw-medium">${unite}</span></div>` : ''}
                    <div class="col-md-12"><small class="text-muted">Rôle:</small><br><span class="fw-medium">${role}</span></div>
                    ${specialite ? `<div class="col-md-6"><small class="text-muted">Spécialité:</small><br><span class="fw-medium">${specialite}</span></div>` : ''}
                    ${dateArrivee ? `<div class="col-md-6"><small class="text-muted">Date d'arrivée:</small><br><span class="fw-medium">${new Date(dateArrivee).toLocaleDateString('fr-FR')}</span></div>` : ''}
                    ${dateDepart ? `<div class="col-md-6"><small class="text-muted">Date de départ:</small><br><span class="fw-medium">${new Date(dateDepart).toLocaleDateString('fr-FR')}</span></div>` : ''}
                </div>
            </div>
        `;
    }

    // Fonction pour réinitialiser le modal en mode ajout
    function resetModalToAddMode() {
        document.getElementById('autreParticipantModalLabel').textContent = 'Ajouter un autre participant';
        const saveBtn = document.getElementById('saveAutreParticipant');
        saveBtn.textContent = 'Ajouter';
        saveBtn.className = 'btn btn-primary';
        saveBtn.removeAttribute('data-editing-participant');
    }

    // Réinitialiser le modal quand il se ferme
    document.getElementById('autreParticipantModal').addEventListener('hidden.bs.modal', function() {
        resetModalToAddMode();
        autreParticipantForm.reset();
    });

    // Fonction pour modifier un autre participant
    function editAutreParticipant(participantGroup) {
        // Récupérer les données actuelles du participant
        const nom = participantGroup.querySelector('input[name="autre_participant_nom[]"]').value;
        const prenom = participantGroup.querySelector('input[name="autre_participant_prenom[]"]').value;
        const matricule = participantGroup.querySelector('input[name="autre_participant_matricule[]"]').value;
        const unite = participantGroup.querySelector('input[name="autre_participant_unite[]"]').value;
        const dateArrivee = participantGroup.querySelector('input[name="autre_participant_date_arrivee[]"]').value;
        const dateDepart = participantGroup.querySelector('input[name="autre_participant_date_depart[]"]').value;
        const specialite = participantGroup.querySelector('input[name="autre_participant_specialite[]"]').value;
        const role = participantGroup.querySelector('input[name="autre_participant_role[]"]').value;

        // Remplir le formulaire du modal avec les données existantes
        document.getElementById('autre_nom').value = nom;
        document.getElementById('autre_prenom').value = prenom;
        document.getElementById('autre_matricule').value = matricule;
        document.getElementById('autre_unite').value = unite;
        document.getElementById('autre_date_arrivee').value = dateArrivee;
        document.getElementById('autre_date_depart').value = dateDepart;
        document.getElementById('autre_specialite').value = specialite;
        document.getElementById('autre_role').value = role;

        // Changer le titre du modal
        document.getElementById('autreParticipantModalLabel').textContent = 'Modifier le participant';

        // Changer le texte du bouton
        const saveBtn = document.getElementById('saveAutreParticipant');
        saveBtn.textContent = 'Modifier';
        saveBtn.className = 'btn btn-warning';

        // Stocker la référence du participant en cours de modification
        saveBtn.setAttribute('data-editing-participant', participantGroup.getAttribute('data-participant-id'));

        // Ouvrir le modal
        autreParticipantModal.show();
    }

    // Fonction pour afficher des notifications
    function showNotification(message, type = 'info') {
        // Créer l'élément de notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Ajouter au DOM
        document.body.appendChild(notification);

        // Supprimer automatiquement après 3 secondes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
});
</script>



{% endblock %}
