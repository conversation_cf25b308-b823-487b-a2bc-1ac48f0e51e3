<div class="row" style="display: flex; margin-left: 10px; margin-top: 10px;"></div>
<div class="container mt-5">
    <h2 class="mb-4"><PERSON>réer une Mission</h2>
    <form>

        <div class="mb-3 row">
            <div class="col-md-6">
                <label for="titre" class="form-label">Titre de la mission</label>
                <input type="text" class="form-control" id="titre" placeholder="Entrez le titre de la mission" required>
            </div>
            <div class="col-md-6">
                <label for="ref" class="form-label">Reference de la mission</label>
                <input type="text" class="form-control" id="titre" placeholder="Entrez la reference de la mission" required>
            </div>
        </div>

 

        <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" id="description" rows="3" placeholder="Entrez la description" required></textarea>
        </div>
        <div class="mb-3 row">
            <div class="col-md-6">
                <label for="dateDebut" class="form-label">Date de début</label>
                <input type="date" class="form-control" id="dateDebut" required>
            </div>
            <div class="col-md-6">
                <label for="dateFin" class="form-label">Date de fin</label>
                <input type="date" class="form-control" id="dateFin" required>
            </div>
        </div>
    
        <div class="mb-3">
            <label class="form-label">Équipe</label>
            <div id="equipe-container"></div>
            <button type="button" class="btn btn-success mt-2" onclick="ajouterMembre()">➕ Ajouter un membre</button>
        </div>

        <button type="submit" class="btn btn-primary">Créer la Mission</button>
    </form>
</div>

<script>
    let membreCount = 0;

    function ajouterMembre() {
        membreCount++;
        const container = document.getElementById('equipe-container');
        const div = document.createElement('div');
        div.classList.add('card', 'mb-3', 'shadow-sm');
        div.setAttribute('id', 'membre-' + membreCount);

        div.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center">
                <strong>Membre ${membreCount}</strong>
                <div>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="toggleDetails(${membreCount})">🔽</button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="supprimerMembre(${membreCount})">➖</button>
                </div>
            </div>
            <div id="details-${membreCount}" class="card-body">
                <div class="mb-2">
                    <label class="form-label">Nom</label>
                    <input type="text" class="form-control" placeholder="Nom" required>
                </div>
                <div class="mb-2">
                    <label class="form-label">Prénom</label>
                    <input type="text" class="form-control" placeholder="Prénom" required>
                </div>
                <div class="mb-2">
                    <label class="form-label">Matricule</label>
                    <input type="text" class="form-control" placeholder="Matricule" required>
                </div>
                <div class="mb-2">
                    <label class="form-label">Rôle</label>
                    <select class="form-control">
                        <option value="">Sélectionnez un rôle</option>
                        <option value="Chef d'équipe">Chef d'équipe</option>
                        <option value="Technicien">Technicien</option>
                        <option value="Opérateur">Opérateur</option>
                    </select>
                </div>
            </div>
        `;

        container.appendChild(div);
    }

    function supprimerMembre(id) {
        const membre = document.getElementById('membre-' + id);
        if (membre) {
            membre.remove();
        }
    }

    function toggleDetails(id) {
        const details = document.getElementById('details-' + id);
        const btn = document.querySelector(`#membre-${id} .btn-secondary`);
        if (details.style.display === 'none') {
            details.style.display = 'block';
            btn.innerText = '🔽';
        } else {
            details.style.display = 'none';
            btn.innerText = '🔼';
        }
    }
</script>
  

        