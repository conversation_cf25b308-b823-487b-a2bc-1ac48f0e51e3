{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Liste des Équipements</h1>
    <pre>{{ equipements }}</pre>


    {% for equipement in equipements %}
    <p>{{ equipement.nom }}</p>
    {% empty %}
    <p>Aucun équipement disponible.</p>
    {% endfor %}
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Nom</th>
                <th>Constructeur</th>
                <th>Numéro de produit</th>
                <th>Date d'installation</th>
                <th>Date de fin de garantie</th>
                <th>MTTR (min)</th>
                <th>MTBF (h)</th>
                <th>Criticité</th>
                <th>Media</th>
            </tr>
        </thead>
        <tbody>
            {% for equipement in equipements %}
                <tr>
                    <td>{{ equipement.nom }}</td>
                    <td>{{ equipement.constructeur }}</td>
                    <td>{{ equipement.product_numbre }}</td>
                    <td>{{ equipement.date_installation }}</td>
                    <td>{{ equipement.date_fin_garantie }}</td>
                    <td>{{ equipement.mttr }}</td>
                    <td>{{ equipement.mtbf }}</td>
                    <td>{{ equipement.criticite }}</td>
                    <td>
                        {% if equipement.media %}
                            <a href="{{ equipement.media.url }}" target="_blank">Télécharger</a>
                        {% else %}
                            Aucune pièce jointe
                        {% endif %}
                    </td>
                </tr>
            {% empty %}
                <tr>
                    <td colspan="9">Aucun équipement disponible</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
