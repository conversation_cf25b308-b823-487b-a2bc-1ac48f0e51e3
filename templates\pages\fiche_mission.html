{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-5">
    <div class="text-center mb-4">
        <h2 class="fw-bold">Fiche de Mission</h2>
    </div>

    <div class="accordion" id="missionAccordion">

        <!-- Informations Générales -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingGen">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGen" aria-expanded="true">
                    Informations Générales
                </button>
            </h2>
            <div id="collapseGen" class="accordion-collapse collapse show" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    <p><strong>Objet :</strong> {{ mission.objet }}</p>
                    <p><strong>Référence :</strong> {{ mission.reference_mission }}</p>
                    <p><strong>Responsable :</strong> {{ mission.user.first_name }} {{ mission.user.last_name }}</p>
                    <p><strong>Lieu :</strong> {{ mission.lieu }}</p>
                    {% if mission.latitude or mission.longitude %}
                    <p><strong>Coordonnées :</strong>
                        {% if mission.latitude %}Latitude: {{ mission.latitude }}{% endif %}
                        {% if mission.latitude and mission.longitude %} | {% endif %}
                        {% if mission.longitude %}Longitude: {{ mission.longitude }}{% endif %}
                    </p>
                    {% endif %}
                    <p><strong>Dates :</strong> du {{ mission.date_debut_mission }} au {{ mission.date_fin_mission }}</p>
                    <p><strong>Description :</strong> {{ mission.description }}</p>
                </div>
            </div>
        </div>

        <!-- Modules Déployés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingUnits">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseUnits">
                    Modules Déployés
                </button>
            </h2>
            <div id="collapseUnits" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">

                    {% if afficher_bion %}
                        <p><strong>Bataillon :</strong> {{ afficher_bion.nom }}</p>
                    {% endif %}

                    {% if afficher_cies %}
                        <p><strong>Compagnies :</strong>
                            {% for cie in afficher_cies %}
                                {{ cie.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if afficher_sions %}
                        <p><strong>Sections :</strong>
                            {% for sion in afficher_sions %}
                                {{ sion.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if not afficher_bion and not afficher_cies and not afficher_sions %}
                        <p class="text-muted">Aucune unité associée.</p>
                    {% endif %}

                </div>
            </div>
        </div>

        <!-- Techniciens Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingTech">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTech">
                    Techniciens Affectés
                </button>
            </h2>
            <div id="collapseTech" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if technicien_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in technicien_roles %}
                            <tr>
                                <td>{{ r.technicien.nom }}</td>
                                <td>{{ r.technicien.prenom }}</td>
                                <td>{{ r.technicien.grade }}</td>
                                <td>{{ r.technicien.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun technicien affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Servants Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingServant">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseServant">
                    Servants Affectés
                </button>
            </h2>
            <div id="collapseServant" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if servant_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in servant_roles %}
                            <tr>
                                <td>{{ r.servant.nom }}</td>
                                <td>{{ r.servant.prenom }}</td>
                                <td>{{ r.servant.grade }}</td>
                                <td>{{ r.servant.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun servant affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Conducteurs Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingConducteur">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseConducteur">
                    Conducteurs Affectés
                </button>
            </h2>
            <div id="collapseConducteur" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if conducteur_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in conducteur_roles %}
                            <tr>
                                <td>{{ r.conducteur.nom }}</td>
                                <td>{{ r.conducteur.prenom }}</td>
                                <td>{{ r.conducteur.grade }}</td>
                                <td>{{ r.conducteur.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun conducteur affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Autres Participants -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingAutres">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAutres">
                    Autres Participants
                </button>
            </h2>
            <div id="collapseAutres" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if autre_personnel_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Matricule</th>
                                <th>Unité</th>
                                <th>Spécialité</th>
                                <th>Date d'arrivée</th>
                                <th>Date de départ</th>
                                <th>Rôle</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for r in autre_personnel_roles %}
                            <tr>
                                <td>{{ r.autre_personnel.nom }}</td>
                                <td>{{ r.autre_personnel.prenom }}</td>
                                <td>{{ r.autre_personnel.matricule|default:"-" }}</td>
                                <td>{{ r.autre_personnel.unite|default:"-" }}</td>
                                <td>{{ r.autre_personnel.specialite|default:"-" }}</td>
                                <td>{{ r.autre_personnel.date_arrivee|default:"-" }}</td>
                                <td>{{ r.autre_personnel.date_depart|default:"-" }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun autre participant.</p>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>
</div>
{% endblock %}
