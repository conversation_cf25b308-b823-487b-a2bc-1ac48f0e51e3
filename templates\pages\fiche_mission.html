{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
    .mission-header {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
    }

    .mission-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .mission-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .accordion-button {
        background: #f8fafc;
        border: none;
        font-weight: 600;
        color: #1e293b;
        padding: 1.25rem 1.5rem;
        font-size: 1.1rem;
    }

    .accordion-button:not(.collapsed) {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        box-shadow: none;
    }

    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
    }

    .accordion-body {
        padding: 2rem;
        background: white;
    }

    .info-item {
        display: flex;
        margin-bottom: 1rem;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .info-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #475569;
        min-width: 140px;
        margin-right: 1rem;
    }

    .info-value {
        color: #1e293b;
        flex: 1;
    }

    .professional-table {
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .professional-table thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        color: #475569;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.05em;
        padding: 1rem 0.75rem;
        border: none;
    }

    .professional-table tbody td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #f1f5f9;
        vertical-align: middle;
    }

    .professional-table tbody tr:hover {
        background-color: #f8fafc;
    }

    .professional-table tbody tr:last-child td {
        border-bottom: none;
    }

    .badge-role {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: #64748b;
        font-style: italic;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .coordinates-badge {
        background: #f0f9ff;
        color: #0369a1;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        border: 1px solid #bae6fd;
    }
</style>

<div class="container-fluid py-4">
    <!-- Header professionnel -->
    <div class="mission-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2 fw-bold">
                    <i class="fas fa-clipboard-list me-3"></i>
                    Fiche de Mission
                </h1>
                <p class="mb-0 opacity-90">{{ mission.reference_mission }} - {{ mission.objet }}</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-column align-items-end">
                    <span class="badge bg-light text-primary px-3 py-2 mb-2">
                        <i class="fas fa-calendar me-1"></i>
                        {{ mission.date_debut_mission|date:"d/m/Y" }} - {{ mission.date_fin_mission|date:"d/m/Y" }}
                    </span>
                    <button class="btn btn-outline-light btn-sm" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>
                        Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="accordion" id="missionAccordion">

        <!-- Informations Générales -->
        <div class="accordion-item mission-card">
            <h2 class="accordion-header" id="headingGen">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGen" aria-expanded="true">
                    <i class="fas fa-info-circle me-2"></i>
                    Informations Générales
                </button>
            </h2>
            <div id="collapseGen" class="accordion-collapse collapse show" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-bullseye me-2 text-primary"></i>
                                    Objet :
                                </span>
                                <span class="info-value">{{ mission.objet }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-hashtag me-2 text-primary"></i>
                                    Référence :
                                </span>
                                <span class="info-value">{{ mission.reference_mission }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-user-tie me-2 text-primary"></i>
                                    Responsable :
                                </span>
                                <span class="info-value">{{ mission.user.first_name }} {{ mission.user.last_name }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                    Lieu :
                                </span>
                                <span class="info-value">{{ mission.lieu }}</span>
                            </div>
                            {% if mission.latitude or mission.longitude %}
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-crosshairs me-2 text-primary"></i>
                                    Coordonnées :
                                </span>
                                <span class="info-value">
                                    <div class="coordinates-badge">
                                        {% if mission.latitude %}{{ mission.latitude }}{% endif %}
                                        {% if mission.latitude and mission.longitude %} | {% endif %}
                                        {% if mission.longitude %}{{ mission.longitude }}{% endif %}
                                    </div>
                                </span>
                            </div>
                            {% endif %}
                            <div class="info-item">
                                <span class="info-label">
                                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                    Période :
                                </span>
                                <span class="info-value">
                                    {{ mission.date_debut_mission|date:"d/m/Y" }} - {{ mission.date_fin_mission|date:"d/m/Y" }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% if mission.description %}
                    <div class="info-item mt-3">
                        <span class="info-label">
                            <i class="fas fa-align-left me-2 text-primary"></i>
                            Description :
                        </span>
                        <span class="info-value">{{ mission.description }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Modules Déployés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingUnits">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseUnits">
                    Modules Déployés
                </button>
            </h2>
            <div id="collapseUnits" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">

                    {% if afficher_bion %}
                        <p><strong>Bataillon :</strong> {{ afficher_bion.nom }}</p>
                    {% endif %}

                    {% if afficher_cies %}
                        <p><strong>Compagnies :</strong>
                            {% for cie in afficher_cies %}
                                {{ cie.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if afficher_sions %}
                        <p><strong>Sections :</strong>
                            {% for sion in afficher_sions %}
                                {{ sion.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if afficher_systemes %}
                        <p><strong>Systèmes déployés :</strong>
                            {% for systeme in afficher_systemes %}
                                {{ systeme.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if not afficher_bion and not afficher_cies and not afficher_sions and not afficher_systemes %}
                        <p class="text-muted">Aucune unité ou système associé.</p>
                    {% endif %}

                </div>
            </div>
        </div>

        <!-- Techniciens Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingTech">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTech">
                    Techniciens Affectés
                </button>
            </h2>
            <div id="collapseTech" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if technicien_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in technicien_roles %}
                            <tr>
                                <td>{{ r.technicien.nom }}</td>
                                <td>{{ r.technicien.prenom }}</td>
                                <td>{{ r.technicien.grade }}</td>
                                <td>{{ r.technicien.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun technicien affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Servants Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingServant">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseServant">
                    Servants Affectés
                </button>
            </h2>
            <div id="collapseServant" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if servant_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in servant_roles %}
                            <tr>
                                <td>{{ r.servant.nom }}</td>
                                <td>{{ r.servant.prenom }}</td>
                                <td>{{ r.servant.grade }}</td>
                                <td>{{ r.servant.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun servant affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Conducteurs Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingConducteur">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseConducteur">
                    Conducteurs Affectés
                </button>
            </h2>
            <div id="collapseConducteur" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if conducteur_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in conducteur_roles %}
                            <tr>
                                <td>{{ r.conducteur.nom }}</td>
                                <td>{{ r.conducteur.prenom }}</td>
                                <td>{{ r.conducteur.grade }}</td>
                                <td>{{ r.conducteur.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun conducteur affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Autres Participants -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingAutres">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAutres">
                    Autres Participants
                </button>
            </h2>
            <div id="collapseAutres" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if autre_personnel_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Matricule</th>
                                <th>Unité</th>
                                <th>Spécialité</th>
                                <th>Date d'arrivée</th>
                                <th>Date de départ</th>
                                <th>Rôle</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for r in autre_personnel_roles %}
                            <tr>
                                <td>{{ r.autre_personnel.nom }}</td>
                                <td>{{ r.autre_personnel.prenom }}</td>
                                <td>{{ r.autre_personnel.matricule|default:"-" }}</td>
                                <td>{{ r.autre_personnel.unite|default:"-" }}</td>
                                <td>{{ r.autre_personnel.specialite|default:"-" }}</td>
                                <td>{{ r.autre_personnel.date_arrivee|default:"-" }}</td>
                                <td>{{ r.autre_personnel.date_depart|default:"-" }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun autre participant.</p>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>
</div>
{% endblock %}
