{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: #fafbfc;
        color: #2d3748;
        line-height: 1.6;
    }

    .mission-document {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-radius: 0;
    }

    .document-header {
        background: white;
        border-bottom: 3px solid #e2e8f0;
        padding: 2.5rem 3rem;
        position: relative;
    }

    .document-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #1a365d 0%, #2d3748 100%);
    }

    .mission-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1a365d;
        margin: 0 0 0.5rem 0;
        letter-spacing: -0.025em;
    }

    .mission-subtitle {
        font-size: 1.1rem;
        color: #4a5568;
        font-weight: 400;
        margin: 0;
    }

    .document-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e2e8f0;
    }

    .meta-item {
        font-size: 0.875rem;
        color: #718096;
    }

    .meta-value {
        font-weight: 600;
        color: #2d3748;
    }

    .section {
        border-bottom: 1px solid #e2e8f0;
    }

    .section:last-child {
        border-bottom: none;
    }

    .section-header {
        background: #f7fafc;
        padding: 1.5rem 3rem;
        border: none;
        font-weight: 600;
        font-size: 1rem;
        color: #2d3748;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .section-header:hover {
        background: #edf2f7;
    }

    .section-header:not(.collapsed) {
        background: #1a365d;
        color: white;
    }

    .section-content {
        padding: 2.5rem 3rem;
        background: white;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .info-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.25rem;
    }

    .info-row:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #4a5568;
        min-width: 120px;
        margin-right: 1rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .info-value {
        color: #2d3748;
        font-weight: 500;
        flex: 1;
    }

    .coordinates {
        background: #f0fff4;
        border: 1px solid #9ae6b4;
        color: #22543d;
        padding: 0.5rem 0.75rem;
        border-radius: 4px;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        font-size: 0.875rem;
        display: inline-block;
    }

    .professional-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
        font-size: 0.875rem;
    }

    .professional-table th {
        background: #f7fafc;
        color: #4a5568;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        padding: 1rem 0.75rem;
        text-align: left;
        border-bottom: 2px solid #e2e8f0;
    }

    .professional-table td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #f1f5f9;
        vertical-align: top;
    }

    .professional-table tbody tr:hover {
        background: #f7fafc;
    }

    .role-badge {
        background: #edf2f7;
        color: #4a5568;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .empty-message {
        text-align: center;
        color: #a0aec0;
        font-style: italic;
        padding: 2rem;
        background: #f7fafc;
        border-radius: 4px;
        margin-top: 1rem;
    }

    .print-button {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #1a365d;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        font-weight: 500;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }

    .print-button:hover {
        background: #2d3748;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    @media print {
        body { background: white; }
        .print-button { display: none; }
        .mission-document { box-shadow: none; }
        .section-header:not(.collapsed) { background: #f7fafc !important; color: #2d3748 !important; }
    }
</style>

<button class="print-button" onclick="window.print()">
    Imprimer
</button>

<div class="mission-document">
    <div class="document-header">
        <h1 class="mission-title">FICHE DE MISSION</h1>
        <p class="mission-subtitle">{{ mission.reference_mission }} — {{ mission.objet }}</p>

        <div class="document-meta">
            <div class="meta-item">
                <span class="meta-value">{{ mission.user.first_name }} {{ mission.user.last_name }}</span><br>
                Responsable de mission
            </div>
            <div class="meta-item text-end">
                <span class="meta-value">{{ mission.date_debut_mission|date:"d/m/Y" }} — {{ mission.date_fin_mission|date:"d/m/Y" }}</span><br>
                Période d'exécution
            </div>
        </div>
    </div>

    <div class="accordion" id="missionAccordion">

        <!-- Informations Générales -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingGen">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGen" aria-expanded="true">
                    Informations Générales
                </button>
            </h2>
            <div id="collapseGen" class="accordion-collapse collapse show" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    <p><strong>Objet :</strong> {{ mission.objet }}</p>
                    <p><strong>Référence :</strong> {{ mission.reference_mission }}</p>
                    <p><strong>Responsable :</strong> {{ mission.user.first_name }} {{ mission.user.last_name }}</p>
                    <p><strong>Lieu :</strong> {{ mission.lieu }}</p>
                    {% if mission.latitude or mission.longitude %}
                    <p><strong>Coordonnées :</strong>
                        {% if mission.latitude %}Latitude: {{ mission.latitude }}{% endif %}
                        {% if mission.latitude and mission.longitude %} | {% endif %}
                        {% if mission.longitude %}Longitude: {{ mission.longitude }}{% endif %}
                    </p>
                    {% endif %}
                    <p><strong>Dates :</strong> du {{ mission.date_debut_mission }} au {{ mission.date_fin_mission }}</p>
                    <p><strong>Description :</strong> {{ mission.description }}</p>
                </div>
            </div>
        </div>

        <!-- Modules Déployés -->
        <div class="accordion-item mission-card">
            <h2 class="accordion-header" id="headingUnits">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseUnits">
                    <i class="fas fa-cubes me-2"></i>
                    Modules Déployés
                </button>
            </h2>
            <div id="collapseUnits" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">

                    {% if afficher_bion %}
                        <p><strong>Bataillon :</strong> {{ afficher_bion.nom }}</p>
                    {% endif %}

                    {% if afficher_cies %}
                        <p><strong>Compagnies :</strong>
                            {% for cie in afficher_cies %}
                                {{ cie.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if afficher_sions %}
                        <p><strong>Sections :</strong>
                            {% for sion in afficher_sions %}
                                {{ sion.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if afficher_systemes %}
                        <p><strong>Systèmes déployés :</strong>
                            {% for systeme in afficher_systemes %}
                                {{ systeme.nom }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    {% endif %}

                    {% if not afficher_bion and not afficher_cies and not afficher_sions and not afficher_systemes %}
                        <div class="empty-message">
                            Aucune unité ou système associé à cette mission.
                        </div>
                    {% endif %}

                </div>
            </div>
        </div>

    <!-- Techniciens Affectés -->
    <div class="section">
        <button class="section-header w-100 text-start collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#section-techniciens">
            Techniciens Affectés
        </button>
        <div id="section-techniciens" class="collapse">
            <div class="section-content">
                {% if technicien_roles %}
                    <table class="professional-table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Grade</th>
                                <th>Spécialité</th>
                                <th>Rôle</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for r in technicien_roles %}
                            <tr>
                                <td>{{ r.technicien.nom }}</td>
                                <td>{{ r.technicien.prenom }}</td>
                                <td>{{ r.technicien.grade }}</td>
                                <td>{{ r.technicien.specialite }}</td>
                                <td><span class="role-badge">{{ r.role }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div class="empty-message">
                        Aucun technicien affecté à cette mission.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

        <!-- Servants Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingServant">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseServant">
                    Servants Affectés
                </button>
            </h2>
            <div id="collapseServant" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if servant_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in servant_roles %}
                            <tr>
                                <td>{{ r.servant.nom }}</td>
                                <td>{{ r.servant.prenom }}</td>
                                <td>{{ r.servant.grade }}</td>
                                <td>{{ r.servant.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun servant affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Conducteurs Affectés -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingConducteur">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseConducteur">
                    Conducteurs Affectés
                </button>
            </h2>
            <div id="collapseConducteur" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if conducteur_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr><th>Nom</th><th>Prénom</th><th>Grade</th><th>Spécialité</th><th>Rôle</th></tr>
                        </thead>
                        <tbody>
                            {% for r in conducteur_roles %}
                            <tr>
                                <td>{{ r.conducteur.nom }}</td>
                                <td>{{ r.conducteur.prenom }}</td>
                                <td>{{ r.conducteur.grade }}</td>
                                <td>{{ r.conducteur.specialite }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun conducteur affecté.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Autres Participants -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingAutres">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAutres">
                    Autres Participants
                </button>
            </h2>
            <div id="collapseAutres" class="accordion-collapse collapse" data-bs-parent="#missionAccordion">
                <div class="accordion-body">
                    {% if autre_personnel_roles %}
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Matricule</th>
                                <th>Unité</th>
                                <th>Spécialité</th>
                                <th>Date d'arrivée</th>
                                <th>Date de départ</th>
                                <th>Rôle</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for r in autre_personnel_roles %}
                            <tr>
                                <td>{{ r.autre_personnel.nom }}</td>
                                <td>{{ r.autre_personnel.prenom }}</td>
                                <td>{{ r.autre_personnel.matricule|default:"-" }}</td>
                                <td>{{ r.autre_personnel.unite|default:"-" }}</td>
                                <td>{{ r.autre_personnel.specialite|default:"-" }}</td>
                                <td>{{ r.autre_personnel.date_arrivee|default:"-" }}</td>
                                <td>{{ r.autre_personnel.date_depart|default:"-" }}</td>
                                <td>{{ r.role }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p class="text-muted">Aucun autre participant.</p>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>
</div>
{% endblock %}
