{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="card mb-3" style="max-width: 800px; margin-left: 10px; margin-top: 10px;">
    <h3 class="mb-4">Formulaire de Maintenance</h3>

    <!-- Ligne contenant les champs avant le type de maintenance -->
    <div class="row mb-3">
        <div class="col">
            <label class="form-label">Bataillon</label>
            <input type="text" class="form-control" name="bion">
        </div>
        <div class="col">
            <label class="form-label">Compagnie</label>
            <input type="text" class="form-control" name="cie">
        </div>
        <div class="col">
            <label class="form-label">Section</label>
            <input type="text" class="form-control" name="sion">
        </div>
        <div class="col">
            <label class="form-label">Système</label>
            <input type="text" class="form-control" name="systeme">
        </div>
        <div class="col">
            <label class="form-label">Catégorie</label>
            <input type="text" class="form-control" name="categorie">
        </div>
    </div>

    <!-- Sélection du type de maintenance -->
    <div class="mb-3">
        <label class="form-label">Type de Maintenance</label>
        <select class="form-select" id="type_maintenance">
            <option value="" selected>Choisir...</option>
            <option value="preventive">Préventive</option>
            <option value="corrective">Corrective</option>
        </select>
    </div>

   
   <!-- Formulaire de Maintenance Préventive (Caché par défaut) -->
<form id="form_preventive" style="display: none;">
    <h4>Maintenance Préventive</h4>

    <div class="row mb-3">
        <div class="col">
            <label class="form-label">Équipement</label>
            <input type="text" class="form-control" name="equipement">
        </div>
    

  
        <div class="col">
            <label class="form-label">Numéro de série</label>
            <input type="text" class="form-control" name="numero_serie">
        </div>
   

   
        <div class="col">
            <label class="form-label">Date de Maintenance</label>
            <input type="date" class="form-control" name="date_maintenance">
        </div>
    

 
        <div class="col">
            <label class="form-label">Durée </label>
            <input type="number" class="form-control" name="duree">
        </div>
    </div>

    <div class="row mb-3">
        <div class="col">
            <label class="form-label">Statut Système</label>
            <input type="text" class="form-control" name="statut_systeme">
        </div>
   

        <div class="col">
            <label class="form-label">Statut Équipement</label>
            <input type="text" class="form-control" name="statut_equipement">
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-12">
            <label class="form-label">Description</label>
            <textarea class="form-control" name="description" rows="3"></textarea>
        </div>
    </div>

    <button type="submit" class="btn btn-success">Enregistrer Maintenance</button>
</form>


    <!-- Formulaire de Réparation Corrective (Caché par défaut) -->
    <form id="form_corrective" style="display: none;">
        <h4>Réparation Corrective</h4>
        <div class="row mb-3">
            <div class="col">
            <label class="form-label">Maintenance associée</label>
            <select class="form-select" name="maintenance">
                <option selected>Choisir...</option>
                <!-- Options dynamiques remplies par Django -->
            </select>
        </div>
        <div class="col">
            <label class="form-label">Equipement</label>
            <input type="text" class="form-control" name="equipement">
        </div>
        <div class="col">
            <label class="form-label">Destination après échange</label>
            <input type="text" class="form-control" name="destination_apres_echange">
        </div>
    </div>
        <div class="mb-3">
            <label class="form-label">Date de Réception</label>
            <input type="date" class="form-control" name="date_reception">
        </div>
        <div class="mb-3">
            <label class="form-label">Date de Réparation</label>
            <input type="date" class="form-control" name="date_reparation">
        </div>
        <div class="mb-3">
            <label class="form-label">Statut Réparation</label>
            <input type="text" class="form-control" name="status_reparation">
        </div>
        <button type="submit" class="btn btn-danger">Enregistrer Réparation</button>
    </form>
</div>

<script>
    document.getElementById("type_maintenance").addEventListener("change", function() {
        var type = this.value;
        document.getElementById("form_preventive").style.display = (type === "preventive") ? "block" : "none";
        document.getElementById("form_corrective").style.display = (type === "corrective") ? "block" : "none";
    });
</script>

{% endblock %}
