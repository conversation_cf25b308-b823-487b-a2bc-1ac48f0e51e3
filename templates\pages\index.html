{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
    /* Variables CSS pour une cohérence visuelle */
    :root {
        --primary-color: #2563eb;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --secondary-color: #6b7280;
        --light-bg: #f8fafc;
        --white: #ffffff;
        --border-radius: 8px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .fc-toolbar-title {
        font-size: 20px !important;
    }
    .fc-col-header-cell-cushion{
        font-size: 10px !important;
    }
    body {
        background-color: var(--light-bg);
        font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
        color: #1f2937;
        line-height: 1.6;
    }

    /* Amélioration des cartes de statut */
    .status-card-modern {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        overflow: hidden;
        background: var(--white);
        height: 100%;
    }

    .status-card-modern:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-indicator.success { background-color: var(--success-color); }
    .status-indicator.danger { background-color: var(--danger-color); }
    .status-indicator.warning { background-color: var(--warning-color); }

    .calendar-container {
        padding: 20px;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .small-font {
        font-size: 0.8rem;
        font-weight: bold;
    }
    h1 {
        font-size: 2rem;
        color: #343a40;
        font-weight: 600;
        margin-bottom: 20px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    /* Style du calendrier */
    #calendar {
        max-width: 100%;
        height: 600px;
        border: none !important; /* Supprimer la bordure par défaut */
    }
    /* Style des jours du calendrier */
    .fc-daygrid-day {
        border: 1px solid #e9ecef !important; /* Bordure légère pour chaque jour */
        border-radius: 6px; /* Coins arrondis pour les cellules */
    }
    /* Style des événements */
    .fc-event {
        background-color: #0d6efd; /* Bleu Bootstrap pour les événements */
        border: none !important; /* Supprimer la bordure des événements */
        color: white; /* Texte en blanc */
        border-radius: 6px; /* Coins arrondis */
        font-size: 0.9rem; /* Taille de police ajustée */
        padding: 5px 10px; /* Espacement interne */
        cursor: pointer; /* Curseur en forme de main */
    }
    /* Style des boutons de navigation */
    .fc-button {
        background-color: #0d6efd; /* Bleu Bootstrap */
        border: none !important; /* Supprimer la bordure */
        color: white; /* Texte en blanc */
        font-size: 0.9rem; /* Taille de police ajustée */
        padding: 8px 16px; /* Espacement interne */
        border-radius: 6px; /* Coins arrondis */
        transition: background-color 0.3s ease; /* Effet de transition */
    }
    .fc-button:hover {
        background-color: #0b5ed7; /* Bleu plus foncé au survol */
    }
    /* Style du bouton "Today" */
    .fc-today-button {
        background-color: #198754; /* Vert Bootstrap */
    }
    .fc-today-button:hover {
        background-color: #157347; /* Vert plus foncé au survol */
    }
    /* Style de la barre d'outils */
    .fc-toolbar {
        margin-bottom: 20px; /* Espacement sous la barre d'outils */
    }
    /* Style du lien "+X événements" */
    .fc-more-link {
        display: block !important; /* Pour rendre le lien stylable comme un bloc */
        width: 100% !important; /* Prendre toute la largeur de la cellule */
        text-align: center; /* Centrer le texte horizontalement */
        background-color: #f0c817; /* Même couleur que les événements */
        border: none !important; /* Supprimer la bordure */
        color: white !important; /* Texte en blanc */
        border-radius: 6px; /* Coins arrondis */
        font-size: 0.9rem; /* Taille de police ajustée */
        padding: 5px 10px; /* Espacement interne */
        text-decoration: none !important; /* Supprimer le soulignement */
        cursor: pointer; /* Curseur en forme de main */
        box-sizing: border-box; /* Inclure le padding dans la largeur totale */
    }
    .fc-more-link:hover {
        background-color: #0b5ed7; /* Bleu plus foncé au survol */
    }
    /* Style du modal pour les détails d'événement */
    #eventDetailsModalBody {
        font-size: 0.9rem; /* Taille de police ajustée */
        line-height: 1.6; /* Espacement entre les lignes */
    }
    #eventDetailsModalBody p {
        margin-bottom: 10px; /* Espacement entre les paragraphes */
    }
    #eventDetailsModalBody strong {
        color: #0d6efd; /* Bleu Bootstrap pour les titres */
    }
    /* Style des séparateurs (hr) dans le modal */
    #eventDetailsModalBody hr {
        border: none; /* Supprimer la bordure par défaut */
        border-top: 1px solid #e9ecef; /* Ligne légère */
        margin: 15px 0; /* Espacement autour de la ligne */
    }

    /* Amélioration du tableau de stock */
    .stock-table {
        background: var(--white);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        border: none;
    }

    .stock-table .card-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
        color: white;
        border: none;
        padding: 1.5rem;
    }

    .stock-table .table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .stock-table .table thead th {
        background: #f8fafc;
        border-bottom: 2px solid #e2e8f0;
        font-weight: 600;
        color: #475569;
        padding: 1rem 0.75rem;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        border-top: none;
    }

    .stock-table .table tbody td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #f1f5f9;
        vertical-align: middle;
        border-left: none;
        border-right: none;
    }

    .stock-table .table tbody tr:hover {
        background-color: #f8fafc;
    }

    .warning-cell {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
        color: #92400e !important;
        font-weight: 600;
        position: relative;
    }

    .warning-cell::before {
        content: '⚠️';
        margin-right: 0.5rem;
    }

    .danger-cell {
        background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%) !important;
        color: #991b1b !important;
        font-weight: 600;
        position: relative;
    }

    .danger-cell::before {
        content: '🚨';
        margin-right: 0.5rem;
    }

    .stock-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .stock-badge.critical {
        background-color: #fee2e2;
        color: #991b1b;
    }

    .stock-badge.warning {
        background-color: #fef3c7;
        color: #92400e;
    }

    .stock-badge.normal {
        background-color: #dcfce7;
        color: #166534;
    }

    .product-code {
        background-color: #f1f5f9;
        color: #475569;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .brand-badge {
        background-color: #e0e7ff;
        color: #3730a3;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }
</style>
<div class="container-fluid px-4 center" style="margin-top:20px;">
     <ol class="breadcrumb mb-4" style="margin-top: 10px;">
        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
       
    </ol>
    <div class="row">
        <div class="col-xl-5" style="margin-top: 20px; margin-left: 10px;">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-shield-alt me-1"></i>
                    GE-R
                </div>
                <div class="card-body center">
                    <div class="row justify-content-center">
                        <!-- Première carte -->
                        <div class="col-md-4 mb-4">
                            <div>                  
                                <div class="card  text-white {% if results.esm_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                                    <div class="card-body small-font">ESM {{esm_pourcentage}}%</div>
                                    <a href="{% url 'ecm' %}" class="text-decoration-none">
                                       
                                    </a>
                                    <div class="card-footer d-flex align-items-center justify-content-between {% if results.esm_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %} text-white">
                                        <!-- Bouton pour afficher/masquer les détails -->
                                        <a href="#" class="text-decoration-none text-white" data-bs-toggle="collapse" data-bs-target="#details-esm-1">
                                            <span class="small">Voir Détails</span>
                                            <i class="fas fa-angle-right"></i> 
                                        </a>
                                    </div>
                                </div>
                            </div>
                        
                           <!-- Conteneur principal -->
                           <div class="collapse mt-2" id="details-esm-1">
                            <div class="card card-body small-font">
                                <!-- Boucle pour générer dynamiquement les liens -->
                                {% for esm in results.resultats_esm %}
                                    <a href="{% url 'esmbyid' id=esm.id %}" class="text-decoration-none 
                                        {% if esm.status|lower == 'en panne' or esm.status|lower == 'panne' %}
                                            text-danger
                                        {% else %}
                                            text-dark
                                        {% endif %}">
                                        <p>{{ esm.nom }} : {{ esm.status }}</p>
                                    </a>
                                {% empty %}
                                    <!-- Message si la liste est vide -->
                                    <p class="text-muted">Aucun résultat disponible.</p>
                                {% endfor %}
                            </div>
                          </div>
                        </div>
        
                        <!-- Deuxième carte -->
                        <div class="col-md-4 mb-4">
                            <div>                  
                                <div class="card  text-white {% if results.ecm_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                                    <div class="card-body small-font">ECM {{ecm_pourcentage}}%</div>
                                    <a href="{% url 'ecm' %}" class="text-decoration-none">
                                        
                                    </a>
                                    <div class="card-footer d-flex align-items-center justify-content-between {% if results.ecm_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %} text-white">
                                        <!-- Bouton pour afficher/masquer les détails -->
                                        <a href="#" class="text-decoration-none text-white" data-bs-toggle="collapse" data-bs-target="#details-ecm">
                                            <span class="small">Voir Détails</span>
                                            <i class="fas fa-angle-right"></i> 
                                        </a>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Contenu à afficher/masquer -->
                            <div class="collapse mt-2" id="details-ecm">
                                <div class="card card-body small-font">
                                    <!-- Boucle pour générer dynamiquement les liens -->
                                    {% for ecm in results.resultats_ecm %}
                                        <a href="{% url 'ecm' %}" class="text-decoration-none 
                                            {% if ecm.status|lower == 'en panne' or ecm.status|lower == 'panne' %}
                                                text-danger
                                            {% else %}
                                                text-success
                                            {% endif %}">
                                            <p>{{ ecm.nom }} : {{ ecm.status }}</p>
                                        </a>
                                    {% empty %}
                                        <!-- Message si la liste est vide -->
                                        <p class="text-muted">Aucun résultat disponible.</p>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
        
                        <!-- Troisième carte -->
                        <div class="col-md-4 mb-4">
                            <div>                  
                                <div class="card  text-white {% if results.cc_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                                    <div class="card-body small-font">CC {{cc_pourcentage}}%</div>
                                    <a href="{% url 'ecm' %}" class="text-decoration-none">
                                      
                                    </a>
                                    <div class="card-footer d-flex align-items-center justify-content-between {% if results.cc_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %} text-white">
                                        <!-- Bouton pour afficher/masquer les détails -->
                                        <a href="#" class="text-decoration-none text-white" data-bs-toggle="collapse" data-bs-target="#details-cc">
                                            <span class="small">Voir Détails</span>
                                            <i class="fas fa-angle-right"></i> 
                                        </a>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Contenu à afficher/masquer -->
                        
                           <div class="collapse mt-2" id="details-cc">
                          
                                <div class="card card-body small-font"> 
                                <!-- Boucle pour générer dynamiquement les liens -->
                                {% for cc in results.resultats_cc %}
                                    <a href="{% url 'ecm' %}" class="text-decoration-none 
                                        {% if cc.status|lower == 'en panne' or cc.status|lower == 'panne' %}
                                            text-danger
                                        {% else %}
                                            text-success
                                        {% endif %}">
                                        <p>{{ cc.nom }} : {{ cc.status }}</p>
                                    </a>
                                {% empty %}
                                    <!-- Message si la liste est vide -->
                                    <p class="text-muted">Aucun résultat disponible.</p>
                                {% endfor %}
                            </div>
                          </div>
                        </div>
        
                        <!-- Quatrième carte -->
                        <div class="col-md-4 mb-4">
                            <div>                  
                                <div class="card  text-white {% if results.bcc_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                                    <div class="card-body small-font">BCC {{bcc_pourcentage}}%</div>
                                    <a href="{% url 'ecm' %}" class="text-decoration-none">
                                        
                                    </a>
                                    <div class="card-footer d-flex align-items-center justify-content-between {% if results.bcc_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %} text-white">
                                        <!-- Bouton pour afficher/masquer les détails -->
                                        <a href="#" class="text-decoration-none text-white" data-bs-toggle="collapse" data-bs-target="#details-bcc">
                                            <span class="small">Voir Détails</span>
                                            <i class="fas fa-angle-right"></i> 
                                        </a>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Contenu à afficher/masquer -->
                            <div class="collapse mt-2" id="details-bcc">
                                <div class="card card-body small-font">
                                    <!-- Boucle pour générer dynamiquement les liens -->
                                    {% for bcc in results.resultats_bcc %}
                                        <a href="{% url 'ecm' %}" class="text-decoration-none 
                                            {% if bcc.status|lower == 'en panne' or bcc.status|lower == 'panne' %}
                                                text-danger
                                            {% else %}
                                                text-success
                                            {% endif %}">
                                            <p>{{ bcc.nom }} : {{ bcc.status }}</p>
                                        </a>
                                    {% empty %}
                                        <!-- Message si la liste est vide -->
                                        <p class="text-muted">Aucun résultat disponible.</p>
                                    {% endfor %}
                                </div>
                              </div>
                        </div>
        
                        <!-- Cinquième carte -->
                        <div class="col-md-4 mb-4">
                            <div>                  
                                <div class="card  text-white {% if results.maintenance_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                                    <div class="card-body small-font">MAINT {{maintenance_pourcentage}}%</div>
                                    <a href="{% url 'ecm' %}" class="text-decoration-none">
                                      
                                    </a>
                                    <div class="card-footer d-flex align-items-center justify-content-between {% if results.maintenance_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %} text-white">
                                        <!-- Bouton pour afficher/masquer les détails -->
                                        <a href="#" class="text-decoration-none text-white" data-bs-toggle="collapse" data-bs-target="#details-maintenance">
                                            <span class="small">Voir Détails</span>
                                            <i class="fas fa-angle-right"></i> 
                                        </a>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Contenu à afficher/masquer -->
                            <div class="collapse mt-2" id="details-maintenance">
                                <div class="card card-body small-font">
                                    <!-- Boucle pour générer dynamiquement les liens -->
                                    {% for maintenance in results.resultats_maintenance %}
                                        <a href="{% url 'ecm' %}" class="text-decoration-none 
                                            {% if maintenance.status|lower == 'en panne' or maintenance.status|lower == 'panne' %}
                                                text-danger
                                            {% else %}
                                                text-success
                                            {% endif %}">
                                            <p>{{ maintenance.nom }} : {{ maintenance.status }}</p>
                                        </a>
                                    {% empty %}
                                        <!-- Message si la liste est vide -->
                                        <p class="text-muted">Aucun résultat disponible.</p>
                                    {% endfor %}
                                </div>
                              </div>
                        </div>
        
                        <!-- Sixième carte -->
                        <div class="col-md-4 mb-4">
                            <div>                  
                                <div class="card  text-white {% if results.simulateur_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                                    <div class="card-body small-font">SIM {{simulateur_pourcentage}}%</div>
                                    <a href="{% url 'ecm' %}" class="text-decoration-none">
                                       
                                    </a>
                                    <div class="card-footer d-flex align-items-center justify-content-between {% if results.simulateur_statut == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %} text-white">
                                        <!-- Bouton pour afficher/masquer les détails -->
                                        <a href="#" class="text-decoration-none text-white" data-bs-toggle="collapse" data-bs-target="#details-simulateur">
                                            <span class="small">Voir Détails</span>
                                            <i class="fas fa-angle-right"></i> 
                                        </a>
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Contenu à afficher/masquer -->
                            <div class="collapse mt-2" id="details-simulateur">
                                <div class="card card-body small-font">
                                    <!-- Boucle pour générer dynamiquement les liens -->
                                    {% for simulateur in results.resultats_simulateur %}
                                        <a href="{% url 'ecm' %}" class="text-decoration-none 
                                            {% if simulateur.status|lower == 'en panne' or simulateur.status|lower == 'panne' %}
                                                text-danger
                                            {% else %}
                                                text-success
                                            {% endif %}">
                                            <p>{{ simulateur.nom }} : {{ simulateur.status }}</p>
                                        </a>
                                    {% empty %}
                                        <!-- Message si la liste est vide -->
                                        <p class="text-muted">Aucun résultat disponible.</p>
                                    {% endfor %}
                                </div>
                              </div>
                        </div>
        
                    </div>
                </div>
            </div>


            <!-- Section Stock améliorée -->
            <div class="stock-table mb-4">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-boxes me-2"></i>
                            <h5 class="mb-0 fw-bold">Gestion des Stocks</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-light text-primary me-2">
                                <i class="fas fa-sync-alt me-1"></i>
                                Temps réel
                            </span>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-light btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-download me-1"></i>
                                    Exporter
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportTableToCSV('stock-data.csv')">
                                        <i class="fas fa-file-csv me-2"></i>Exporter en CSV
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportTableToExcel('stock-data.xlsx')">
                                        <i class="fas fa-file-excel me-2"></i>Exporter en Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="printTable()">
                                        <i class="fas fa-print me-2"></i>Imprimer
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table id="datatablesSimple" class="table mb-0" data-table-name="Gestion des Stocks"">
                            <thead>
                                <tr>
                                    <th>
                                        <i class="fas fa-cog me-1"></i>
                                        Équipement
                                    </th>
                                    <th>
                                        <i class="fas fa-barcode me-1"></i>
                                        N° de produit
                                    </th>
                                    <th>
                                        <i class="fas fa-industry me-1"></i>
                                        Marque
                                    </th>
                                    <th>
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Seuil critique
                                    </th>
                                    <th>
                                        <i class="fas fa-cubes me-1"></i>
                                        Stock actuel
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                {% for stock in stock %}
                                    <tr>
                                        <td class="fw-medium">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    {% if stock.quantite < stock.quantite_critique %}
                                                        <i class="fas fa-exclamation-circle text-danger"></i>
                                                    {% elif stock.quantite == stock.quantite_critique %}
                                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                                    {% else %}
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    {% endif %}
                                                </div>
                                                {{ stock.equipement.nom }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="product-code">{{ stock.equipement.product_numbre }}</span>
                                        </td>
                                        <td>
                                            <span class="brand-badge">{{ stock.equipement.constructeur }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="fw-bold text-warning">{{ stock.quantite_critique }}</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <span class="fw-bold fs-5 me-2
                                                    {% if stock.quantite < stock.quantite_critique %}
                                                        text-danger
                                                    {% elif stock.quantite == stock.quantite_critique %}
                                                        text-warning
                                                    {% else %}
                                                        text-success
                                                    {% endif %}">
                                                    {{ stock.quantite }}
                                                </span>
                                                {% if stock.quantite < stock.quantite_critique %}
                                                    <span class="stock-badge critical">Critique</span>
                                                {% elif stock.quantite == stock.quantite_critique %}
                                                    <span class="stock-badge warning">Attention</span>
                                                {% else %}
                                                    <span class="stock-badge normal">Normal</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-6" style="margin-top: 20px; margin-left: 20px;">
            <div id="calendrier-container">
               
                <!-- Conteneur principal avec Bootstrap -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-calendar me-1"></i>
                        Calendrier
                    </div>
                    <div class="card-body">
                        <div id="calendar"></div>
                    </div>
                </div>
                <!-- Modal pour ajouter un événement -->
                <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="eventModalLabel">Ajouter un Événement</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="eventForm">
                                    <div class="mb-3">
                                        <label for="eventTitle" class="form-label">Titre</label>
                                        <input type="text" class="form-control" id="eventTitle" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="eventDescription" class="form-label">Description</label>
                                        <textarea class="form-control" id="eventDescription"></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" id="saveEvent">Enregistrer</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Modal pour afficher les détails d'un événement -->
                <div class="modal fade" id="eventDetailsModal" tabindex="-1" aria-labelledby="eventDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="eventDetailsModalLabel">Détails de l'Événement</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="eventDetailsModalBody">
                                <!-- Contenu des détails sera inséré ici -->
                            </div>
                            <div class="modal-footer">
                                <a id="predictiveMaintenanceBtn" href="#" class="btn btn-success">Maintenance</a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                <!-- Nouveau bouton pour Maintenance Prédictive -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
 


        
        
    </div>
</div>

 
<script>
 
 
    document.addEventListener('DOMContentLoaded', function () {
        var calendarEl = document.getElementById('calendar');
        // Initialisation du calendrier
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            allDaySlot: true,
            allDayText:'Jour',
            height: 600,
            locale: 'fr', // Configuration de la langue française
            buttonText: {
                today: 'Aujourd\'hui',
                month: 'Mois',
                week: 'Semaine',
                day: 'Jour',
               
                list: 'Liste'
            },
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            columnHeaderFormat: { day: 'numeric', month: '2-digit' }, // Format JJ/MM
            
            selectable: true,
            dayMaxEvents: true, // Afficher "+X événements" si le nombre dépasse la limite
            events: JSON.parse('{{ events | safe }}'), // Injecter les événements depuis Django
            eventClick: function (info) {
                // Récupérer les détails de l'événement cliqué
                var event = info.event;
                var modalBody = document.getElementById('eventDetailsModalBody');
                modalBody.innerHTML = ''; // Réinitialiser le contenu
            
                // Créer le contenu du modal
                var titleElement = document.createElement('p');
                titleElement.innerHTML = `<strong>Instance :</strong> ${event.title}`;
                modalBody.appendChild(titleElement);
            
                if (event.extendedProps && event.extendedProps.description) {
                    var descriptionElement = document.createElement('p');
                    descriptionElement.innerHTML = `<strong>Désignation :</strong> ${event.extendedProps.description}`;
                    modalBody.appendChild(descriptionElement);
                }
                
                var startDateElement = document.createElement('p');
                startDateElement.innerHTML = `<strong>Date:</strong> ${event.start ? event.start.toLocaleDateString() : 'Non spécifiée'}`;
                modalBody.appendChild(startDateElement);
                const startDate = new Date(event.start); // Convertir en objet Date si ce n'est pas déjà fait
                const endDate = new Date(startDate.getTime() + 120 * 60 * 1000);
                if (event.end) {
                    var endDateElement = document.createElement('p');
                    endDateElement.innerHTML = `<strong>Date de fin :</strong> ${endDate.toLocaleString()}`;
                    modalBody.appendChild(endDateElement);
                }   
            
                // Mettre à jour l'URL du bouton "Maintenance Prédictive"
                var predictiveMaintenanceBtn = document.getElementById('predictiveMaintenanceBtn');
                if (event.extendedProps && event.extendedProps.id_instance) {
                    predictiveMaintenanceBtn.href = `/pages/preventivebyId/${event.extendedProps.id_instance}/${event.extendedProps.serial_id }`;
                    predictiveMaintenanceBtn.style.display = 'inline-block'; // Assurez-vous que le bouton est visible
                } else {
                    predictiveMaintenanceBtn.style.display = 'none'; // Masquer le bouton si pas d'ID
                }
            
                // Afficher le modal sans backdrop
                var detailsModal = new bootstrap.Modal(document.getElementById('eventDetailsModal'), {
                    backdrop: false // Désactiver le backdrop
                });
                detailsModal.show();
            },
            eventDidMount: function(info) {
    const eventDate = new Date(info.event.start);
    const today = new Date();

    // Supprimer l’heure pour comparaison juste sur la date
    eventDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const el = info.el;

    if (eventDate < today) {
        // Date dépassée -> rouge
        el.classList.add('bg-danger', 'text-white');
    } else {
        // Date future -> orange
        el.classList.add('bg-warning', 'text-dark');
    }
}
        });
        calendar.render();
    });

    // Fonctions d'export pour le tableau de stock
    function exportTableToCSV(filename) {
        const table = document.getElementById('datatablesSimple');
        const rows = table.querySelectorAll('tr');
        let csvContent = '';

        // En-têtes
        const headers = table.querySelectorAll('thead th');
        const headerRow = Array.from(headers).map(th => {
            return '"' + th.textContent.trim().replace(/"/g, '""') + '"';
        }).join(',');
        csvContent += headerRow + '\n';

        // Données
        const dataRows = table.querySelectorAll('tbody tr');
        dataRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const rowData = Array.from(cells).map(td => {
                // Nettoyer le texte (enlever les icônes et espaces supplémentaires)
                let text = td.textContent.trim();
                // Enlever les badges et garder seulement le texte principal
                if (td.querySelector('.stock-badge')) {
                    const mainText = td.childNodes[0].textContent.trim();
                    text = mainText;
                }
                return '"' + text.replace(/"/g, '""') + '"';
            }).join(',');
            csvContent += rowData + '\n';
        });

        // Télécharger le fichier
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Notification de succès
        showNotification('Export CSV réussi !', 'success');
    }

    function exportTableToExcel(filename) {
        const table = document.getElementById('datatablesSimple');
        const workbook = XLSX.utils.table_to_book(table, {sheet: "Stock"});
        XLSX.writeFile(workbook, filename);

        // Notification de succès
        showNotification('Export Excel réussi !', 'success');
    }

    function printTable() {
        const table = document.getElementById('datatablesSimple');
        const tableName = table.getAttribute('data-table-name') || 'Tableau';

        // Créer une nouvelle fenêtre pour l'impression
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>${tableName}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { color: #2563eb; text-align: center; margin-bottom: 30px; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
                        th { background-color: #f8fafc; font-weight: bold; }
                        tr:nth-child(even) { background-color: #f9f9f9; }
                        .critical { background-color: #fecaca !important; color: #991b1b; }
                        .warning { background-color: #fef3c7 !important; color: #92400e; }
                        .normal { background-color: #dcfce7 !important; color: #166534; }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <h1>${tableName}</h1>
                    <p><strong>Date d'export :</strong> ${new Date().toLocaleString('fr-FR')}</p>
                    ${table.outerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();

        // Notification de succès
        showNotification('Impression lancée !', 'info');
    }

    function showNotification(message, type) {
        // Créer une notification toast
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // Auto-supprimer après 3 secondes
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
</script>

<!-- Script pour Excel export (optionnel - nécessite la librairie XLSX) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

{% endblock %}
