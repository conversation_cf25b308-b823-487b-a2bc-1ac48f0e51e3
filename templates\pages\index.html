{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
    /* Variables CSS pour une cohérence visuelle */
    :root {
        --primary-color: #2563eb;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --secondary-color: #6b7280;
        --light-bg: #f8fafc;
        --white: #ffffff;
        --border-radius: 8px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    body {
        background-color: var(--light-bg);
        font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
        color: #1f2937;
        line-height: 1.6;
    }

    /* Header professionnel */
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        box-shadow: var(--shadow-lg);
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        font-weight: 400;
    }

    /* Amélioration des cartes de statut */
    .status-card {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        overflow: hidden;
        background: var(--white);
    }

    .status-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .status-card-header {
        padding: 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-indicator.success { background-color: var(--success-color); }
    .status-indicator.danger { background-color: var(--danger-color); }
    .status-indicator.warning { background-color: var(--warning-color); }

    .small-font {
        font-size: 0.9rem;
        font-weight: 600;
    }
    /* Amélioration du calendrier */
    .calendar-container {
        background: var(--white);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-md);
        overflow: hidden;
    }

    #calendar {
        max-width: 100%;
        height: 600px;
        border: none !important;
        font-family: inherit;
    }

    .fc-toolbar {
        padding: 1rem;
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 0 !important;
    }

    .fc-toolbar-title {
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        color: #1f2937;
    }

    .fc-button {
        background-color: var(--primary-color) !important;
        border: none !important;
        color: white !important;
        font-size: 0.875rem !important;
        padding: 0.5rem 1rem !important;
        border-radius: var(--border-radius) !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
    }

    .fc-button:hover {
        background-color: #1d4ed8 !important;
        transform: translateY(-1px);
    }

    .fc-today-button {
        background-color: var(--success-color) !important;
    }

    .fc-today-button:hover {
        background-color: #047857 !important;
    }

    .fc-daygrid-day {
        border: 1px solid #f3f4f6 !important;
        transition: background-color 0.2s ease;
    }

    .fc-daygrid-day:hover {
        background-color: #f9fafb !important;
    }

    .fc-event {
        border: none !important;
        border-radius: var(--border-radius) !important;
        font-size: 0.875rem !important;
        font-weight: 500 !important;
        padding: 0.25rem 0.5rem !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }

    .fc-event:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-md);
    }

    /* Amélioration du tableau de stock */
    .stock-table {
        background: var(--white);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-md);
    }

    .table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .table thead th {
        background: #f9fafb;
        border-bottom: 2px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 1rem 0.75rem;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
    }

    .table tbody td {
        padding: 0.875rem 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        vertical-align: middle;
    }

    .warning-cell {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
        color: #92400e !important;
        font-weight: 600;
        position: relative;
    }

    .warning-cell::before {
        content: '⚠️';
        margin-right: 0.5rem;
    }

    .danger-cell {
        background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%) !important;
        color: #991b1b !important;
        font-weight: 600;
        position: relative;
    }

    .danger-cell::before {
        content: '🚨';
        margin-right: 0.5rem;
    }

    /* Amélioration des modals */
    .modal-content {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
    }

    .modal-header {
        background: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        padding: 1.5rem;
    }

    .modal-title {
        font-weight: 600;
        color: #1f2937;
    }

    .modal-body {
        padding: 1.5rem;
    }

    #eventDetailsModalBody p {
        margin-bottom: 1rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f3f4f6;
    }

    #eventDetailsModalBody p:last-child {
        border-bottom: none;
    }

    #eventDetailsModalBody strong {
        color: var(--primary-color);
        font-weight: 600;
    }
</style>

<!-- Header professionnel -->
<div class="dashboard-header">
    <div class="container-fluid px-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="dashboard-title">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    Tableau de Bord EWMS
                </h1>
                <p class="dashboard-subtitle">
                    Surveillance et gestion des équipements en temps réel
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex align-items-center justify-content-end">
                    <div class="me-3">
                        <small class="text-white-50">Dernière mise à jour</small>
                        <div class="fw-bold" id="lastUpdate"></div>
                    </div>
                    <div class="status-indicator success"></div>
                    <small>Système opérationnel</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid px-4">
    <!-- Breadcrumb amélioré -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb bg-white rounded shadow-sm px-3 py-2">
            <li class="breadcrumb-item">
                <a href="{% url 'index' %}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Accueil
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">Tableau de bord</li>
        </ol>
    </nav>
    <div class="row g-4">
        <!-- Section des cartes de statut -->
        <div class="col-xl-6">
            <div class="status-card mb-4">
                <div class="status-card-header">
                    <i class="fas fa-shield-alt me-2"></i>
                    <span class="fw-bold">État des Systèmes GE-R</span>
                    <span class="badge bg-primary ms-2">Temps réel</span>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <!-- Carte ESM -->
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold">ESM</h6>
                                        <span class="status-indicator {% if results.esm_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.esm_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{esm_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-esm-1">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails ESM -->
                            <div class="collapse mt-3" id="details-esm-1">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails ESM</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for esm in results.resultats_esm %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if esm.status|lower == 'en panne' or esm.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ esm.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if esm.status|lower == 'en panne' or esm.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ esm.status }}
                                                    </span>
                                                    <a href="{% url 'esmbyid' id=esm.id %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte ECM -->
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold">ECM</h6>
                                        <span class="status-indicator {% if results.ecm_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.ecm_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{ecm_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-ecm">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails ECM -->
                            <div class="collapse mt-3" id="details-ecm">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails ECM</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for ecm in results.resultats_ecm %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if ecm.status|lower == 'en panne' or ecm.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ ecm.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if ecm.status|lower == 'en panne' or ecm.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ ecm.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte CC -->
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold">CC</h6>
                                        <span class="status-indicator {% if results.cc_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.cc_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{cc_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-cc">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails CC -->
                            <div class="collapse mt-3" id="details-cc">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails CC</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for cc in results.resultats_cc %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if cc.status|lower == 'en panne' or cc.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ cc.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if cc.status|lower == 'en panne' or cc.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ cc.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte BCC -->
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold">BCC</h6>
                                        <span class="status-indicator {% if results.bcc_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.bcc_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{bcc_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-bcc">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails BCC -->
                            <div class="collapse mt-3" id="details-bcc">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails BCC</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for bcc in results.resultats_bcc %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if bcc.status|lower == 'en panne' or bcc.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ bcc.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if bcc.status|lower == 'en panne' or bcc.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ bcc.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte MAINT -->
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold">MAINT</h6>
                                        <span class="status-indicator {% if results.maintenance_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.maintenance_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{maintenance_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-maintenance">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails MAINT -->
                            <div class="collapse mt-3" id="details-maintenance">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails Maintenance</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for maintenance in results.resultats_maintenance %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if maintenance.status|lower == 'en panne' or maintenance.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ maintenance.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if maintenance.status|lower == 'en panne' or maintenance.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ maintenance.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte SIM -->
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold">SIM</h6>
                                        <span class="status-indicator {% if results.simulateur_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.simulateur_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{simulateur_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-simulateur">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails SIM -->
                            <div class="collapse mt-3" id="details-simulateur">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails Simulateur</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for simulateur in results.resultats_simulateur %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if simulateur.status|lower == 'en panne' or simulateur.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ simulateur.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if simulateur.status|lower == 'en panne' or simulateur.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ simulateur.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
        
                    </div>
                </div>
            </div>


            <!-- Section Stock améliorée -->
            <div class="stock-table mb-4">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-boxes me-2 text-primary"></i>
                            <h5 class="mb-0 fw-bold">Gestion des Stocks</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">
                                <i class="fas fa-sync-alt me-1"></i>
                                Temps réel
                            </span>
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-download me-1"></i>
                                Exporter
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table id="datatablesSimple" class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="border-0">
                                        <i class="fas fa-cog me-1"></i>
                                        Équipement
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-barcode me-1"></i>
                                        N° de produit
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-industry me-1"></i>
                                        Marque
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Seuil critique
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-cubes me-1"></i>
                                        Stock actuel
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                {% for stock in stock %}
                                    <tr class="{% if stock.quantite < stock.quantite_critique %}table-danger{% elif stock.quantite == stock.quantite_critique %}table-warning{% endif %}">
                                        <td class="fw-medium">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    {% if stock.quantite < stock.quantite_critique %}
                                                        <i class="fas fa-exclamation-circle text-danger"></i>
                                                    {% elif stock.quantite == stock.quantite_critique %}
                                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                                    {% else %}
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    {% endif %}
                                                </div>
                                                {{ stock.equipement.nom }}
                                            </div>
                                        </td>
                                        <td>
                                            <code class="bg-light px-2 py-1 rounded">{{ stock.equipement.product_numbre }}</code>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ stock.equipement.constructeur }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="fw-bold text-warning">{{ stock.quantite_critique }}</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <span class="fw-bold fs-5
                                                    {% if stock.quantite < stock.quantite_critique %}
                                                        text-danger
                                                    {% elif stock.quantite == stock.quantite_critique %}
                                                        text-warning
                                                    {% else %}
                                                        text-success
                                                    {% endif %}">
                                                    {{ stock.quantite }}
                                                </span>
                                                {% if stock.quantite < stock.quantite_critique %}
                                                    <span class="badge bg-danger ms-2">Critique</span>
                                                {% elif stock.quantite == stock.quantite_critique %}
                                                    <span class="badge bg-warning ms-2">Attention</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Calendrier -->
        <div class="col-xl-6">
            <div class="calendar-container mb-4">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-calendar-alt me-2 text-primary"></i>
                            <h5 class="mb-0 fw-bold">Planification des Maintenances</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">
                                <i class="fas fa-sync-alt me-1"></i>
                                Synchronisé
                            </span>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-plus me-1"></i>
                                    Ajouter
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-filter me-1"></i>
                                    Filtrer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="calendar"></div>
                </div>
            </div>
                <!-- Modal pour ajouter un événement -->
                <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="eventModalLabel">Ajouter un Événement</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="eventForm">
                                    <div class="mb-3">
                                        <label for="eventTitle" class="form-label">Titre</label>
                                        <input type="text" class="form-control" id="eventTitle" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="eventDescription" class="form-label">Description</label>
                                        <textarea class="form-control" id="eventDescription"></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" id="saveEvent">Enregistrer</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Modal pour afficher les détails d'un événement -->
                <div class="modal fade" id="eventDetailsModal" tabindex="-1" aria-labelledby="eventDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="eventDetailsModalLabel">Détails de l'Événement</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="eventDetailsModalBody">
                                <!-- Contenu des détails sera inséré ici -->
                            </div>
                            <div class="modal-footer">
                                <a id="predictiveMaintenanceBtn" href="#" class="btn btn-success">Maintenance</a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                <!-- Nouveau bouton pour Maintenance Prédictive -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
 


        
        
    </div>
</div>

<script>
    // Fonction pour mettre à jour l'heure de dernière mise à jour
    function updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        const lastUpdateElement = document.getElementById('lastUpdate');
        if (lastUpdateElement) {
            lastUpdateElement.textContent = timeString;
        }
    }

    // Fonction pour animer les cartes au chargement
    function animateCards() {
        const cards = document.querySelectorAll('.status-card, .stock-table, .calendar-container');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Initialiser l'heure de dernière mise à jour
        updateLastUpdateTime();
        setInterval(updateLastUpdateTime, 1000);

        // Animer les cartes
        animateCards();
        var calendarEl = document.getElementById('calendar');
        // Initialisation du calendrier
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            allDaySlot: true,
            allDayText:'Jour',
            height: 600,
            locale: 'fr', // Configuration de la langue française
            buttonText: {
                today: 'Aujourd\'hui',
                month: 'Mois',
                week: 'Semaine',
                day: 'Jour',
               
                list: 'Liste'
            },
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            columnHeaderFormat: { day: 'numeric', month: '2-digit' }, // Format JJ/MM
            
            selectable: true,
            dayMaxEvents: true, // Afficher "+X événements" si le nombre dépasse la limite
            events: JSON.parse('{{ events | safe }}'), // Injecter les événements depuis Django
            eventClick: function (info) {
                // Récupérer les détails de l'événement cliqué
                var event = info.event;
                var modalBody = document.getElementById('eventDetailsModalBody');
                modalBody.innerHTML = ''; // Réinitialiser le contenu
            
                // Créer le contenu du modal
                var titleElement = document.createElement('p');
                titleElement.innerHTML = `<strong>Instance :</strong> ${event.title}`;
                modalBody.appendChild(titleElement);
            
                if (event.extendedProps && event.extendedProps.description) {
                    var descriptionElement = document.createElement('p');
                    descriptionElement.innerHTML = `<strong>Désignation :</strong> ${event.extendedProps.description}`;
                    modalBody.appendChild(descriptionElement);
                }
                
                var startDateElement = document.createElement('p');
                startDateElement.innerHTML = `<strong>Date:</strong> ${event.start ? event.start.toLocaleDateString() : 'Non spécifiée'}`;
                modalBody.appendChild(startDateElement);
                const startDate = new Date(event.start); // Convertir en objet Date si ce n'est pas déjà fait
                const endDate = new Date(startDate.getTime() + 120 * 60 * 1000);
                if (event.end) {
                    var endDateElement = document.createElement('p');
                    endDateElement.innerHTML = `<strong>Date de fin :</strong> ${endDate.toLocaleString()}`;
                    modalBody.appendChild(endDateElement);
                }   
            
                // Mettre à jour l'URL du bouton "Maintenance Prédictive"
                var predictiveMaintenanceBtn = document.getElementById('predictiveMaintenanceBtn');
                if (event.extendedProps && event.extendedProps.id_instance) {
                    predictiveMaintenanceBtn.href = `/pages/preventivebyId/${event.extendedProps.id_instance}/${event.extendedProps.serial_id }`;
                    predictiveMaintenanceBtn.style.display = 'inline-block'; // Assurez-vous que le bouton est visible
                } else {
                    predictiveMaintenanceBtn.style.display = 'none'; // Masquer le bouton si pas d'ID
                }
            
                // Afficher le modal sans backdrop
                var detailsModal = new bootstrap.Modal(document.getElementById('eventDetailsModal'), {
                    backdrop: false // Désactiver le backdrop
                });
                detailsModal.show();
            },
            eventDidMount: function(info) {
    const eventDate = new Date(info.event.start);
    const today = new Date();

    // Supprimer l’heure pour comparaison juste sur la date
    eventDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const el = info.el;

    if (eventDate < today) {
        // Date dépassée -> rouge
        el.classList.add('bg-danger', 'text-white');
    } else {
        // Date future -> orange
        el.classList.add('bg-warning', 'text-dark');
    }
}
        });
        calendar.render();
    });
</script> 

{% endblock %}
