{% extends 'base.html' %}
{% load static %}

{% block content %}
<style>
    /* Variables CSS pour une cohérence visuelle */
    :root {
        --primary-color: #2563eb;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --secondary-color: #6b7280;
        --light-bg: #f8fafc;
        --white: #ffffff;
        --border-radius: 8px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .fc-toolbar-title {
        font-size: 20px !important;
    }
    .fc-col-header-cell-cushion{
        font-size: 10px !important;
    }
    body {
        background-color: var(--light-bg);
        font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
        color: #1f2937;
        line-height: 1.6;
    }

    /* Amélioration des cartes de statut */
    .status-card-modern {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        overflow: hidden;
        background: var(--white);
        height: 100%;
    }

    .status-card-modern:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-indicator.success { background-color: var(--success-color); }
    .status-indicator.danger { background-color: var(--danger-color); }
    .status-indicator.warning { background-color: var(--warning-color); }

    .calendar-container {
        padding: 20px;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .small-font {
        font-size: 0.8rem;
        font-weight: bold;
    }
    h1 {
        font-size: 2rem;
        color: #343a40;
        font-weight: 600;
        margin-bottom: 20px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    /* Style du calendrier */
    #calendar {
        max-width: 100%;
        height: 600px;
        border: none !important; /* Supprimer la bordure par défaut */
    }
    /* Style des jours du calendrier */
    .fc-daygrid-day {
        border: 1px solid #e9ecef !important; /* Bordure légère pour chaque jour */
        border-radius: 6px; /* Coins arrondis pour les cellules */
    }
    /* Style des événements */
    .fc-event {
        background-color: #0d6efd; /* Bleu Bootstrap pour les événements */
        border: none !important; /* Supprimer la bordure des événements */
        color: white; /* Texte en blanc */
        border-radius: 6px; /* Coins arrondis */
        font-size: 0.9rem; /* Taille de police ajustée */
        padding: 5px 10px; /* Espacement interne */
        cursor: pointer; /* Curseur en forme de main */
    }
    /* Style des boutons de navigation */
    .fc-button {
        background-color: #0d6efd; /* Bleu Bootstrap */
        border: none !important; /* Supprimer la bordure */
        color: white; /* Texte en blanc */
        font-size: 0.9rem; /* Taille de police ajustée */
        padding: 8px 16px; /* Espacement interne */
        border-radius: 6px; /* Coins arrondis */
        transition: background-color 0.3s ease; /* Effet de transition */
    }
    .fc-button:hover {
        background-color: #0b5ed7; /* Bleu plus foncé au survol */
    }
    /* Style du bouton "Today" */
    .fc-today-button {
        background-color: #198754; /* Vert Bootstrap */
    }
    .fc-today-button:hover {
        background-color: #157347; /* Vert plus foncé au survol */
    }
    /* Style de la barre d'outils */
    .fc-toolbar {
        margin-bottom: 20px; /* Espacement sous la barre d'outils */
    }
    /* Style du lien "+X événements" */
    .fc-more-link {
        display: block !important; /* Pour rendre le lien stylable comme un bloc */
        width: 100% !important; /* Prendre toute la largeur de la cellule */
        text-align: center; /* Centrer le texte horizontalement */
        background-color: #f0c817; /* Même couleur que les événements */
        border: none !important; /* Supprimer la bordure */
        color: white !important; /* Texte en blanc */
        border-radius: 6px; /* Coins arrondis */
        font-size: 0.9rem; /* Taille de police ajustée */
        padding: 5px 10px; /* Espacement interne */
        text-decoration: none !important; /* Supprimer le soulignement */
        cursor: pointer; /* Curseur en forme de main */
        box-sizing: border-box; /* Inclure le padding dans la largeur totale */
    }
    .fc-more-link:hover {
        background-color: #0b5ed7; /* Bleu plus foncé au survol */
    }
    /* Style du modal pour les détails d'événement */
    #eventDetailsModalBody {
        font-size: 0.9rem; /* Taille de police ajustée */
        line-height: 1.6; /* Espacement entre les lignes */
    }
    #eventDetailsModalBody p {
        margin-bottom: 10px; /* Espacement entre les paragraphes */
    }
    #eventDetailsModalBody strong {
        color: #0d6efd; /* Bleu Bootstrap pour les titres */
    }
    /* Style des séparateurs (hr) dans le modal */
    #eventDetailsModalBody hr {
        border: none; /* Supprimer la bordure par défaut */
        border-top: 1px solid #e9ecef; /* Ligne légère */
        margin: 15px 0; /* Espacement autour de la ligne */
    }

    .warning-cell {
        background-color: #fff3cd !important; /* Jaune pour warning */
        color: black;
    }
    .danger-cell {
        background-color: #f8d7da !important; /* Rouge pour danger */
        color: black;
    }
</style>
<div class="container-fluid px-4 center" style="margin-top:20px;">
     <ol class="breadcrumb mb-4" style="margin-top: 10px;">
        <li class="breadcrumb-item"><a href="{% url 'index' %}">Home</a></li>
       
    </ol>
    <div class="row">
        <div class="col-xl-5" style="margin-top: 20px; margin-left: 10px;">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-shield-alt me-1"></i>
                    GE-R
                </div>
                <div class="card-body center">
                    <div class="row g-3">
                        <!-- Carte ESM -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="status-card-modern">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold text-dark">ESM</h6>
                                        <span class="status-indicator {% if results.esm_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.esm_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{esm_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-esm-1">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails ESM - Conservation de la structure originale -->
                            <div class="collapse mt-3" id="details-esm-1">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails ESM</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for esm in results.resultats_esm %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if esm.status|lower == 'en panne' or esm.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ esm.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if esm.status|lower == 'en panne' or esm.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ esm.status }}
                                                    </span>
                                                    <a href="{% url 'esmbyid' id=esm.id %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte ECM -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="status-card-modern">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold text-dark">ECM</h6>
                                        <span class="status-indicator {% if results.ecm_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.ecm_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{ecm_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-ecm">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails ECM - Conservation de la structure originale -->
                            <div class="collapse mt-3" id="details-ecm">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails ECM</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for ecm in results.resultats_ecm %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if ecm.status|lower == 'en panne' or ecm.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ ecm.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if ecm.status|lower == 'en panne' or ecm.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ ecm.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte CC -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="status-card-modern">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold text-dark">CC</h6>
                                        <span class="status-indicator {% if results.cc_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.cc_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{cc_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-cc">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails CC - Conservation de la structure originale -->
                            <div class="collapse mt-3" id="details-cc">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails CC</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for cc in results.resultats_cc %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if cc.status|lower == 'en panne' or cc.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ cc.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if cc.status|lower == 'en panne' or cc.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ cc.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte BCC -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="status-card-modern">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold text-dark">BCC</h6>
                                        <span class="status-indicator {% if results.bcc_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.bcc_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{bcc_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-bcc">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails BCC - Conservation de la structure originale -->
                            <div class="collapse mt-3" id="details-bcc">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails BCC</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for bcc in results.resultats_bcc %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if bcc.status|lower == 'en panne' or bcc.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ bcc.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if bcc.status|lower == 'en panne' or bcc.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ bcc.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte MAINT -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="status-card-modern">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold text-dark">MAINT</h6>
                                        <span class="status-indicator {% if results.maintenance_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.maintenance_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{maintenance_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-maintenance">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails MAINT - Conservation de la structure originale -->
                            <div class="collapse mt-3" id="details-maintenance">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails Maintenance</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for maintenance in results.resultats_maintenance %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if maintenance.status|lower == 'en panne' or maintenance.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ maintenance.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if maintenance.status|lower == 'en panne' or maintenance.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ maintenance.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carte SIM -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="status-card-modern">
                                <div class="card-body text-center p-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <h6 class="card-title mb-0 fw-bold text-dark">SIM</h6>
                                        <span class="status-indicator {% if results.simulateur_statut == 'EN PANNE' %}danger{% else %}success{% endif %}"></span>
                                    </div>
                                    <div class="display-6 fw-bold {% if results.simulateur_statut == 'EN PANNE' %}text-danger{% else %}text-success{% endif %} mb-2">
                                        {{simulateur_pourcentage}}%
                                    </div>
                                    <small class="text-muted">Disponibilité système</small>
                                </div>
                                <div class="card-footer bg-transparent border-0 p-3">
                                    <button class="btn btn-outline-primary btn-sm w-100" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#details-simulateur">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Voir détails
                                    </button>
                                </div>
                            </div>

                            <!-- Détails SIM - Conservation de la structure originale -->
                            <div class="collapse mt-3" id="details-simulateur">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>Détails Simulateur</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        {% for simulateur in results.resultats_simulateur %}
                                            <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator {% if simulateur.status|lower == 'en panne' or simulateur.status|lower == 'panne' %}danger{% else %}success{% endif %} me-2"></span>
                                                    <span class="fw-medium">{{ simulateur.nom }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge {% if simulateur.status|lower == 'en panne' or simulateur.status|lower == 'panne' %}bg-danger{% else %}bg-success{% endif %} me-2">
                                                        {{ simulateur.status }}
                                                    </span>
                                                    <a href="{% url 'ecm' %}" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        {% empty %}
                                            <p class="text-muted text-center mb-0">Aucun résultat disponible.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
        
                    </div>
                </div>
            </div>


            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-shield-alt me-1"></i>
                    Stock
                </div>
                <div class="card-body center">
                    <div class="row justify-content-center">
                        <table id="datatablesSimple" class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Équipement</th>
                                  
                                    <th>N° de produit</th>
                                    <th>Marque</th>
                                    <th>Seuil</th>
                                    <th>Quantité en Stock</th>
                                  
                                    
                                </tr>
                            </thead>
                        
                            <tbody>
                                {% for stock in stock %}
                      
                                    <tr>
                                        <td>{{ stock.equipement.nom }}</td>
                                        <td>{{ stock.equipement.product_numbre }}</td>
                                        <td>{{ stock.equipement.constructeur }}</td>
                                        <td>{{ stock.quantite_critique }}</td>
                                        <td class="
                                        {% if stock.quantite == stock.quantite_critique %}
                                            warning-cell
                                        {% elif stock.quantite < stock.quantite_critique %}
                                            danger-cell
                                        {% endif %}
                                    ">
                                        {{ stock.quantite }}
                                    </td>
                                         
                                        
                                      
                                    </tr> 
                            
                                {% endfor %}
                            </tbody>
                        </table>
        
                        
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-6" style="margin-top: 20px; margin-left: 20px;">
            <div id="calendrier-container">
               
                <!-- Conteneur principal avec Bootstrap -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-calendar me-1"></i>
                        Calendrier
                    </div>
                    <div class="card-body">
                        <div id="calendar"></div>
                    </div>
                </div>
                <!-- Modal pour ajouter un événement -->
                <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="eventModalLabel">Ajouter un Événement</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="eventForm">
                                    <div class="mb-3">
                                        <label for="eventTitle" class="form-label">Titre</label>
                                        <input type="text" class="form-control" id="eventTitle" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="eventDescription" class="form-label">Description</label>
                                        <textarea class="form-control" id="eventDescription"></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" id="saveEvent">Enregistrer</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Modal pour afficher les détails d'un événement -->
                <div class="modal fade" id="eventDetailsModal" tabindex="-1" aria-labelledby="eventDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="eventDetailsModalLabel">Détails de l'Événement</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="eventDetailsModalBody">
                                <!-- Contenu des détails sera inséré ici -->
                            </div>
                            <div class="modal-footer">
                                <a id="predictiveMaintenanceBtn" href="#" class="btn btn-success">Maintenance</a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                <!-- Nouveau bouton pour Maintenance Prédictive -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
 


        
        
    </div>
</div>

 
<script>
 
 
    document.addEventListener('DOMContentLoaded', function () {
        var calendarEl = document.getElementById('calendar');
        // Initialisation du calendrier
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            allDaySlot: true,
            allDayText:'Jour',
            height: 600,
            locale: 'fr', // Configuration de la langue française
            buttonText: {
                today: 'Aujourd\'hui',
                month: 'Mois',
                week: 'Semaine',
                day: 'Jour',
               
                list: 'Liste'
            },
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            columnHeaderFormat: { day: 'numeric', month: '2-digit' }, // Format JJ/MM
            
            selectable: true,
            dayMaxEvents: true, // Afficher "+X événements" si le nombre dépasse la limite
            events: JSON.parse('{{ events | safe }}'), // Injecter les événements depuis Django
            eventClick: function (info) {
                // Récupérer les détails de l'événement cliqué
                var event = info.event;
                var modalBody = document.getElementById('eventDetailsModalBody');
                modalBody.innerHTML = ''; // Réinitialiser le contenu
            
                // Créer le contenu du modal
                var titleElement = document.createElement('p');
                titleElement.innerHTML = `<strong>Instance :</strong> ${event.title}`;
                modalBody.appendChild(titleElement);
            
                if (event.extendedProps && event.extendedProps.description) {
                    var descriptionElement = document.createElement('p');
                    descriptionElement.innerHTML = `<strong>Désignation :</strong> ${event.extendedProps.description}`;
                    modalBody.appendChild(descriptionElement);
                }
                
                var startDateElement = document.createElement('p');
                startDateElement.innerHTML = `<strong>Date:</strong> ${event.start ? event.start.toLocaleDateString() : 'Non spécifiée'}`;
                modalBody.appendChild(startDateElement);
                const startDate = new Date(event.start); // Convertir en objet Date si ce n'est pas déjà fait
                const endDate = new Date(startDate.getTime() + 120 * 60 * 1000);
                if (event.end) {
                    var endDateElement = document.createElement('p');
                    endDateElement.innerHTML = `<strong>Date de fin :</strong> ${endDate.toLocaleString()}`;
                    modalBody.appendChild(endDateElement);
                }   
            
                // Mettre à jour l'URL du bouton "Maintenance Prédictive"
                var predictiveMaintenanceBtn = document.getElementById('predictiveMaintenanceBtn');
                if (event.extendedProps && event.extendedProps.id_instance) {
                    predictiveMaintenanceBtn.href = `/pages/preventivebyId/${event.extendedProps.id_instance}/${event.extendedProps.serial_id }`;
                    predictiveMaintenanceBtn.style.display = 'inline-block'; // Assurez-vous que le bouton est visible
                } else {
                    predictiveMaintenanceBtn.style.display = 'none'; // Masquer le bouton si pas d'ID
                }
            
                // Afficher le modal sans backdrop
                var detailsModal = new bootstrap.Modal(document.getElementById('eventDetailsModal'), {
                    backdrop: false // Désactiver le backdrop
                });
                detailsModal.show();
            },
            eventDidMount: function(info) {
    const eventDate = new Date(info.event.start);
    const today = new Date();

    // Supprimer l’heure pour comparaison juste sur la date
    eventDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const el = info.el;

    if (eventDate < today) {
        // Date dépassée -> rouge
        el.classList.add('bg-danger', 'text-white');
    } else {
        // Date future -> orange
        el.classList.add('bg-warning', 'text-dark');
    }
}
        });
        calendar.render();
    });
</script> 

{% endblock %}
