import csv
from io import TextIOWrapper
from django.contrib import admin
from django.urls import path
from django.shortcuts import render
from django.http import HttpResponseRedirect
from django.apps import apps
from django.db.models import Char<PERSON>ield
from .models import Equipement  # important pour l'enregistrement manuel


class EquipementAdmin(admin.ModelAdmin):
    list_display = ['nom', 'constructeur', 'product_numbre', 'mttr', 'mtbf', 'criticite']
    change_list_template = "admin/equipement_changelist.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('import-csv/', self.import_csv),
        ]
        return custom_urls + urls

    def import_csv(self, request):
        if request.method == "POST":
            csv_file = request.FILES["csv_file"]
            decoded_file = TextIOWrapper(csv_file.file, encoding='utf-8')
            reader = csv.DictReader(decoded_file)
            count = 0
            for row in reader:
                Equipement.objects.create(
                    nom=row.get('nom'),
                    constructeur=row.get('constructeur'),
                    product_numbre=row.get('product_numbre'),
                    date_fin_garantie=row.get('date_fin_garantie') or None,
                    mttr=row.get('mttr') or None,
                    mtbf=row.get('mtbf') or None,
                    criticite=row.get('criticite') or 1,
                    maintenance_planifiee=row.get('maintenance_planifiee') or None,
                    type=row.get('type') or None,
                    periodicite_maintenance=row.get('periodicite_maintenance') or None,
                )
                count += 1
            self.message_user(request, f"{count} équipements importés avec succès ✅")
            return HttpResponseRedirect("../")
        return render(request, "admin/import_csv.html")

# Enregistrement spécifique d'Equipement
admin.site.register(Equipement, EquipementAdmin)


# Remplace 'pages' par le nom exact de ton app
app_models = apps.get_app_config('pages').get_models()

for model in app_models:
    if not admin.site.is_registered(model):
        list_display = [field.name for field in model._meta.fields if field.name != 'id'][:10]
        search_fields = [field.name for field in model._meta.fields if isinstance(field, CharField)]

        # Définir la classe dynamiquement pour éviter que la dernière valeur ne soit appliquée à tous
        admin_class = type(
            f'{model.__name__}AutoAdmin',
            (admin.ModelAdmin,),
            {
                'list_display': list_display,
                'search_fields': search_fields
            }
        )
        admin.site.register(model, admin_class)
