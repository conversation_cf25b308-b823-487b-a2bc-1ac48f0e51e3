{% extends 'base.html' %}
{% load static %}

{% block content %}

<div class="container-fluid px-4">
     <ol class="breadcrumb mb-4" style="margin-top: 10px;">
        <li class="breadcrumb-item">Maintenance</li>
        <li class="breadcrumb-item active"><a href="{% url 'intervention' %}">Interventions</a></li>
    </ol>
 
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            MEC
            <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addData">
                <i class="fa-solid fa-plus"></i>
            </a>
            
        </div>
        <div class="card-body">
            <div class="table-responsive"></div>
            <table id="datatablesSimple" class="table table-bordered">
                <thead>
                    <tr>
                        <th>id</th>
                        <th>Équipement</th>
                        <th>Compagnie</th>
                        <th>Système</th>
                        <th>N° Serie</th>
                        <th>Status</th>
                        <th>Date de la maintenance</th>
                        <th>Technicien</th>
                      <!--  <th>Description</th>--> 
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for maintenance in maintenances %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ maintenance.equipement.nom|default:"Non spécifié" }}</td>
                        <td>{{ maintenance.cie.nom |default:"Non spécifié" }}</td>
                        <td>{{ maintenance.systeme.nom |default:"Non spécifié" }}</td>
                        <td>
                            {% if maintenance.equipement_de_rechange is none %}
                              {{ maintenance.equipementsystemeInstance.numero_serie|default:"Non spécifié" }}
                            {% else %}
                              {{ maintenance.equipementsystemeInstance.numero_serie }} remplacé par {{ maintenance.equipement_de_rechange.numero_serie }}
                            {% endif %}
                          </td>
                       <td>
                        <span class="badge {% if maintenance.statut_equipement == 'EN PANNE' %}bg-danger{% else %}bg-success{% endif %}">
                           
                            {{ maintenance.statut_equipement|default:"Non spécifié" }}
                            
                        </span>
                    </td>

                        <td>{{ maintenance.date_maintenance|default:"Non spécifié" }}</td>
                        <td>{{ maintenance.technicien_nom }}</td>
                        <!-- <td>{{ maintenance.description |default:"Non spécifié" }}</td> -->
                        <td class="d-flex justify-content-center gap-2" style=" text-align: center;">

                            <a href="{% url 'fiche_maintenance' maintenance.id %}" class="btn btn-success btn-sm">
                                <i class="fa-solid fa-eye fa-sm"></i>
                            </a>
                            {% if maintenance.statut_equipement == "EN PANNE" %}
                                <a href="{% url 'correctivebyId' id=maintenance.id %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-wrench fa-sm"></i>
                                </a>
                                {% endif %}
                               
                           
                        </td>
                       
                    </tr>
                    {% endfor %}
                </tbody>
               
            </table>
        </div>
    </div>
</div>


    <div class="modal fade" id="addData" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Déclarer une maintenance</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                
                <div class="modal-body text-center">
                    <div class="d-flex justify-content-center gap-2">
                        <a href="{% url 'declarer_panne' %}" class="btn btn-danger">Panne</a>
                        <a href="{% url 'preventive' %}" class="btn btn-primary">Preventive</a>
                        <a href="{% url 'corrective' %}" class="btn btn-warning"  >Corrective</a>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script>
        const addModal = document.getElementById('addData');
        addModal.addEventListener('show.bs.modal', function () {
            document.getElementById('addForm').reset(); // Réinitialiser le formulaire
        });
        
    </script>
{% endblock %}