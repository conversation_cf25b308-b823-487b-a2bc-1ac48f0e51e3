{% extends 'base.html' %}
{% load static %}

{% block content %}

<style>
    /* Cacher les colonnes via CSS */
    .hidden-column {
        display: none;
    }
</style>
 

<div class="container mt-4">
<h2>Tableau Dynamique avec Sélection des Colonnes</h2>

<!-- Sélecteur de colonnes -->
<div class="mb-3">
    <label class="form-label">Sélectionner les colonnes :</label>
    <div id="columnButtons"></div>
</div>

<!-- Tableau -->
<table id="dynamicTable" class="table table-striped">
    <thead>
        <tr id="tableHeader"></tr>
    </thead>
    <tbody></tbody>
</table>
</div>


<script>
    document.addEventListener("DOMContentLoaded", function () {
        let apiData = [
            { "id": 1, "nom": "Alice", "age": 30, "email": "<EMAIL>" },
            { "id": 2, "nom": "<PERSON>", "age": 25, "email": "<EMAIL>" },
            { "id": 3, "nom": "<PERSON>", "age": 35, "email": "<EMAIL>" }
        ];
    
        let columns = Object.keys(apiData[0]); 
    
        // Générer l'en-tête du tableau
        let tableHeader = document.getElementById("tableHeader");
        columns.forEach(col => {
            let th = document.createElement("th");
            th.textContent = col.charAt(0).toUpperCase() + col.slice(1);
            tableHeader.appendChild(th);
        });
    
        // Générer le corps du tableau
        let tbody = document.querySelector("#dynamicTable tbody");
        apiData.forEach(row => {
            let tr = document.createElement("tr");
            columns.forEach(col => {
                let td = document.createElement("td");
                td.textContent = row[col];
                tr.appendChild(td);
            });
            tbody.appendChild(tr);
        });
    
        let table = new simpleDatatables.DataTable("#dynamicTable");
    
        // Générer les boutons pour chaque colonne
        let columnButtons = document.getElementById("columnButtons");
        let columnStates = {}; // Stocke l'état de visibilité des colonnes
    
        columns.forEach((col, index) => {
            let button = document.createElement("button");
            button.textContent = col.charAt(0).toUpperCase() + col.slice(1);
            button.classList.add("btn", "btn-primary", "m-1", "active"); // Toutes les colonnes sont actives au début
            button.dataset.index = index;
            columnStates[index] = true; // Toutes les colonnes sont visibles au départ
    
            button.onclick = function () {
                let columnCells = document.querySelectorAll(`#dynamicTable tbody tr td:nth-child(${index + 1}), #dynamicTable thead tr th:nth-child(${index + 1})`);
                
                if (columnStates[index]) {
                    columnCells.forEach(cell => cell.classList.add("hidden-column"));
                    button.classList.remove("btn-primary", "active");
                    button.classList.add("btn-outline-primary");
                } else {
                    columnCells.forEach(cell => cell.classList.remove("hidden-column"));
                    button.classList.add("btn-primary", "active");
                    button.classList.remove("btn-outline-primary");
                }
    
                columnStates[index] = !columnStates[index]; // Inverser l'état de la colonne
            };
    
            columnButtons.appendChild(button);
        });
    });
    
</script>

 
{% endblock %}