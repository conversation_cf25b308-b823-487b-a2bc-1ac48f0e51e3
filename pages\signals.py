from django.db.models.signals import post_save
from django.dispatch import receiver
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from .models import Maintenance, EquipementStockInstance, StockHasEquipement, Notification

# Quand une nouvelle maintenance est déclarée
@receiver(post_save, sender=Maintenance)
def notifier_panne(sender, instance, created, **kwargs):
    """
    Génère des notifications lorsqu'une nouvelle maintenance est créée.
    """
    if created:  # Vérifie si l'objet vient d'être créé
        notification_type = None
        message = ""

        if instance.type_maintenance == "PANNE":
            notification_type = "PANNE"
            message = f"Panne déclarée: {instance.equipement.nom}/{instance.systeme.nom}"
        elif instance.type_maintenance == "CORRECTIVE":
            notification_type = "Maintenance corrective"
            message = f"Maintenance corrective de {instance.equipement.nom}/{instance.systeme.nom}."
        elif instance.type_maintenance == "PREVENTIVE":
            notification_type = "Maintenance préventive"
            message = f"Maintenance préventive de {instance.equipement.nom}/{instance.systeme.nom}."

        if notification_type and message:
            Notification.objects.create(
                type_notification=notification_type,
                message=message
            )

# Quand un stock est modifié (EquipementStockInstance)
@receiver(post_save, sender=EquipementStockInstance)
def notifier_changement_stock_instance(sender, instance, **kwargs):
    """
    Génère des notifications lorsque des modifications significatives sont apportées à un stock d'équipement.
    """
    if kwargs.get('created', False) or (hasattr(instance, 'tracker') and 'stock_has_instance' in instance.tracker.changed()):
        Notification.objects.create(
            type_notification="STOCK",
            message=f"Modification du stock : {instance.stock_has_instance.equipement.nom}/{instance.stock_has_instance.stock.unite}."
        )

# Quand un équipement dans le stock est modifié (StockHasEquipement)
@receiver(post_save, sender=StockHasEquipement)
def notifier_changement_stock_equipement(sender, instance, **kwargs):
    """
    Génère des notifications lorsque la quantité d'un équipement dans le stock change.
    """
    if kwargs.get('created', False) or (hasattr(instance, 'tracker') and 'quantite' in instance.tracker.changed()):
        Notification.objects.create(
            type_notification="STOCK",
            message=f"Changement de stock : {instance.equipement.nom} - Nouvelle quantité : {instance.quantite}."
        )


@csrf_exempt  # Utilisez ceci avec prudence en production
def mark_notification_as_read(request, notification_id):
    """
    Marque une notification comme lue.
    """
    if request.method == "POST":
        try:
            # Récupérer la notification
            notif = Notification.objects.get(id=notification_id)
            notif.is_read = True
            notif.save()
            return JsonResponse({"success": True})
        except Notification.DoesNotExist:
            return JsonResponse({"success": False, "error": "Notification non trouvée"})
    return JsonResponse({"success": False, "error": "Méthode non autorisée"})


def fetch_notifications(request):
    """
    Récupère les notifications non lues et supprime les notifications déjà lues.
    """
    # Supprimer les notifications déjà lues
    Notification.objects.filter(is_read=True).delete()

    # Récupérer les notifications non lues
    unread_notifications = Notification.objects.filter(is_read=False).order_by("-id")
    notifications_list = [
        {"id": notif.id, "message": notif.message, "is_read": notif.is_read}
        for notif in unread_notifications
    ]

    return JsonResponse({
        "notifications": notifications_list,
        "count": unread_notifications.count()
    })