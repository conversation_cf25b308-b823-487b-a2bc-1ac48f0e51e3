from django.urls import path
from . import views
from .views import get_equipements ,delete_equipment,export_pdf


urlpatterns = [
    path('index/', views.index,name='index'),
    path('', views.index,name='index'),
    path('about/', views.about,name='about'),
   # path('tables/', views.tables,name='tables'),
    path('get-equipements/', get_equipements, name='get_equipements'),
    path('ect/', views.ect,name='ect'),
    path('bst/', views.bst,name='bst'),
    path('bge/', views.bge,name='bge'),
    path('allstock/', views.get_equipements,name='allstock'),
    path('systeme/<str:id>/', views.systeme,name='systeme'),
    path('ecm/', views.ecm,name='ecm'),
    path('cc/', views.cc,name='cc'),
    path('maintenance/', views.maintenance,name='maintenance'),
    path('intervention/', views.intervention,name='intervention'),
    path('mission/', views.mission,name='mission'),
    path('kpi/', views.kpi,name='kpi'),
    path('edit-equipment/', views.edit_equipment, name='edit_equipment'),
    path('delete_equipment/<int:instance_id>/', delete_equipment, name='delete_equipment'),
    path('export-pdf/', export_pdf, name='export_pdf'),
     
 path('fiche_maint/', views.fiche_maint ,name='fiche_maint'),
    path('ajouter_quantite_stock/', views.ajouter_quantite_stock ,name='ajouter_quantite_stock'),
    path('ajouter_equipement/', views.ajouter_equipement, name='ajouter_equipement'),
    path('fiche_maintenance/<int:id>/', views.fiche_maintenance, name='fiche_maintenance'),

path('correctivebyId/<int:id>/', views.correctivebyId, name='correctivebyId'),
    
       path('fiche_equipement/<int:id1>/<int:id2>/', views.fiche_equipement, name='fiche_equipement'),

    path('preventive/', views.preventive, name='preventive'),
     path('corrective/', views.corrective, name='corrective'),
    
      path('declarer_panne/', views.declarer_panne, name='declarer_panne'),
    path('fetch_notifications/', views.fetch_notifications, name='fetch_notifications'),
    
    path('mark_notification_as_read/<int:notification_id>/', views.mark_notification_as_read, name='mark_notification_as_read'),
     path('get_equipements/', views.get_equipements, name='get_equipements'),
      path('personnel/', views.personnel, name='personnel'),
      path('panne/', views.panne, name='panne'),
       path('simulateur/', views.simulateur, name='simulateur'),
       path('fiche_personnel/<str:id>/', views.fiche_personnel, name='fiche_personnel'),
        path('interventions_par_mois/<int:id>/', views. interventions_par_mois, name=' interventions_par_mois'),
       
     path('fiche_systeme/', views.fiche_systeme, name='fiche_systeme'),
     path('historique_stock/', views.historique_stock, name='historique_stock'),

     path('preventivebyId/<int:id1>/<str:id2>/', views.preventivebyId, name='preventivebyId'),
     path('fiche_suivie_reparation/<int:id>/', views.fiche_suivie_reparation, name='fiche_suivie_reparation'),
     path('create_maintenance_history/<int:id>/', views.create_maintenance_history, name='create_maintenance_history'),
      path('fiche_mission/<int:id>/', views.fiche_mission, name='fiche_mission'),
        path('esmbyid/<int:id>/', views.esmbyid, name='esmbyid'),
   path('addpersonnel/', views.addpersonnel, name='addpersonnel'),
     path('edit_personnel/', views.edit_personnel, name='edit_personnel'),
         path('delete_personnel/<str:matricule>/', views.delete_personnel, name='delete_personnel'),
            path('get_personnel/', views.get_personnel, name='get_personnel'),
             path('addmission/', views.addmission, name='addmission'),
             path('get-systemes-by-cies/', views.get_systemes_by_cies, name='get_systemes_by_cies'),

            
    
   
        
 
 
    

]
 
 